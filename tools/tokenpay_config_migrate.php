<?php
@header('Content-Type: text/plain; charset=UTF-8');
// 加载系统公共文件，获取数据库连接与配置
include_once(__DIR__ . '/../confing/common.php');

// 需要的配置键与默认值
$need = [
    'tokenpay_enabled' => '0',
    'tokenpay_gateway' => '',
    'tokenpay_webhook_secret' => ''
];

$inserted = [];
$errors = [];

foreach ($need as $key => $defVal) {
    // 判断配置项是否存在
    $exists = $DB->get_row("SELECT v FROM qingka_wangke_config WHERE v='" . daddslashes($key) . "' LIMIT 1");
    if (!$exists) {
        // 不存在则插入默认值
        $sql = "INSERT INTO qingka_wangke_config(v,k) VALUES ('".$DB->escape($key)."','".$DB->escape($defVal)."')";
        if ($DB->query($sql)) {
            $inserted[] = $key;
        } else {
            $errors[] = $key . ' 插入失败：' . $DB->error();
        }
    }
}

if (!empty($inserted)) {
    echo "已插入配置键：" . implode(', ', $inserted) . "\n";
} else {
    echo "无需插入，配置已存在\n";
}

if (!empty($errors)) {
    echo "错误：\n" . implode("\n", $errors) . "\n";
}

echo "完成\n"; 