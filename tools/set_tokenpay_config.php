<?php
@header('Content-Type: text/plain; charset=UTF-8');
include_once(__DIR__ . '/../confing/common.php');

$kv = [
  'tokenpay_enabled' => '1',
  'tokenpay_gateway' => 'https://tokenpay.freedomp.icu',
  // 与 TokenPay appsettings.json 中的 ApiToken 保持一致
  'tokenpay_webhook_secret' => 'pqo24uw3ja765skfhi42ah4654',
];

foreach ($kv as $k => $v) {
  $exists = $DB->get_row("SELECT v FROM qingka_wangke_config WHERE v='".daddslashes($k)."' LIMIT 1");
  if ($exists) {
    $DB->query("UPDATE qingka_wangke_config SET k='".$DB->escape($v)."' WHERE v='".$DB->escape($k)."'");
    echo "更新：$k=$v\n";
  } else {
    $DB->query("INSERT INTO qingka_wangke_config(v,k) VALUES ('".$DB->escape($k)."','".$DB->escape($v)."')");
    echo "插入：$k=$v\n";
  }
}

echo "完成\n"; 