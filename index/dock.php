<?php
$mod='blank';
$title='平台对接';
$ym="freedomp.icu";
require_once('head.php');
$fl=$DB->count("select addprice from qingka_wangke_user where uid='{$userrow['uid']}'");
$ck=$DB->count("SELECT count(id) FROM `qingka_wangke_log` WHERE type='API查课'AND uid='{$userrow['uid']}' ");
$xd = $DB->count("SELECT count(id) FROM `qingka_wangke_log` WHERE type='API添加任务' AND uid='{$userrow['uid']}' ");
$xdbxz=$conf['api_proportion'];
$xdb=round($xd/$ck,4)*100;
?>
<style>
.interface-section {
    margin-bottom: 30px;
}

.interface-title {
    color: #333;
    margin-bottom: 20px;
}

.interface-divider {
    border: 0;
    height: 1px;
    background: #ddd;
    margin: 30px 0;
}
.custom-close-btn:hover {
    color: #333 !important;
    transform: scale(1.1);
    transition: all 0.3s ease;
}

  .td-wrap {
    white-space: normal; /* 允许内容自动换行 */
    max-width: 200px; /* 最大宽度 */
    word-wrap: break-word; /* 允许在单词中间断行 */
  }
/* 资料下载手风琴样式 */
.download-accordion {
  max-width: 100%;
  margin: 0;
}

.download-panel {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  overflow: hidden;
}

.download-panel:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  border-color: rgba(0, 123, 255, 0.2);
}

.download-panel:last-child {
  margin-bottom: 0;
}

/* 手风琴头部样式 */
.panel-header {
  padding: 20px 24px;
  cursor: pointer;
  background: #ffffff;
  border: none;
  width: 100%;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s ease;
}

.panel-header:hover {
  background: #f8f9fa;
}

.panel-header:focus {
  outline: none;
  background: #f8f9fa;
}

.panel-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.panel-subtitle {
  font-size: 14px;
  color: #6c757d;
  margin: 4px 0 0 0;
}

.panel-toggle {
  font-size: 20px;
  color: #007bff;
  transition: transform 0.3s ease;
}

.panel-toggle.collapsed {
  transform: rotate(0deg);
}

.panel-toggle.expanded {
  transform: rotate(180deg);
}

/* 手风琴内容样式 */
.panel-content {
  padding: 0 24px 24px 24px;
  background: #ffffff;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.panel-body {
  padding-top: 20px;
}

.content-description {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #007bff;
  margin-bottom: 20px;
}

.content-description h6 {
  color: #007bff;
  font-weight: 600;
  margin-bottom: 12px;
  font-size: 14px;
}

/* 按钮样式优化 */
.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  border-radius: 8px;
  color: #fff;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
  min-width: 120px;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

/* 文本样式优化 */
.card-title {
  font-weight: 700;
  font-size: 20px;
  color: #1a1a1a;
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.card-text {
  font-size: 14px;
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 16px;
}

.card-description {
  font-size: 13px;
  color: #495057;
  line-height: 1.7;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #007bff;
  margin: 16px 0;
}

.card-description h6 {
  color: #007bff;
  font-weight: 600;
  margin-bottom: 12px;
  font-size: 14px;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 12px 0;
}

.feature-list li {
  padding: 4px 0;
  position: relative;
  padding-left: 20px;
}

.feature-list li:before {
  content: "✓";
  color: #28a745;
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* 响应式布局优化 */
@media (max-width: 768px) {
  .panel-header {
    padding: 16px 20px;
  }

  .panel-title {
    font-size: 16px;
  }

  .panel-subtitle {
    font-size: 13px;
  }

  .panel-content {
    padding: 0 20px 20px 20px;
  }
}

@media (max-width: 480px) {
  .panel-header {
    padding: 14px 16px;
  }

  .panel-content {
    padding: 0 16px 16px 16px;
  }

  .btn-primary {
    padding: 10px 20px;
    font-size: 13px;
    width: 100%;
  }
}


</style>
     <div class="app-content-body ">
        <div class="wrapper-md control">
        <div class="layui-row layui-col-space8 layui-anim layui-anim-upbit">
        <div  class="card" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
            <ul class="nav nav-tabs" role="tablist">
            <li class="active">
              <a data-toggle="tab" href="#ck">查课接口</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#xd">下单接口</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#bs">补刷接口</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#jd">同步接口</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#gm">改密接口</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#ik">独家项目接口</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#kcid">商品信息</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#flid">分类信息</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#xx">我的信息</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#xz">资料下载</a>
            </li>
          </ul>
            <div class="tab-content">
                <div class="tab-pane fade active in" id="ck">
                    <pre><h5>POST网址:</h5>https://<?echo($ym);?>/api.php?act=get</pre>
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                        <tr>
                                            <th class="text-center" style="width:100px">请求参数</th>
                                            <th>
                                                说明<br>
                                            </th>
                                            <th class="text-center" style="width:100px">
                                                传输类型<br>
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <tr>
                                                <th class="text-center" scope="row">uid</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">key</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">platform</th>
                                                <td><code>平台ID</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
    
                                            <tr>
                                                <th class="text-center" scope="row">user</th>
                                                <td><code>学生账号</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">pass</th>
                                                <td><code>学生密码</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                             <tr>
                                                <th class="text-center" scope="row">school</th>
                                                <td><code>学生学校</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                        </tr>
                                        </tbody>
                                    </table>
                    <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                    <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                    <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/xdjk.php 文件</h5></pre>
                    <pre class="layui-code code-demo" lay-title="29查课对接代码" lay-options="{}"><h4>29对接代码：</h4><h5>//FD平台查课接口 复制代码放在/Checkorder/ckjk.php 文件
                    
                        else if ($type == "FD")
                        {
                            $data = array("uid" => $a["user"], "key" => $a["pass"], "school" => $school, "user" => $user, "pass" => $pass, "platform" => $noun, "kcid" => $kcid);
                            $dx_rl = $a["url"];
                            $dx_url = "$dx_rl/api.php?act=get";
                            $result = get_url($dx_url, $data);
                            $result = json_decode($result, true);
                            return $result;
                        }</h5></pre>
                </div>
                <div class="tab-pane fade" id="xd">
                 <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=add</pre>
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                            <tr>
                                                <th class="text-center" style="width:100px">请求参数</th>
                                                <th>
                                                    说明<br>
                                                </th>
                                                <th class="text-center" style="width:100px">
                                                    传输类型<br>
                                                </th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <th class="text-center" scope="row">uid</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">key</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">platform</th>
                                                <td><code>平台ID</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
    
                                            <tr>
                                                <th class="text-center" scope="row">user</th>
                                                <td><code>学生账号</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">pass</th>
                                                <td><code>账号密码</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">kcname</th>
                                                <td><code>课程名字</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">kcid</th>
                                                <td><code>课程ID</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            </tbody>
                                        </table>
                 <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                 <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                    <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/xdjk.php 文件</h5></pre>
                    <pre class="layui-code code-demo" lay-title="29下单对接代码" lay-options="{}"><h4>29对接代码：</h4><h5>//FD平台下单接口 复制代码放在/Checkorder/xdjk.php 文件
                    
                        else if ($type == "FD") 
                        {
                            $data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $noun, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "kcid" => $kcid);
                            $dx_rl = $a["url"];
                            $dx_url = "$dx_rl/api.php?act=add";
                            $result = get_url($dx_url, $data);
                            $result = json_decode($result, true);
                            if ($result["code"] == "0") {
                                $b = array("code" => 1, "msg" => "下单成功",'yid'=>$result['id']);
                            } else {
                                $b = array("code" => - 1, "msg" => $result["msg"]);
                        }
                        return $b;
                        }</h5></pre>
                </div>
                <div class="tab-pane fade" id="bs">
                    <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=budan</pre>
                        <table class="table table-bordered table-striped">
                                        <thead>
                                        <tr>
                                            <th class="text-center" style="width:100px">请求参数</th>
                                            <th>
                                                说明<br>
                                            </th>
                                            <th class="text-center" style="width:100px">
                                                传输类型<br>
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <th class="text-center" scope="row">uid</th>
                                            <td><code>登录验证</code></td>
                                            <td class="text-center"><code>必传</code></td>
                                        </tr>
                                        <tr>
                                            <th class="text-center" scope="row">key</th>
                                            <td><code>登录验证</code></td>
                                            <td class="text-center"><code>必传</code></td>
                                            </tr>
                                        <tr>
                                            <th class="text-center" scope="row">id</th>
                                            <td><code>订单账号</code></td>
                                            <td class="text-center"><code>必传</code></td>
                                        </tr>
                                        </tbody>
                                    </table>
                    <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                    <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                    <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/xdjk.php 文件</h5></pre>
                    <pre class="layui-code code-demo" lay-title="29补刷对接代码" lay-options="{}"><h4>29对接代码：</h4><h5>//FD平台补刷接口 复制代码放在/Checkorder/bsjk.php 文件
                    
                        elseif ($type == "FD") 
                        {
                            $data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
                            $dx_rl = $a["url"];
                            $dx_url = "$dx_rl/api.php?act=budan";
                            $result = get_url($dx_url, $data);
                            $result = json_decode($result, true);
                            return $result;
                        }</h5></pre>
                </div>
                <div class="tab-pane fade" id="jd">
                    <pre><h5>POST:（下面使用的是实时进度接口chadanoid，如遇到问题可以更换为默认的进度接口chadanmr，记到传yid，两个接口均不会串进度！）</h5>https://<?echo($ym);?>/api.php?act=chadanoid</pre>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width:100px">请求参数</th>
                                    <th>
                                        说明<br>
                                    </th>
                                    <th class="text-center" style="width:100px">
                                         传输类型<br>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                            <tr>
                                                <th class="text-center" scope="row">uid</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">key</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">yid</th>
                                                <td><code>订单id</code></td>
                                                <td class="text-center"><code>可选</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">username</th>
                                                <td><code>订单账号</code></td>
                                                <td class="text-center"><code>可选</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">school</th>
                                                <td><code>订单学校</code></td>
                                                <td class="text-center"><code>可选</code></td>
                                            </tr>
                                            </tbody>
                                        </table>
                    <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                    <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                    <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/xdjk.php 文件</h5></pre>
                    <pre class="layui-code code-demo" lay-title="29进度对接代码" lay-options="{}"><h4>29进度对接代码（支持yid和账号查询）：</h4><h5>//FD平台进度接口 复制代码放在/Checkorder/jdjk.php 文件
                    
        else if ($type == "FD") {
            $data = array("username" => $user,"yid"=>$d["yid"],"uid" => $a["user"],"key" => $a["pass"]);//$d["yid"]为订单yid，如果没有自行在jdjk.php文件顶部添加该参数！
            $dx_rl = $a["url"];
            $dx_url  = "$dx_rl/api.php?act=chadanoid";
            $result = get_url($dx_url, $data);
            $result = json_decode($result, true);
            if ($result["code"] == "1") {
                foreach ($result["data"] as $res) {
                $yid = $res["id"];
                $kcname = $res["kcname"];
                $status = $res["status"];
                $process = $res["process"];
                $remarks = $res["remarks"];
                $kcks = $res["courseStartTime"];
                $kcjs = $res["courseEndTime"];
                $ksks = $res["examStartTime"];
                $ksjs = $res["examEndTime"];
                $b[] = array("code" => 1, "msg" => "查询成功", "yid" => $yid, "kcname" => $kcname, "user" => $user, "pass" => $pass, "ksks" => $ksks, "ksjs" => $ksjs, "status_text" => $status, "process" => $process, "remarks" => $remarks);
                }
            } else {
                $b[] = array("code" => -1, "msg" => $result);
            }
            return $b;
        }</h5></pre>
                </div>
                <div class="tab-pane fade" id="ik">
                    <!-- 改密接口 -->
                    <div class="interface-section">
                        <h3 class="interface-title">改密接口</h3>
                        <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=xgmm</pre>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width:100px">请求参数</th>
                                    <th>说明</th>
                                    <th class="text-center" style="width:100px">传输类型</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th class="text-center" scope="row">uid</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">key</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">yid</th>
                                    <td><code>订单id</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">pwd</th>
                                    <td><code>新密码</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                            </tbody>
                        </table>
                        <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                        <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                        <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/gmjk.php 文件</h5></pre>
                        <pre class="layui-code code-demo" lay-title="29进度对接代码" lay-options="{}"><h4>29改密对接代码：</h4><h5>//FD平台改密接口 复制代码放在/Checkorder/gmjk.php 文件
                
                elseif ($type == "FD") {
                    $data = array("uid" => $a["user"], "key" => $a["pass"], "oid" => $yid,'pwd' =>$newpass);
                    $dx_rl = $a["url"];
                    $dx_url = "$dx_rl/api.php?act=xgmm";
                    $result = get_url($dx_url, $data);
                    $result = json_decode($result, true);
                    return $result;
                }</h5></pre>
                    </div>
                    <hr class="interface-divider">
                
                    <!-- 停止接口 -->
                    <div class="interface-section">
                        <h3 class="interface-title">停止接口</h3>
                        <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=iktz</pre>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width:100px">请求参数</th>
                                    <th>说明</th>
                                    <th class="text-center" style="width:100px">传输类型</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th class="text-center" scope="row">uid</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">key</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">yid</th>
                                    <td><code>订单id</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                            </tbody>
                        </table>
                        <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                        <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                        <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/stopjk.php 文件</h5></pre>
                        <pre class="layui-code code-demo" lay-title="对接提示" lay-options="{}"><h4>独家项目停止跑单对接代码：</h4><h5>传递订单ID接口即可，自行开发</h5></pre>
                    </div>
                    <hr class="interface-divider">
                
                    <!-- 转秒接口 -->
                    <div class="interface-section">
                        <h3 class="interface-title">转秒接口</h3>
                        <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=ikzm</pre>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width:100px">请求参数</th>
                                    <th>说明</th>
                                    <th class="text-center" style="width:100px">传输类型</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th class="text-center" scope="row">uid</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">key</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">yid</th>
                                    <td><code>订单id</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                            </tbody>
                        </table>
                        <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                        <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                        <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4></pre>
                        <pre class="layui-code code-demo" lay-title="29进度对接代码" lay-options="{}"><h4>独家项目转秒对接代码：</h4><h5>传递订单ID接口即可，自行开发</h5></pre>
                    </div>
                    <hr class="interface-divider">
                
                    <!-- 立即考试接口 -->
                    <div class="interface-section">
                        <h3 class="interface-title">立即考试接口</h3>
                        <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=ikks</pre>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width:100px">请求参数</th>
                                    <th>说明</th>
                                    <th class="text-center" style="width:100px">传输类型</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th class="text-center" scope="row">uid</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">key</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">yid</th>
                                    <td><code>订单id</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                            </tbody>
                        </table>
                        <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                        <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                        <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4></pre>
                        <pre class="layui-code code-demo" lay-title="独家项目立即考试对接代码" lay-options="{}"><h4>29立即考试对接代码：</h4><h5>传递订单ID接口即可，自行开发</h5></pre>
                    </div>
                </div>
                <div class="tab-pane fade" id="gm">
                    <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=xgmm</pre>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width:100px">请求参数</th>
                                    <th>
                                        说明<br>
                                    </th>
                                    <th class="text-center" style="width:100px">
                                         传输类型<br>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                            <tr>
                                                <th class="text-center" scope="row">uid</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">key</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">yid</th>
                                                <td><code>订单id</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">pwd</th>
                                                <td><code>新密码</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            </tbody>
                                        </table>
                    <pre><h4>29系统对接网址</h4>https://<?echo($_SERVER['SERVER_NAME']);?></pre>
                    <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                    <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/gmjk.php 文件</h5></pre>
                    <pre class="layui-code code-demo" lay-title="29进度对接代码" lay-options="{}"><h4>29改密对接代码：</h4><h5>//FD平台改密接口 复制代码放在/Checkorder/gmjk.php 文件
                    
        elseif ($type == "FD") {
            $data = array("uid" => $a["user"], "key" => $a["pass"], "oid" => $yid,'pwd' =>$newpass);
            $dx_rl = $a["url"];
            $dx_url = "$dx_rl/api.php?act=xgmm";
            $result = get_url($dx_url, $data);
            $result = json_decode($result, true);
            return $result;
            }</h5></pre>
                </div>
                <div class="tab-pane fade" id="kcid">
                    <div class="modal-content">
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索商品名称..." onkeyup="searchTable()">
                        <div class="table-responsive"> 
                            <table class="table table-striped" id="dataTable">
                                <thead>
                                    <tr>
                                        <th>对接ID</th>
                                        <th>分类id</th>
                                        <th>商品名称</th>
                                        <th>你的价格（<?php echo $userrow['addprice']?>）</th>
                                        <th>0.2</th>
                                        <th>0.3</th>
                                        <th>0.4</th>
                                        <th>0.5</th>
                                        <th>0.6</th>
                                        <th>0.8</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                        $query = "select * from qingka_wangke_class where status=1";
                                        $result = $DB->query($query);
                                    
                                        while ($row = $DB->fetch($result)) {
                                            echo "<tr>";
                                            echo "<td>".$row['cid']."</td>";
                                            echo "<td>".$row['fenlei']."</td>";
                                            echo "<td class=\"td-wrap\">".htmlspecialchars($row['name'])."</td>";
                                            echo "<td>".$row['price'] * $fl."</td>";
                                            echo "<td>".$row['price']*0.2."</td>";
                                            echo "<td>".$row['price']*0.3."</td>";
                                            echo "<td>".$row['price']*0.4."</td>";
                                            echo "<td>".$row['price']*0.5."</td>";
                                            echo "<td>".$row['price']*0.6."</td>";
                                            echo "<td>".$row['price']*0.8."</td>";
                                            echo "</tr>";
                                        }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="flid">
                <div class="modal-content">
                    <div class="table-responsive"> 
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>分类ID</th>
                                    <th>分类名称</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                    $query = "select * from qingka_wangke_fenlei where status!=0";
                                    $result = $DB->query($query);
    
                                    while ($row = $DB->fetch($result)) {
                                        echo "<tr>";
                                        echo "<td>".$row['id']."</td>";
                                        echo "<td>".$row['name']."</td>";
                                        echo "</tr>";
                                    }
                                ?>
                        </tbody>
                        </table>
                    </div>
                </div>
                </div>
                <div class="tab-pane fade" id="xx">
                    <div class="modal-content">
                        <div class="table-responsive"> 
    						<table class="table table-striped">
    							<thead>
    							    <tr>
    							        <th>对接ID</th>
    							        <th>对接KEY</th>
    							        <th>下单比限制</th>
    							        <th>当前API下单比</th>
    							    </tr>
    							</thead>
    								<tbody>
    									<?php 
    										$a=$DB->query("select * from qingka_wangke_user where uid='{$userrow['uid']}'");
    										
    										while($rs=$DB->fetch($a)){
    										    if($rs['key']==0){
    										    $rs['key']="未开通KEY";
    										    echo "<tr><td>".$rs['uid']."</td><td>".$rs['key']."</td><td>".$xdbxz."%</td><td>".$xdb."%</td></tr>"; 
    										    }else{
    										        echo "<tr><td>".$rs['uid']."</td><td>".$rs['key']."</td><td>".$xdbxz."%</td><td>".$xdb."%</td></tr>"; 
    										    }
    										}
    									?>
                                	</tbody>
    						</table>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="xz">
                    <div style="padding: 20px 0;">
                        <h3 style="color: #1a1a1a; font-weight: 700; margin-bottom: 8px;">资料下载</h3>
                        <p style="color: #6c757d; margin-bottom: 24px;">对接插件下载</p>
                    </div>
                    <div class="download-accordion">
                        <div class="download-panel">
                          <button class="panel-header" onclick="togglePanel('panel1')">
                            <div>
                              <div class="panel-title">FreeDom模板</div>
                              <div class="panel-subtitle">余额不低于1000可下载 - 完整的网站模板系统</div>
                            </div>
                            <span class="panel-toggle collapsed" id="toggle1">▼</span>
                          </button>
                          <div class="panel-content" id="panel1" style="display: none;">
                            <div class="panel-body">
                              <div class="content-description">
                                <h6>最新更新</h6>
                                <ul class="feature-list">
                                  <li>5.28日更新：加入接单商城</li>
                                  <li>数据库字段增加，老模板无法直接替换</li>
                                  <li>代理管理界面加入跨户权限修改</li>
                                  <li>下单页加入勾选课程数量提示</li>
                                </ul>

                                <h6 style="margin-top: 20px;">核心功能</h6>
                                <ul class="feature-list">
                                  <li>全新公告系统：暗网同款，独家编辑界面</li>
                                  <li>首页登录免责申明：暗网同款</li>
                                  <li>微信/邮件发信系统：支持异常提醒</li>
                                  <li>独创货源管理系统：一键对接，批量管理</li>
                                  <li>密价管理系统，界面美化</li>
                                  <li>批量补刷与同步文件</li>
                                  <li>同款交单页面，订单页面，代理管理界面</li>
                                </ul>

                                <p style="margin-top: 16px; font-style: italic;">代码开源，绝大部分代码均来自于互联网，欢迎交流使用。</p>
                              </div>
                              <div style="text-align: right; margin-top: 20px;">
                                <?php if ($userrow['money'] >= 1000&&$userrow['addprice'] == 0.2) { ?>
                                  <button onclick="download(1)" class="btn btn-primary">立即下载</button>
                                <?php } else { ?>
                                  <div class="alert alert-warning" style="margin: 0; padding: 12px; border-radius: 8px; text-align: left;">
                                    余额至少1000且等级为顶级才能下载此文件！
                                  </div>
                                <?php } ?>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="download-panel">
                          <button class="panel-header" onclick="togglePanel('panel2')">
                            <div>
                              <div class="panel-title">鲸鱼运动无限套娃对接套件</div>
                              <div class="panel-subtitle">累计充值不低于500可下载 - 无限套娃对接</div>
                            </div>
                            <span class="panel-toggle collapsed" id="toggle2">▼</span>
                          </button>
                          <div class="panel-content" id="panel2" style="display: none;">
                            <div class="panel-body">
                              <div class="content-description">
                                <h6>产品特色</h6>
                                <ul class="feature-list">
                                  <li>可无限向下对接，无限二开让代理对接您</li>
                                  <li>本插件为免费试用，无任何收费</li>
                                  <li>恢复源台体验，功能完整</li>
                                  <li>傻瓜式配置，填写uid与key即可一键对接</li>
                                </ul>

                                <h6 style="margin-top: 20px;">使用说明</h6>
                                <p>如出现页面大小不正常，请打开单页代码删除 {class="lyear-layout-content"} 即可</p>
                                <p>余额不低于500，且顶级费率，刷新本页可见下载按钮！</p>
                              </div>
                              <div style="text-align: right; margin-top: 20px;">
                                <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                  <button onclick="download(2)" class="btn btn-primary">立即下载</button>
                                <?php } else { ?>
                                  <div class="alert alert-warning" style="margin: 0; padding: 12px; border-radius: 8px; text-align: left;">
                                    您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！
                                  </div>
                                <?php } ?>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="download-panel">
                          <button class="panel-header" onclick="togglePanel('panel3')">
                            <div>
                              <div class="panel-title">雷电运动无限套娃对接套件</div>
                              <div class="panel-subtitle">累计充值不低于500可下载 - 无限套娃对接</div>
                            </div>
                            <span class="panel-toggle collapsed" id="toggle3">▼</span>
                          </button>
                          <div class="panel-content" id="panel3" style="display: none;">
                            <div class="panel-body">
                              <div class="content-description">
                                <h6>产品特色</h6>
                                <ul class="feature-list">
                                  <li>可无限向下对接，无限二开让代理对接您</li>
                                  <li>本插件为免费试用，无任何收费</li>
                                  <li>恢复源台体验，功能完整</li>
                                  <li>傻瓜式配置，填写uid与key即可一键对接</li>
                                </ul>

                                <h6 style="margin-top: 20px;">使用说明</h6>
                                <p>如出现页面大小不正常，请打开单页代码删除 {class="lyear-layout-content"} 即可</p>
                                <p>余额不低于500，且顶级费率，刷新本页可见下载按钮！</p>
                              </div>
                              <div style="text-align: right; margin-top: 20px;">
                                <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                  <button onclick="download(4)" class="btn btn-primary">立即下载</button>
                                <?php } else { ?>
                                  <div class="alert alert-warning" style="margin: 0; padding: 12px; border-radius: 8px; text-align: left;">
                                    您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！
                                  </div>
                                <?php } ?>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="download-panel">
                          <button class="panel-header" onclick="togglePanel('panel4')">
                            <div>
                              <div class="panel-title">杀手运动无限套娃对接套件</div>
                              <div class="panel-subtitle">累计充值不低于500可下载 - 无限套娃对接</div>
                            </div>
                            <span class="panel-toggle collapsed" id="toggle4">▼</span>
                          </button>
                          <div class="panel-content" id="panel4" style="display: none;">
                            <div class="panel-body">
                              <div class="content-description">
                                <h6>产品特色</h6>
                                <ul class="feature-list">
                                  <li>可无限向下对接，无限二开让代理对接您</li>
                                  <li>本插件为免费试用，无任何收费</li>
                                  <li>恢复源台体验，功能完整</li>
                                  <li>傻瓜式配置，填写uid与key即可一键对接</li>
                                </ul>

                                <h6 style="margin-top: 20px;">使用说明</h6>
                                <p>如出现页面大小不正常，请打开单页代码删除 {class="lyear-layout-content"} 即可</p>
                                <p>余额不低于500，且顶级费率，刷新本页可见下载按钮！</p>
                              </div>
                              <div style="text-align: right; margin-top: 20px;">
                                <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                  <button onclick="download(3)" class="btn btn-primary">立即下载</button>
                                <?php } else { ?>
                                  <div class="alert alert-warning" style="margin: 0; padding: 12px; border-radius: 8px; text-align: left;">
                                    您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！
                                  </div>
                                <?php } ?>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="download-panel">
                          <button class="panel-header" onclick="togglePanel('panel5')">
                            <div>
                              <div class="panel-title">APPUI打卡无限套娃对接套件</div>
                              <div class="panel-subtitle">累计充值不低于500可下载 - 打卡系统对接</div>
                            </div>
                            <span class="panel-toggle collapsed" id="toggle5">▼</span>
                          </button>
                          <div class="panel-content" id="panel5" style="display: none;">
                            <div class="panel-body">
                              <div class="content-description">
                                <h6>产品特色</h6>
                                <ul class="feature-list">
                                  <li>可无限向下对接，无限二开让代理对接您</li>
                                  <li>本插件为免费试用，无任何收费</li>
                                  <li>恢复源台体验，功能完整</li>
                                  <li>傻瓜式配置，填写uid与key即可一键对接</li>
                                </ul>

                                <h6 style="margin-top: 20px;">使用说明</h6>
                                <p>如出现页面大小不正常，请打开单页代码删除 {class="lyear-layout-content"} 即可</p>
                                <p>余额不低于500，且顶级费率，刷新本页可见下载按钮！</p>
                              </div>
                              <div style="text-align: right; margin-top: 20px;">
                                <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                  <button onclick="download(5)" class="btn btn-primary">立即下载</button>
                                <?php } else { ?>
                                  <div class="alert alert-warning" style="margin: 0; padding: 12px; border-radius: 8px; text-align: left;">
                                    您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！
                                  </div>
                                <?php } ?>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="download-panel">
                          <button class="panel-header" onclick="togglePanel('panel6')">
                            <div>
                              <div class="panel-title">盘古运动无限套娃对接套件</div>
                              <div class="panel-subtitle">累计充值不低于500可下载 - 功能最全面的运动套件</div>
                            </div>
                            <span class="panel-toggle collapsed" id="toggle6">▼</span>
                          </button>
                          <div class="panel-content" id="panel6" style="display: none;">
                            <div class="panel-body">
                              <div class="content-description">
                                <h6>功能说明</h6>
                                <ul class="feature-list">
                                  <li>包含：步道乐跑、乐跑小程序、体适能、keep、云运动</li>
                                  <li>支持用户自助退款</li>
                                  <li>所有项目支持自定义配速</li>
                                  <li>支持用户查看跑步详情、日志</li>
                                  <li>源台支持的功能我们都支持！</li>
                                </ul>

                                <h6 style="margin-top: 20px;">产品特色</h6>
                                <ul class="feature-list">
                                  <li>可无限向下对接，无限二开让代理对接您</li>
                                  <li>本插件为免费试用，无任何收费</li>
                                  <li>恢复源台体验，功能完整</li>
                                </ul>

                                <h6 style="margin-top: 20px;">使用说明</h6>
                                <p>如出现页面大小不正常，请打开单页代码删除 {class="lyear-layout-content"} 即可</p>
                                <p>余额不低于500，且顶级费率，刷新本页可见下载按钮！</p>
                              </div>
                              <div style="text-align: right; margin-top: 20px;">
                                <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                  <button onclick="download(6)" class="btn btn-primary">立即下载</button>
                                <?php } else { ?>
                                  <div class="alert alert-warning" style="margin: 0; padding: 12px; border-radius: 8px; text-align: left;">
                                    您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！
                                  </div>
                                <?php } ?>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="download-panel">
                          <button class="panel-header" onclick="togglePanel('panel7')">
                            <div>
                              <div class="panel-title">FD独家项目对接文档</div>
                              <div class="panel-subtitle">累计充值不低于500可下载 - 改密、暂停、日志对接文档</div>
                            </div>
                            <span class="panel-toggle collapsed" id="toggle7">▼</span>
                          </button>
                          <div class="panel-content" id="panel7" style="display: none;">
                            <div class="panel-body">
                              <div class="content-description">
                                <h6>文档内容</h6>
                                <ul class="feature-list">
                                  <li>独家项目改密接口对接文档</li>
                                  <li>项目暂停功能对接说明</li>
                                  <li>日志查看功能对接指南</li>
                                  <li>完整的API接口说明</li>
                                </ul>

                                <h6 style="margin-top: 20px;">产品特色</h6>
                                <ul class="feature-list">
                                  <li>可无限向下对接，无限二开让代理对接您</li>
                                  <li>本插件为免费试用，无任何收费</li>
                                  <li>详细的对接说明和示例代码</li>
                                </ul>

                                <p style="margin-top: 16px;">余额不低于500，且顶级费率，刷新本页可见下载按钮！</p>
                              </div>
                              <div style="text-align: right; margin-top: 20px;">
                                <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                  <button onclick="download(7)" class="btn btn-primary">立即下载</button>
                                <?php } else { ?>
                                  <div class="alert alert-warning" style="margin: 0; padding: 12px; border-radius: 8px; text-align: left;">
                                    您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！
                                  </div>
                                <?php } ?>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="download-panel">
                          <button class="panel-header" onclick="togglePanel('panel8')">
                            <div>
                              <div class="panel-title">FD全能价格监控脚本</div>
                              <div class="panel-subtitle">累计充值不低于500可下载 - 解决所有痛点的监控脚本</div>
                            </div>
                            <span class="panel-toggle collapsed" id="toggle8">▼</span>
                          </button>
                          <div class="panel-content" id="panel8" style="display: none;">
                            <div class="panel-body">
                              <div class="content-description">
                                <h6>核心功能</h6>
                                <ul class="feature-list">
                                  <li>商品上架/下架/价格监控</li>
                                  <li>自动识别商品分类</li>
                                  <li>自动修改商品前缀（根据已有商品）</li>
                                  <li>支持监控多货源多价格倍率自定义</li>
                                  <li>支持邮件发送监控记录（FD模板原生支持）</li>
                                  <li>仅监控平台已对接的上游分类，自动忽略未上架分类</li>
                                </ul>

                                <h6 style="margin-top: 20px;">兼容性</h6>
                                <p>上游只要有getclass接口则均可使用，包含主流功能，解决所有痛点。</p>

                                <p style="margin-top: 16px;">余额不低于500，且顶级费率，刷新本页可见下载按钮！</p>
                              </div>
                              <div style="text-align: right; margin-top: 20px;">
                                <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                  <button onclick="download(8)" class="btn btn-primary">立即下载</button>
                                <?php } else { ?>
                                  <div class="alert alert-warning" style="margin: 0; padding: 12px; border-radius: 8px; text-align: left;">
                                    您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！
                                  </div>
                                <?php } ?>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="download-panel">
                          <button class="panel-header" onclick="togglePanel('panel9')">
                            <div>
                              <div class="panel-title">更多插件...</div>
                              <div class="panel-subtitle">更多实用插件正在开发中，敬请期待</div>
                            </div>
                            <span class="panel-toggle collapsed" id="toggle9">▼</span>
                          </button>
                          <div class="panel-content" id="panel9" style="display: none;">
                            <div class="panel-body">
                              <div class="content-description">
                                <h6>即将推出</h6>
                                <ul class="feature-list">
                                  <li>更多运动平台对接套件</li>
                                  <li>高级功能插件</li>
                                  <li>自动化工具脚本</li>
                                  <li>系统优化工具</li>
                                </ul>

                                <p style="margin-top: 16px; font-style: italic;">敬请期待更多实用功能的上线！</p>
                              </div>
                              <div style="text-align: right; margin-top: 20px;">
                                <button class="btn btn-primary" style="background: #6c757d; border-color: #6c757d;" disabled>
                                  敬请期待
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                    </div>
              </div>
            </div>
        </div>
 </div>
 

<script type="text/javascript" src="assets/LightYear/js/jquery.min.js"></script>
<script type="text/javascript" src="assets/LightYear/js/bootstrap.min.js"></script>
<script type="text/javascript" src="assets/LightYear/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="assets/LightYear/js/main.min.js"></script>
<script src="assets/js/aes.js"></script>
<script src="assets/js/vue.min.js"></script>
<script src="assets/js/vue-resource.min.js"></script>
<script src="assets/main/axios.min.js"></script>
<script src="assets/layui/layui.js"></script>  
<script src="assets/js/element.js"></script>

<script>
function searchTable() {
    var input, filter, table, tr, td, i, txtValue;
    input = document.getElementById("searchInput");
    filter = input.value.toUpperCase();
    table = document.getElementById("dataTable");
    tr = table.getElementsByTagName("tr");

    for (i = 0; i < tr.length; i++) {
        td = tr[i].getElementsByTagName("td")[2]; // 假设商品名称在第三列
        if (td) {
            txtValue = td.textContent || td.innerText;
            if (txtValue.toUpperCase().indexOf(filter) > -1) {
                tr[i].style.display = "";
            } else {
                tr[i].style.display = "none";
            }
        } 
    }
}
</script>
<script>
    //注意：选项卡 依赖 element 模块，否则无法进行功能性操作
    layui.use('element', function(){
      var element = layui.element;
      
      //…
    });
    </script>
<script>
function download(file_id) {
    var progressLayer = layer.open({
        type: 1,
        title: '下载中...',
        content: '<div style="padding:20px;"><div style="width:100%;height:12px;background:#f0f0f0;border-radius:6px;"><div id="downloadProgressBar" style="width:0%;height:100%;background:#1E9FFF;border-radius:6px;transition:width 0.3s ease;"></div></div><div id="progressText" style="text-align:center;padding-top:10px;color:#666;">0%</div></div>',
        shadeClose: true,
        closeBtn: 0,
        area: ['400px', '160px'],
        success: function(layerElem) {
            // 添加自定义关闭按钮
            var closeBtn = document.createElement('div');
            closeBtn.innerHTML = '×';
            closeBtn.style.position = 'absolute';
            closeBtn.style.right = '15px';
            closeBtn.style.bottom = '15px';
            closeBtn.style.cursor = 'pointer';
            closeBtn.style.fontSize = '20px';
            closeBtn.style.color = '#666';
            closeBtn.onclick = function() {
                layer.close(progressLayer);
                if(xhr) xhr.abort();
            };
            
            // 将关闭按钮添加到层容器
            var layerContainer = layerElem.find('.layui-layer-content').get(0);
            layerContainer.appendChild(closeBtn);
        }
    });

    var xhr = null;

    tryPost();


    function tryPost() {
        xhr = new XMLHttpRequest();
        xhr.open('POST', '/downloads.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.responseType = 'blob';
        
        setupCommonHandlers();
        xhr.send('file_id=' + encodeURIComponent(file_id));
    }

    function setupCommonHandlers() {
        // 公共处理逻辑
        xhr.timeout = 2160000;
        
        xhr.ontimeout = function() {
            layer.close(progressLayer);
            layer.msg('请求超时，请重试', { icon: 2 });
        };

        xhr.onprogress = function(e) {
            if (e.lengthComputable) {
                var percent = Math.round((e.loaded / e.total) * 100);
                updateProgress(percent, e.loaded, e.total);
            }
        };

        xhr.onload = function() {
            if (xhr.status === 200) {
                handleSuccess();
            } else if(xhr.status >= 400 && this.method === 'GET') {
                // GET失败时尝试POST
                tryPost();
            } else {
                handleError();
            }
        };

        xhr.onerror = function() {
            if(this.method === 'GET') {
                tryPost();
            } else {
                handleError();
            }
        };
    }

    function handleSuccess() {
        layer.close(progressLayer);
        var filename = getFilenameFromHeaders(xhr);
        var url = window.URL.createObjectURL(xhr.response);
        var a = document.createElement('a');
        a.href = url;
        a.download = filename || 'downloadedfile.zip';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    function handleError() {
        layer.close(progressLayer);
        layer.msg('下载失败: ' + (xhr.statusText || '网络错误'), { icon: 2 });
    }

    function updateProgress(percent, loaded, total) {
        var progressBar = document.getElementById('downloadProgressBar');
        var progressText = document.getElementById('progressText');
        if (progressBar && progressText) {
            progressBar.style.width = percent + '%';
            progressText.innerHTML = '已下载 ' + percent + '% (' + 
                formatSize(loaded) + '/' + formatSize(total) + ')';
        }
    }

    function formatSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function getFilenameFromHeaders(xhr) {
        var disposition = xhr.getResponseHeader('Content-Disposition');
        if (disposition) {
            // 处理RFC 5987编码的文件名
            const utf8FilenameRegex = /filename\*=(?:UTF-8'')?([^;]+)/i;
            const matches = utf8FilenameRegex.exec(disposition);
            if (matches && matches[1]) {
                return decodeURIComponent(matches[1]);
            }
            
            // 处理普通文件名
            const filenameRegex = /filename="?([^;"]+)"?/i;
            const normalMatches = filenameRegex.exec(disposition);
            if (normalMatches && normalMatches[1]) {
                return normalMatches[1].replace(/['"]/g, '');
            }
        }
        return null;
    }
}
</script>
<!--<script>
             //禁止鼠标右击
      document.oncontextmenu = function() {
        event.returnValue = false;
      };
      //禁用开发者工具F12
      document.onkeydown = document.onkeyup = document.onkeypress = function(event) {
        let e = event || window.event || arguments.callee.caller.arguments[0];
        if (e && e.keyCode == 123) {
          e.returnValue = false;
          return false;
        }
      };
      let userAgent = navigator.userAgent;
      if (userAgent.indexOf("Firefox") > -1) {
        let checkStatus;
        let devtools = /./;
        devtools.toString = function() {
          checkStatus = "on";
        };
        setInterval(function() {
          checkStatus = "off";
          console.log(devtools);
          console.log(checkStatus);
          console.clear();
          if (checkStatus === "on") {
            let target = "";
            try {
              window.open("about:blank", (target = "_self"));
            } catch (err) {
              let a = document.createElement("button");
              a.onclick = function() {
                window.open("about:blank", (target = "_self"));
              };
              a.click();
            }
          }
        }, 200);
      } else {
        //禁用控制台
        let ConsoleManager = {
          onOpen: function() {
            alert("Console is opened");
          },
          onClose: function() {
            alert("Console is closed");
          },
          init: function() {
            let self = this;
            let x = document.createElement("div");
            let isOpening = false,
              isOpened = false;
            Object.defineProperty(x, "id", {
              get: function() {
                if (!isOpening) {
                  self.onOpen();
                  isOpening = true;
                }
                isOpened = true;
                return true;
              }
            });
            setInterval(function() {
              isOpened = false;
              console.info(x);
              console.clear();
              if (!isOpened && isOpening) {
                self.onClose();
                isOpening = false;
              }
            }, 200);
          }
        };
        ConsoleManager.onOpen = function() {
          //打开控制台，跳转
          let target = "";
          try {
            window.open("about:blank", (target = "_self"));
          } catch (err) {
            let a = document.createElement("button");
            a.onclick = function() {
              window.open("about:blank", (target = "_self"));
            };
            a.click();
          }
        };
        ConsoleManager.onClose = function() {
          alert("Console is closed!!!!!");
        };
        ConsoleManager.init();
      }
        </script>-->