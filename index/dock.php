<?php
$mod='blank';
$title='平台对接';
$ym="freedomp.icu";
require_once('head.php');
$fl=$DB->count("select addprice from qingka_wangke_user where uid='{$userrow['uid']}'");
$ck=$DB->count("SELECT count(id) FROM `qingka_wangke_log` WHERE type='API查课'AND uid='{$userrow['uid']}' ");
$xd = $DB->count("SELECT count(id) FROM `qingka_wangke_log` WHERE type='API添加任务' AND uid='{$userrow['uid']}' ");
$xdbxz=$conf['api_proportion'];
$xdb=round($xd/$ck,4)*100;
?>
<style>
.interface-section {
    margin-bottom: 30px;
}

.interface-title {
    color: #333;
    margin-bottom: 20px;
}

.interface-divider {
    border: 0;
    height: 1px;
    background: #ddd;
    margin: 30px 0;
}
.custom-close-btn:hover {
    color: #333 !important;
    transform: scale(1.1);
    transition: all 0.3s ease;
}

  .td-wrap {
    white-space: normal; /* 允许内容自动换行 */
    max-width: 200px; /* 最大宽度 */
    word-wrap: break-word; /* 允许在单词中间断行 */
  }
/* 卡片阴影和边框 */
.card {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2); /* 加强阴影效果 */
  border-radius: 12px; /* 更加圆滑的边角 */
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out; /* 添加过渡效果 */
  border: 1px solid rgba(0, 0, 0, 0.1); /* 添加轻微边框 */
  background: linear-gradient(145deg, #ffffff, #f1f1f1); /* 添加渐变背景 */
}

.fdcard:hover {
  transform: scale(1.01); /* 鼠标悬停时轻微放大 */
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3); /* 鼠标悬停时加强阴影 */
}

/* 按钮样式 */
.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  border-radius: 8px; /* 使按钮有圆角 */
  color: #fff;
  padding: 10px 20px; /* 加大内边距 */
  font-size: 16px; /* 调整字体大小 */
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4); /* 添加阴影效果 */
  transition: background-color 0.3s ease, transform 0.2s ease; /* 添加过渡动画 */
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
  transform: translateY(-2px); /* 鼠标悬停时按钮微微上升 */
  box-shadow: 0 6px 12px rgba(0, 123, 255, 0.6); /* 鼠标悬停时加大阴影 */
}

/* 按钮点击时效果 */
.btn-primary:active {
  transform: translateY(0); /* 点击时回到原位置 */
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.4); /* 点击时阴影变弱 */
  background-color: #004085; /* 点击时颜色略深 */
}

/* 卡片内部文本调整 */
.card-title {
  font-weight: bold;
  font-size: 18px;
  color: #333;
}

.card-text {
  font-size: 14px;
  color: #666;
}

/* 小型屏幕调整布局 */
@media (max-width: 768px) {
  .card {
    margin-bottom: 20px; /* 移动端增加卡片之间的间距 */
  }
  .btn-primary {
    width: 100%; /* 按钮在移动设备上全宽显示 */
  }
}


</style>
     <div class="app-content-body ">
        <div class="wrapper-md control">
        <div class="layui-row layui-col-space8 layui-anim layui-anim-upbit">
        <div  class="card" style="box-shadow: 3px 3px 8px #d1d9e6, -3px -3px 8px #d1d9e6;border-radius: 7px;">
            <ul class="nav nav-tabs" role="tablist">
            <li class="active">
              <a data-toggle="tab" href="#ck">查课接口</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#xd">下单接口</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#bs">补刷接口</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#jd">同步接口</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#gm">改密接口</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#ik">独家项目接口</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#kcid">商品信息</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#flid">分类信息</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#xx">我的信息</a>
            </li>
            <li class="nav-item">
              <a data-toggle="tab" href="#xz">资料下载</a>
            </li>
          </ul>
            <div class="tab-content">
                <div class="tab-pane fade active in" id="ck">
                    <pre><h5>POST网址:</h5>https://<?echo($ym);?>/api.php?act=get</pre>
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                        <tr>
                                            <th class="text-center" style="width:100px">请求参数</th>
                                            <th>
                                                说明<br>
                                            </th>
                                            <th class="text-center" style="width:100px">
                                                传输类型<br>
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <tr>
                                                <th class="text-center" scope="row">uid</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">key</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">platform</th>
                                                <td><code>平台ID</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
    
                                            <tr>
                                                <th class="text-center" scope="row">user</th>
                                                <td><code>学生账号</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">pass</th>
                                                <td><code>学生密码</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                             <tr>
                                                <th class="text-center" scope="row">school</th>
                                                <td><code>学生学校</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                        </tr>
                                        </tbody>
                                    </table>
                    <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                    <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                    <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/xdjk.php 文件</h5></pre>
                    <pre class="layui-code code-demo" lay-title="29查课对接代码" lay-options="{}"><h4>29对接代码：</h4><h5>//FD平台查课接口 复制代码放在/Checkorder/ckjk.php 文件
                    
                        else if ($type == "FD")
                        {
                            $data = array("uid" => $a["user"], "key" => $a["pass"], "school" => $school, "user" => $user, "pass" => $pass, "platform" => $noun, "kcid" => $kcid);
                            $dx_rl = $a["url"];
                            $dx_url = "$dx_rl/api.php?act=get";
                            $result = get_url($dx_url, $data);
                            $result = json_decode($result, true);
                            return $result;
                        }</h5></pre>
                </div>
                <div class="tab-pane fade" id="xd">
                 <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=add</pre>
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                            <tr>
                                                <th class="text-center" style="width:100px">请求参数</th>
                                                <th>
                                                    说明<br>
                                                </th>
                                                <th class="text-center" style="width:100px">
                                                    传输类型<br>
                                                </th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <th class="text-center" scope="row">uid</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">key</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">platform</th>
                                                <td><code>平台ID</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
    
                                            <tr>
                                                <th class="text-center" scope="row">user</th>
                                                <td><code>学生账号</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">pass</th>
                                                <td><code>账号密码</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">kcname</th>
                                                <td><code>课程名字</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">kcid</th>
                                                <td><code>课程ID</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            </tbody>
                                        </table>
                 <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                 <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                    <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/xdjk.php 文件</h5></pre>
                    <pre class="layui-code code-demo" lay-title="29下单对接代码" lay-options="{}"><h4>29对接代码：</h4><h5>//FD平台下单接口 复制代码放在/Checkorder/xdjk.php 文件
                    
                        else if ($type == "FD") 
                        {
                            $data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $noun, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "kcid" => $kcid);
                            $dx_rl = $a["url"];
                            $dx_url = "$dx_rl/api.php?act=add";
                            $result = get_url($dx_url, $data);
                            $result = json_decode($result, true);
                            if ($result["code"] == "0") {
                                $b = array("code" => 1, "msg" => "下单成功",'yid'=>$result['id']);
                            } else {
                                $b = array("code" => - 1, "msg" => $result["msg"]);
                        }
                        return $b;
                        }</h5></pre>
                </div>
                <div class="tab-pane fade" id="bs">
                    <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=budan</pre>
                        <table class="table table-bordered table-striped">
                                        <thead>
                                        <tr>
                                            <th class="text-center" style="width:100px">请求参数</th>
                                            <th>
                                                说明<br>
                                            </th>
                                            <th class="text-center" style="width:100px">
                                                传输类型<br>
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <th class="text-center" scope="row">uid</th>
                                            <td><code>登录验证</code></td>
                                            <td class="text-center"><code>必传</code></td>
                                        </tr>
                                        <tr>
                                            <th class="text-center" scope="row">key</th>
                                            <td><code>登录验证</code></td>
                                            <td class="text-center"><code>必传</code></td>
                                            </tr>
                                        <tr>
                                            <th class="text-center" scope="row">id</th>
                                            <td><code>订单账号</code></td>
                                            <td class="text-center"><code>必传</code></td>
                                        </tr>
                                        </tbody>
                                    </table>
                    <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                    <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                    <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/xdjk.php 文件</h5></pre>
                    <pre class="layui-code code-demo" lay-title="29补刷对接代码" lay-options="{}"><h4>29对接代码：</h4><h5>//FD平台补刷接口 复制代码放在/Checkorder/bsjk.php 文件
                    
                        elseif ($type == "FD") 
                        {
                            $data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
                            $dx_rl = $a["url"];
                            $dx_url = "$dx_rl/api.php?act=budan";
                            $result = get_url($dx_url, $data);
                            $result = json_decode($result, true);
                            return $result;
                        }</h5></pre>
                </div>
                <div class="tab-pane fade" id="jd">
                    <pre><h5>POST:（下面使用的是实时进度接口chadanoid，如遇到问题可以更换为默认的进度接口chadanmr，记到传yid，两个接口均不会串进度！）</h5>https://<?echo($ym);?>/api.php?act=chadanoid</pre>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width:100px">请求参数</th>
                                    <th>
                                        说明<br>
                                    </th>
                                    <th class="text-center" style="width:100px">
                                         传输类型<br>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                            <tr>
                                                <th class="text-center" scope="row">uid</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">key</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">yid</th>
                                                <td><code>订单id</code></td>
                                                <td class="text-center"><code>可选</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">username</th>
                                                <td><code>订单账号</code></td>
                                                <td class="text-center"><code>可选</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">school</th>
                                                <td><code>订单学校</code></td>
                                                <td class="text-center"><code>可选</code></td>
                                            </tr>
                                            </tbody>
                                        </table>
                    <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                    <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                    <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/xdjk.php 文件</h5></pre>
                    <pre class="layui-code code-demo" lay-title="29进度对接代码" lay-options="{}"><h4>29进度对接代码（支持yid和账号查询）：</h4><h5>//FD平台进度接口 复制代码放在/Checkorder/jdjk.php 文件
                    
        else if ($type == "FD") {
            $data = array("username" => $user,"yid"=>$d["yid"],"uid" => $a["user"],"key" => $a["pass"]);//$d["yid"]为订单yid，如果没有自行在jdjk.php文件顶部添加该参数！
            $dx_rl = $a["url"];
            $dx_url  = "$dx_rl/api.php?act=chadanoid";
            $result = get_url($dx_url, $data);
            $result = json_decode($result, true);
            if ($result["code"] == "1") {
                foreach ($result["data"] as $res) {
                $yid = $res["id"];
                $kcname = $res["kcname"];
                $status = $res["status"];
                $process = $res["process"];
                $remarks = $res["remarks"];
                $kcks = $res["courseStartTime"];
                $kcjs = $res["courseEndTime"];
                $ksks = $res["examStartTime"];
                $ksjs = $res["examEndTime"];
                $b[] = array("code" => 1, "msg" => "查询成功", "yid" => $yid, "kcname" => $kcname, "user" => $user, "pass" => $pass, "ksks" => $ksks, "ksjs" => $ksjs, "status_text" => $status, "process" => $process, "remarks" => $remarks);
                }
            } else {
                $b[] = array("code" => -1, "msg" => $result);
            }
            return $b;
        }</h5></pre>
                </div>
                <div class="tab-pane fade" id="ik">
                    <!-- 改密接口 -->
                    <div class="interface-section">
                        <h3 class="interface-title">改密接口</h3>
                        <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=xgmm</pre>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width:100px">请求参数</th>
                                    <th>说明</th>
                                    <th class="text-center" style="width:100px">传输类型</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th class="text-center" scope="row">uid</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">key</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">yid</th>
                                    <td><code>订单id</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">pwd</th>
                                    <td><code>新密码</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                            </tbody>
                        </table>
                        <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                        <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                        <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/gmjk.php 文件</h5></pre>
                        <pre class="layui-code code-demo" lay-title="29进度对接代码" lay-options="{}"><h4>29改密对接代码：</h4><h5>//FD平台改密接口 复制代码放在/Checkorder/gmjk.php 文件
                
                elseif ($type == "FD") {
                    $data = array("uid" => $a["user"], "key" => $a["pass"], "oid" => $yid,'pwd' =>$newpass);
                    $dx_rl = $a["url"];
                    $dx_url = "$dx_rl/api.php?act=xgmm";
                    $result = get_url($dx_url, $data);
                    $result = json_decode($result, true);
                    return $result;
                }</h5></pre>
                    </div>
                    <hr class="interface-divider">
                
                    <!-- 停止接口 -->
                    <div class="interface-section">
                        <h3 class="interface-title">停止接口</h3>
                        <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=iktz</pre>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width:100px">请求参数</th>
                                    <th>说明</th>
                                    <th class="text-center" style="width:100px">传输类型</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th class="text-center" scope="row">uid</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">key</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">yid</th>
                                    <td><code>订单id</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                            </tbody>
                        </table>
                        <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                        <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                        <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/stopjk.php 文件</h5></pre>
                        <pre class="layui-code code-demo" lay-title="对接提示" lay-options="{}"><h4>独家项目停止跑单对接代码：</h4><h5>传递订单ID接口即可，自行开发</h5></pre>
                    </div>
                    <hr class="interface-divider">
                
                    <!-- 转秒接口 -->
                    <div class="interface-section">
                        <h3 class="interface-title">转秒接口</h3>
                        <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=ikzm</pre>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width:100px">请求参数</th>
                                    <th>说明</th>
                                    <th class="text-center" style="width:100px">传输类型</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th class="text-center" scope="row">uid</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">key</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">yid</th>
                                    <td><code>订单id</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                            </tbody>
                        </table>
                        <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                        <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                        <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4></pre>
                        <pre class="layui-code code-demo" lay-title="29进度对接代码" lay-options="{}"><h4>独家项目转秒对接代码：</h4><h5>传递订单ID接口即可，自行开发</h5></pre>
                    </div>
                    <hr class="interface-divider">
                
                    <!-- 立即考试接口 -->
                    <div class="interface-section">
                        <h3 class="interface-title">立即考试接口</h3>
                        <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=ikks</pre>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width:100px">请求参数</th>
                                    <th>说明</th>
                                    <th class="text-center" style="width:100px">传输类型</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th class="text-center" scope="row">uid</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">key</th>
                                    <td><code>登录验证</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                                <tr>
                                    <th class="text-center" scope="row">yid</th>
                                    <td><code>订单id</code></td>
                                    <td class="text-center"><code>必传</code></td>
                                </tr>
                            </tbody>
                        </table>
                        <pre><h4>29系统对接网址</h4>https://<?echo($ym);?></pre>
                        <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                        <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4></pre>
                        <pre class="layui-code code-demo" lay-title="独家项目立即考试对接代码" lay-options="{}"><h4>29立即考试对接代码：</h4><h5>传递订单ID接口即可，自行开发</h5></pre>
                    </div>
                </div>
                <div class="tab-pane fade" id="gm">
                    <pre><h5>POST:</h5>https://<?echo($ym);?>/api.php?act=xgmm</pre>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width:100px">请求参数</th>
                                    <th>
                                        说明<br>
                                    </th>
                                    <th class="text-center" style="width:100px">
                                         传输类型<br>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                            <tr>
                                                <th class="text-center" scope="row">uid</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">key</th>
                                                <td><code>登录验证</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">yid</th>
                                                <td><code>订单id</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            <tr>
                                                <th class="text-center" scope="row">pwd</th>
                                                <td><code>新密码</code></td>
                                                <td class="text-center"><code>必传</code></td>
                                            </tr>
                                            </tbody>
                                        </table>
                    <pre><h4>29系统对接网址</h4>https://<?echo($_SERVER['SERVER_NAME']);?></pre>
                    <pre><h4>对接参数设置如下：</h4>账号：uid 密码：key token：key</pre>
                    <pre class="layui-code code-demo" lay-title="标识" lay-options="{}"><h4>29对接标识：</h4><h5>//FD标识  "FD" => "FreeDom", 放在/Checkorder/gmjk.php 文件</h5></pre>
                    <pre class="layui-code code-demo" lay-title="29进度对接代码" lay-options="{}"><h4>29改密对接代码：</h4><h5>//FD平台改密接口 复制代码放在/Checkorder/gmjk.php 文件
                    
        elseif ($type == "FD") {
            $data = array("uid" => $a["user"], "key" => $a["pass"], "oid" => $yid,'pwd' =>$newpass);
            $dx_rl = $a["url"];
            $dx_url = "$dx_rl/api.php?act=xgmm";
            $result = get_url($dx_url, $data);
            $result = json_decode($result, true);
            return $result;
            }</h5></pre>
                </div>
                <div class="tab-pane fade" id="kcid">
                    <div class="modal-content">
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索商品名称..." onkeyup="searchTable()">
                        <div class="table-responsive"> 
                            <table class="table table-striped" id="dataTable">
                                <thead>
                                    <tr>
                                        <th>对接ID</th>
                                        <th>分类id</th>
                                        <th>商品名称</th>
                                        <th>你的价格（<?php echo $userrow['addprice']?>）</th>
                                        <th>0.2</th>
                                        <th>0.3</th>
                                        <th>0.4</th>
                                        <th>0.5</th>
                                        <th>0.6</th>
                                        <th>0.8</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                        $query = "select * from qingka_wangke_class where status=1";
                                        $result = $DB->query($query);
                                    
                                        while ($row = $DB->fetch($result)) {
                                            echo "<tr>";
                                            echo "<td>".$row['cid']."</td>";
                                            echo "<td>".$row['fenlei']."</td>";
                                            echo "<td class=\"td-wrap\">".htmlspecialchars($row['name'])."</td>";
                                            echo "<td>".$row['price'] * $fl."</td>";
                                            echo "<td>".$row['price']*0.2."</td>";
                                            echo "<td>".$row['price']*0.3."</td>";
                                            echo "<td>".$row['price']*0.4."</td>";
                                            echo "<td>".$row['price']*0.5."</td>";
                                            echo "<td>".$row['price']*0.6."</td>";
                                            echo "<td>".$row['price']*0.8."</td>";
                                            echo "</tr>";
                                        }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="flid">
                <div class="modal-content">
                    <div class="table-responsive"> 
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>分类ID</th>
                                    <th>分类名称</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                    $query = "select * from qingka_wangke_fenlei where status!=0";
                                    $result = $DB->query($query);
    
                                    while ($row = $DB->fetch($result)) {
                                        echo "<tr>";
                                        echo "<td>".$row['id']."</td>";
                                        echo "<td>".$row['name']."</td>";
                                        echo "</tr>";
                                    }
                                ?>
                        </tbody>
                        </table>
                    </div>
                </div>
                </div>
                <div class="tab-pane fade" id="xx">
                    <div class="modal-content">
                        <div class="table-responsive"> 
    						<table class="table table-striped">
    							<thead>
    							    <tr>
    							        <th>对接ID</th>
    							        <th>对接KEY</th>
    							        <th>下单比限制</th>
    							        <th>当前API下单比</th>
    							    </tr>
    							</thead>
    								<tbody>
    									<?php 
    										$a=$DB->query("select * from qingka_wangke_user where uid='{$userrow['uid']}'");
    										
    										while($rs=$DB->fetch($a)){
    										    if($rs['key']==0){
    										    $rs['key']="未开通KEY";
    										    echo "<tr><td>".$rs['uid']."</td><td>".$rs['key']."</td><td>".$xdbxz."%</td><td>".$xdb."%</td></tr>"; 
    										    }else{
    										        echo "<tr><td>".$rs['uid']."</td><td>".$rs['key']."</td><td>".$xdbxz."%</td><td>".$xdb."%</td></tr>"; 
    										    }
    										}
    									?>
                                	</tbody>
    						</table>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="xz">
                    <h3>资料下载</h3>
                    <div class="row">
                      <div class="col-md-4">
                        <div class="card mb-4 fdcard">
                          <div class="card-body">
                            <h4 class="card-title">FreeDom模板</h4>
                            <p class="card-text">  余额不低于1000可下！</p><br>
                            <p class="alert alert-success">注意<br><br>
                            5.28日更新<br>
                            1-加入接单商城<br>
                            2-本次更新涉及数据库字段增加，老模板无法直接替换安装！<br><br><br>
                            3.2日更新<br>
                            1-代理管理界面加入跨户权限修改，增加余额扣除功能，仅管理员可进行余额扣除，输入负值即可。<br>
                            2-下单页加入勾选课程数量提示。<br><br><br>
                            
                            
                                FreeDom模板正式上线，已有的一些功能写在下面了，太多宝子不会配置，不会自己调试，所以把我自己这么久整理的东西开放给大家使用，其实难度都不大，花时间慢慢研究就行了。<br><br>
                                加入全新公告系统：暗网同款，独家编辑界面，支持自定义各界面弹窗公告。<br><br>
                                加入首页登录免责申明：暗网同款。<br><br>
                                加入微信/邮件发信系统：支持异常提醒，拟增加登录提醒，发客户进度提醒，也许会支持发客户发代理。<br><br>
                                加入独创货源管理系统：一键对接，批量管理上架，一键修改价格。<br><br>
                                密价管理系统，界面美化，可按照分类批量添加，加入批量删除功能。<br><br>
                                当然也有新的批量补刷与同步文件<br><br>
                                同款交单页面，同款订单页面，同款代理管理界面，加载速度超快，修复许多已知bug。<br><br><br>
                                代码开源，绝大部分代码均是来自于互联网，，欢迎交流使用。
                                
                            <?php if ($userrow['money'] >= 1000&&$userrow['addprice'] == 0.2) { ?>
                                <button onclick="download(1)" class="btn btn-primary" download>立即下载</button>
                            <?php } else { ?>
                                <p class="alert alert-warning">余额至少1000且等级为顶级才能下载此文件！</p>
                            <?php } ?>
                          </div>
                        </div>
                      </div>
                      
                      <div class="col-md-4">
                        <div class="card mb-4 fdcard">
                          <div class="card-body">
                            <h4 class="card-title">鲸鱼运动无限套娃对接套件</h4>
                            <p class="card-text">  累计充值不低于500可下！无限套娃！傻瓜式配置填写uid与key即可一键对接！</p><br>
                            <p class="alert alert-success">注意<br><br>
                            如出现页面大小不正常，请打开单页代码删除{class="lyear-layout-content" }即可<br>
                                1.可无限向下对接，就是无限二开让你代理对接你<br>
                                2.本插件为免费试用无任何收费<br>
                                3.本插件优势为：您对接本站恢复源台体验<br>
                                4.余额不低于500,且顶级费率 刷新本页可见!<br><br>
                                
                            <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                <button onclick="download(2)" class="btn btn-primary" download>立即下载</button>
                            <?php } else { ?>
                                <p class="alert alert-warning">您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！</p>
                            <?php } ?>
                          </div>
                        </div>
                      </div>
                        
                      <div class="col-md-4">
                        <div class="card mb-4 fdcard">
                          <div class="card-body">
                            <h4 class="card-title">雷电运动无限套娃对接套件</h4>
                            <p class="card-text">  累计充值不低于500可下！无限套娃！傻瓜式配置填写uid与key即可一键对接！</p><br>
                            <p class="alert alert-success">注意<br><br>
                            如出现页面大小不正常，请打开单页代码删除{class="lyear-layout-content" }即可<br>
                                1.可无限向下对接，就是无限二开让你代理对接你<br>
                                2.本插件为免费试用无任何收费<br>
                                3.本插件优势为：您对接本站恢复源台体验<br>
                                4.余额不低于500,且顶级费率 刷新本页可见!<br><br>
                                
                            <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                <button onclick="download(4)" class="btn btn-primary" download>立即下载</button>
                            <?php } else { ?>
                                <p class="alert alert-warning">您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！</p>
                            <?php } ?>
                          </div>
                        </div>
                      </div>
                        
                      <div class="col-md-4">
                        <div class="card mb-4 fdcard">
                          <div class="card-body">
                            <h4 class="card-title">杀手运动无限套娃对接套件</h4>
                            <p class="card-text">  累计充值不低于500可下！无限套娃！傻瓜式配置填写uid与key即可一键对接！</p><br>
                            <p class="alert alert-success">注意<br><br>
                            如出现页面大小不正常，请打开单页代码删除{class="lyear-layout-content" }即可<br>
                                1.可无限向下对接，就是无限二开让你代理对接你<br>
                                2.本插件为免费试用无任何收费<br>
                                3.本插件优势为：您对接本站恢复源台体验<br>
                                4.余额不低于500,且顶级费率 刷新本页可见!<br><br>
                                
                            <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                <button onclick="download(3)" class="btn btn-primary" download>立即下载</button>
                            <?php } else { ?>
                                <p class="alert alert-warning">您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！</p>
                            <?php } ?>
                          </div>
                        </div>
                      </div>
                      
                      <div class="col-md-4">
                        <div class="card mb-4 fdcard">
                          <div class="card-body">
                            <h4 class="card-title">APPUI打卡无限套娃对接套件</h4>
                            <p class="card-text">  累计充值不低于500可下！无限套娃！傻瓜式配置填写uid与key即可一键对接！</p><br>
                            <p class="alert alert-success">注意<br><br>
                            如出现页面大小不正常，请打开单页代码删除{class="lyear-layout-content" }即可<br>
                                1.可无限向下对接，就是无限二开让你代理对接你<br>
                                2.本插件为免费试用无任何收费<br>
                                3.本插件优势为：您对接本站恢复源台体验<br>
                                4.余额不低于500,且顶级费率 刷新本页可见!<br><br>
                                
                            <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                <button onclick="download(5)" class="btn btn-primary" download>立即下载</button>
                            <?php } else { ?>
                                <p class="alert alert-warning">您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！</p>
                            <?php } ?>
                          </div>
                        </div>
                      </div>
                      
                      <div class="col-md-4">
                        <div class="card mb-4 fdcard">
                          <div class="card-body">
                            <h4 class="card-title">盘古运动无限套娃对接套件</h4>
                            <p class="card-text">  累计充值不低于500可下！无限套娃！傻瓜式配置填写uid与key即可一键对接！</p><br>
                            <p class="alert alert-success">注意<br><br>
                            如出现页面大小不正常，请打开单页代码删除{class="lyear-layout-content" }即可<br>
                                1.可无限向下对接，就是无限二开让你代理对接你<br>
                                2.本插件为免费试用无任何收费<br><br>
                                3.功能说明：<br>
                                ①、包含:步道乐跑；乐跑小程序；体适能；keep；云运动<br>
                                ②、支持用户自助退款<br>
                                ③、所有项目支持自定义配速<br>
                                ④、支持用户查看跑步详情，日志<br>
                                ⑤、总之源台支持的功能我们都支持！<br><br>
                                4.本插件优势为：您对接本站恢复源台体验<br>
                                5.余额不低于500,且顶级费率 刷新本页可见!<br><br>
                                
                            <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                <button onclick="download(6)" class="btn btn-primary" download>立即下载</button>
                            <?php } else { ?>
                                <p class="alert alert-warning">您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！</p>
                            <?php } ?>
                          </div>
                        </div>
                      </div>
                      
                      <div class="col-md-4">
                        <div class="card mb-4 fdcard">
                          <div class="card-body">
                            <h4 class="card-title">FD独家项目改密，暂停，日志对接文档</h4>
                            <p class="card-text">  累计充值不低于500可下！可无限套娃！</p><br>
                            <p class="alert alert-success">注意<br><br>
                                1.可无限向下对接，就是无限二开让你代理对接你<br>
                                2.本插件为免费试用无任何收费<br><br>
                                3.余额不低于500,且顶级费率 刷新本页可见!<br><br>
                                
                            <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                <button onclick="download(7)" class="btn btn-primary" download>立即下载</button>
                            <?php } else { ?>
                                <p class="alert alert-warning">您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！</p>
                            <?php } ?>
                          </div>
                        </div>
                      </div>
                      
                      <div class="col-md-4">
                        <div class="card mb-4 fdcard">
                          <div class="card-body">
                            <h4 class="card-title">FD全能价格监控脚本</h4>
                            <p class="card-text">  累计充值不低于500可下！</p><br>
                            <p class="alert alert-success">注意<br><br>
                                1.包含主流功能，解决所有痛点<br><br>

                                2.商品上架/下架/价格监控<br>
                                3.自动识别商品分类<br>
                                4.自动修改商品前缀（根据已有商品）<br>
                                5.支持监控多货源多价格倍率自定义<br>
                                6.支持邮件发送监控记录（FD模板原生支持）<br>
                                7.仅监控平台已对接的上游分类，对平台未上架的上游分类自动忽略。<br><br>
                                
                                上游只要有getclass接口则均可使用<br><br>
                                
                            <?php if ($userrow['zcz'] >= 500&&$userrow['addprice'] == 0.2) { ?>
                                <button onclick="download(8)" class="btn btn-primary" download>立即下载</button>
                            <?php } else { ?>
                                <p class="alert alert-warning">您需要累计充值至少500且等级为顶级且余额大于20才能下载此文件！</p>
                            <?php } ?>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-4">
                        <div class="card mb-4 fdcard">
                          <div class="card-body">
                            <h5 class="card-title">更多插件...</h5>
                            <p class="card-text">更多插件...待上架</p>
                            <a href="downloads/resource3.docx" class="btn btn-primary" download>下载</a>
                          </div>
                        </div>
                      </div>
        
                    
                      <!-- 继续添加更多资料卡片 -->
                    </div>
              </div>
            </div>
        </div>
 </div>
 

<script type="text/javascript" src="assets/LightYear/js/jquery.min.js"></script>
<script type="text/javascript" src="assets/LightYear/js/bootstrap.min.js"></script>
<script type="text/javascript" src="assets/LightYear/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="assets/LightYear/js/main.min.js"></script>
<script src="assets/js/aes.js"></script>
<script src="assets/js/vue.min.js"></script>
<script src="assets/js/vue-resource.min.js"></script>
<script src="assets/main/axios.min.js"></script>
<script src="assets/layui/layui.js"></script>  
<script src="assets/js/element.js"></script>

<script>
function searchTable() {
    var input, filter, table, tr, td, i, txtValue;
    input = document.getElementById("searchInput");
    filter = input.value.toUpperCase();
    table = document.getElementById("dataTable");
    tr = table.getElementsByTagName("tr");

    for (i = 0; i < tr.length; i++) {
        td = tr[i].getElementsByTagName("td")[2]; // 假设商品名称在第三列
        if (td) {
            txtValue = td.textContent || td.innerText;
            if (txtValue.toUpperCase().indexOf(filter) > -1) {
                tr[i].style.display = "";
            } else {
                tr[i].style.display = "none";
            }
        } 
    }
}
</script>
<script>
    //注意：选项卡 依赖 element 模块，否则无法进行功能性操作
    layui.use('element', function(){
      var element = layui.element;
      
      //…
    });
    </script>
<script>
function download(file_id) {
    var progressLayer = layer.open({
        type: 1,
        title: '下载中...',
        content: '<div style="padding:20px;"><div style="width:100%;height:12px;background:#f0f0f0;border-radius:6px;"><div id="downloadProgressBar" style="width:0%;height:100%;background:#1E9FFF;border-radius:6px;transition:width 0.3s ease;"></div></div><div id="progressText" style="text-align:center;padding-top:10px;color:#666;">0%</div></div>',
        shadeClose: true,
        closeBtn: 0,
        area: ['400px', '160px'],
        success: function(layerElem) {
            // 添加自定义关闭按钮
            var closeBtn = document.createElement('div');
            closeBtn.innerHTML = '×';
            closeBtn.style.position = 'absolute';
            closeBtn.style.right = '15px';
            closeBtn.style.bottom = '15px';
            closeBtn.style.cursor = 'pointer';
            closeBtn.style.fontSize = '20px';
            closeBtn.style.color = '#666';
            closeBtn.onclick = function() {
                layer.close(progressLayer);
                if(xhr) xhr.abort();
            };
            
            // 将关闭按钮添加到层容器
            var layerContainer = layerElem.find('.layui-layer-content').get(0);
            layerContainer.appendChild(closeBtn);
        }
    });

    var xhr = null;

    tryPost();


    function tryPost() {
        xhr = new XMLHttpRequest();
        xhr.open('POST', '/downloads.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.responseType = 'blob';
        
        setupCommonHandlers();
        xhr.send('file_id=' + encodeURIComponent(file_id));
    }

    function setupCommonHandlers() {
        // 公共处理逻辑
        xhr.timeout = 2160000;
        
        xhr.ontimeout = function() {
            layer.close(progressLayer);
            layer.msg('请求超时，请重试', { icon: 2 });
        };

        xhr.onprogress = function(e) {
            if (e.lengthComputable) {
                var percent = Math.round((e.loaded / e.total) * 100);
                updateProgress(percent, e.loaded, e.total);
            }
        };

        xhr.onload = function() {
            if (xhr.status === 200) {
                handleSuccess();
            } else if(xhr.status >= 400 && this.method === 'GET') {
                // GET失败时尝试POST
                tryPost();
            } else {
                handleError();
            }
        };

        xhr.onerror = function() {
            if(this.method === 'GET') {
                tryPost();
            } else {
                handleError();
            }
        };
    }

    function handleSuccess() {
        layer.close(progressLayer);
        var filename = getFilenameFromHeaders(xhr);
        var url = window.URL.createObjectURL(xhr.response);
        var a = document.createElement('a');
        a.href = url;
        a.download = filename || 'downloadedfile.zip';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    function handleError() {
        layer.close(progressLayer);
        layer.msg('下载失败: ' + (xhr.statusText || '网络错误'), { icon: 2 });
    }

    function updateProgress(percent, loaded, total) {
        var progressBar = document.getElementById('downloadProgressBar');
        var progressText = document.getElementById('progressText');
        if (progressBar && progressText) {
            progressBar.style.width = percent + '%';
            progressText.innerHTML = '已下载 ' + percent + '% (' + 
                formatSize(loaded) + '/' + formatSize(total) + ')';
        }
    }

    function formatSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function getFilenameFromHeaders(xhr) {
        var disposition = xhr.getResponseHeader('Content-Disposition');
        if (disposition) {
            // 处理RFC 5987编码的文件名
            const utf8FilenameRegex = /filename\*=(?:UTF-8'')?([^;]+)/i;
            const matches = utf8FilenameRegex.exec(disposition);
            if (matches && matches[1]) {
                return decodeURIComponent(matches[1]);
            }
            
            // 处理普通文件名
            const filenameRegex = /filename="?([^;"]+)"?/i;
            const normalMatches = filenameRegex.exec(disposition);
            if (normalMatches && normalMatches[1]) {
                return normalMatches[1].replace(/['"]/g, '');
            }
        }
        return null;
    }
}
</script>
<!--<script>
             //禁止鼠标右击
      document.oncontextmenu = function() {
        event.returnValue = false;
      };
      //禁用开发者工具F12
      document.onkeydown = document.onkeyup = document.onkeypress = function(event) {
        let e = event || window.event || arguments.callee.caller.arguments[0];
        if (e && e.keyCode == 123) {
          e.returnValue = false;
          return false;
        }
      };
      let userAgent = navigator.userAgent;
      if (userAgent.indexOf("Firefox") > -1) {
        let checkStatus;
        let devtools = /./;
        devtools.toString = function() {
          checkStatus = "on";
        };
        setInterval(function() {
          checkStatus = "off";
          console.log(devtools);
          console.log(checkStatus);
          console.clear();
          if (checkStatus === "on") {
            let target = "";
            try {
              window.open("about:blank", (target = "_self"));
            } catch (err) {
              let a = document.createElement("button");
              a.onclick = function() {
                window.open("about:blank", (target = "_self"));
              };
              a.click();
            }
          }
        }, 200);
      } else {
        //禁用控制台
        let ConsoleManager = {
          onOpen: function() {
            alert("Console is opened");
          },
          onClose: function() {
            alert("Console is closed");
          },
          init: function() {
            let self = this;
            let x = document.createElement("div");
            let isOpening = false,
              isOpened = false;
            Object.defineProperty(x, "id", {
              get: function() {
                if (!isOpening) {
                  self.onOpen();
                  isOpening = true;
                }
                isOpened = true;
                return true;
              }
            });
            setInterval(function() {
              isOpened = false;
              console.info(x);
              console.clear();
              if (!isOpened && isOpening) {
                self.onClose();
                isOpening = false;
              }
            }, 200);
          }
        };
        ConsoleManager.onOpen = function() {
          //打开控制台，跳转
          let target = "";
          try {
            window.open("about:blank", (target = "_self"));
          } catch (err) {
            let a = document.createElement("button");
            a.onclick = function() {
              window.open("about:blank", (target = "_self"));
            };
            a.click();
          }
        };
        ConsoleManager.onClose = function() {
          alert("Console is closed!!!!!");
        };
        ConsoleManager.init();
      }
        </script>-->