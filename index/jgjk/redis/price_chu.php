<?php
include('../confing/common.php');
$redis=new Redis();
$redis->connect("127.0.0.1","6379");


while(true){
    $price_id=$redis->lpop('price_id');
    if($price_id!=''){
        $a=$DB->get_row("select * from qingka_wangke_class where cid='{$price_id}}' limit 1");
        $jk=$DB->get_row("SELECT * FROM `qingka_wangke_huoyuan` WHERE `hid` = {$a['docking']} limit 1");
        $data=array("uid"=>$jk['user'],"key"=>$jk['pass'],"cid"=>$a['noun']);
        $url=$jk['url']."/api.php?act=getclass";
        $result=post($url,$data);
        $result=daddslashes(json_decode($result,true));
        if ($result['code']==1) {
            $price=$result['data'][0]['price'];
            $price=$price/2*1.1;//价格计算规则修改，此规则根据你的平台来修改,$price是上游价格变量这个不要动，对应计算符号：加+，减-，乘*，除/
            if (!$price) {
                echo("项目ID：[".$price_id."] \r\n 项目名称：【".$a['name']."] \r\n上游价格为空\r\n------------------------------------------------------\r\n");
            }else{
                $price=daddslashes(round($price,2));
                if ($a['price']!=$price) {//此为价格倍数不一致就修改，若想弄成只有涨价了才修改价格就将!=改为<即可
                    $sql=$DB->query("update qingka_wangke_class set `price`='{$price}', `queryplat`='{$a['docking']}', `getnoun`='{$a['noun']}' where `cid`='{$price_id}'");
                    $is=$DB->query("insert into qingka_wangke_uppricelog (cid,ptname,oldprice,newprice,date,state) values ('{$price_id}','{$a['name']}','{$a['price']}','{$price}','{$date}','系统自动更新' ) ");
                    if ($sql) {
                        echo("项目ID：[".$price_id."] \r\n项目名称：【".$a['name']."]\r\n价格倍数：".$a['price']." ------ ".$price."\r\n------------------------------------------------------\r\n");
                    } else {
                        echo("项目ID：[".$price_id."] \r\n项目名称：【".$a['name']."]\r\n数据库修改数据失败\r\n------------------------------------------------------\r\n");
                    }
                }
            }
        } else {
            echo("项目ID：[".$price_id."] \r\n 项目名称：【".$a['name']."] \r\n上游没有此商品\r\n------------------------------------------------------\r\n");
        }
    }
    sleep(1);
}


?>