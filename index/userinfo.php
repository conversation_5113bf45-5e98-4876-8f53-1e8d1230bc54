<?php
include('head.php');
include('../confing/order.php');
?>

<!-- 引入样式 -->
<link rel="stylesheet" href="/assets/element/index.css">
<link rel="stylesheet" href="../assets/css/font-awesome.min.css">

<style>
/* 简约风格个人中心样式 */
:root {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --border-light: #e4e7ed;
  --border-lighter: #f2f6fc;
  --bg-light: #fafafa;
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-base: 0 1px 6px rgba(0, 0, 0, 0.1);
  --border-radius: 6px;
  --border-radius-small: 4px;
  --spacing-xs: 8px;
  --spacing-sm: 12px;
  --spacing-md: 16px;
  --spacing-lg: 20px;
  --spacing-xl: 24px;
}

.user-container {
  padding: var(--spacing-md);
  max-width: 1200px;
  margin: 0 auto;
  background: #ffffff;
  min-height: auto;
}

/* 顶部横幅样式 - 简约风格 */
.user-banner {
  background: #ffffff;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  box-shadow: none;
}

.user-header {
  display: flex;
  align-items: center;
}

.avatar-container {
  margin-right: var(--spacing-xl);
  text-align: center;
  position: relative;
}

.user-avatar {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  border: 2px solid var(--border-light);
  box-shadow: none;
  transition: none;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.avatar-badge {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: var(--success-color);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  border: 2px solid white;
}

.user-info {
  flex: 1;
  color: var(--text-primary);
}

.user-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.user-subtitle {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.user-stats {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--bg-light);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border-lighter);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  display: block;
  color: var(--primary-color);
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.button-group {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
  margin-top: var(--spacing-md);
}

/* 卡片网格布局 - 简约风格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.info-card {
  background: white;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  overflow: hidden;
}

.info-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-base);
}

.card-header {
  padding: var(--spacing-sm) var(--spacing-md);
  background: #fafbfc;
  border-bottom: 1px solid var(--border-light);
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 14px;
}

.card-icon {
  width: 16px;
  height: 16px;
  color: var(--primary-color);
}

.card-body {
  padding: var(--spacing-md);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.info-value {
  font-size: 15px;
  color: var(--text-primary);
  font-weight: 500;
  word-break: break-all;
  text-align: right;
  max-width: 60%;
}

/* 通知设置样式 */
.notify-settings {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.notify-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0;
}

.notify-label {
  font-size: 14px;
  color: var(--text-regular);
}

/* 公告样式 - 简约风格 */
.notice-section {
  margin-bottom: var(--spacing-lg);
}

.notice-card {
  background: #fffbf0;
  border: 1px solid #ffd591;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.notice-header {
  background: #fff7e6;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid #ffd591;
  font-weight: 500;
  color: #d48806;
  font-size: 15px;
}

.notice-content {
  padding: var(--spacing-lg);
  white-space: pre-wrap;
  line-height: 1.6;
  color: #d48806;
}

/* 充值区域样式 */
.recharge-section {
  margin-top: var(--spacing-lg);
}

.recharge-form {
  max-width: 600px;
  margin: 0 auto;
}

.payment-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: var(--spacing-sm);
  margin: var(--spacing-md) 0;
}

.payment-method-btn {
  height: 48px;
  border-radius: var(--border-radius-small);
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 订单表格样式 - 简约风格 */
.order-section {
  margin-top: var(--spacing-lg);
}

.order-table-card {
  background: white;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.table-responsive {
  border-radius: var(--border-radius);
}

/* 分页样式 */
.pagination-container {
  margin-top: var(--spacing-lg);
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .info-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .user-container {
    padding: var(--spacing-md);
  }

  .user-header {
    flex-direction: column;
    text-align: center;
  }

  .avatar-container {
    margin: 0 0 var(--spacing-lg) 0;
  }

  .user-stats {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .stat-item {
    padding: var(--spacing-xs);
  }

  .button-group {
    justify-content: center;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-value {
    max-width: 50%;
    font-size: 14px;
  }

  .payment-methods {
    grid-template-columns: 1fr 1fr;
  }

  .user-title {
    font-size: 20px;
  }

  .stat-value {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .user-container {
    padding: var(--spacing-sm);
  }

  .user-banner {
    padding: var(--spacing-md);
  }

  .user-avatar {
    width: 80px;
    height: 80px;
  }

  .payment-methods {
    grid-template-columns: 1fr;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .info-value {
    max-width: 100%;
    text-align: left;
  }

  .user-stats {
    padding: var(--spacing-sm);
  }

  .stat-value {
    font-size: 16px;
  }
}

/* 简约动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-card {
  animation: fadeIn 0.4s ease forwards;
}

.info-card:nth-child(1) { animation-delay: 0.05s; }
.info-card:nth-child(2) { animation-delay: 0.1s; }
.info-card:nth-child(3) { animation-delay: 0.15s; }
.info-card:nth-child(4) { animation-delay: 0.2s; }

/* 简约深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #e4e7ed;
    --text-regular: #cfd3dc;
    --text-secondary: #a4a9b6;
    --border-light: #4c4d4f;
    --bg-light: #2d2f33;
  }

  .user-container {
    background: #1a1a1a;
  }

  .info-card {
    background: #2d2f33;
    color: var(--text-primary);
    border-color: var(--border-light);
  }

  .card-header {
    background: #3a3d42;
    border-bottom-color: var(--border-light);
  }

  .user-banner {
    background: #2d2f33;
    border-color: var(--border-light);
  }
}
</style>

<div id="userindex" class="user-container">
  <!-- 上级公告 -->
  <div class="notice-section" v-if="row && row.sjnotice">
    <div class="notice-card">
      <div class="notice-header">
        <i class="el-icon-bell"></i> 上级公告
      </div>
      <div class="notice-content">{{row.sjnotice}}</div>
    </div>
  </div>

  <!-- 用户横幅 -->
  <div class="user-banner">
    <div class="user-header">
      <div class="avatar-container">
        <img class="user-avatar" :src="'http://q2.qlogo.cn/headimg_dl?dst_uin=' + (row ? row.user : '') + '&spec=100'" alt="用户头像">
        <div class="avatar-badge" v-if="row && row.key != 0">
          <i class="el-icon-check"></i>
        </div>
      </div>

      <div class="user-info">
        <h1 class="user-title">{{row ? row.name : '<?=$conf['sitename']?>'}}</h1>
        <div class="user-subtitle" v-if="row">UID: {{row.uid}} | {{row.user}}</div>

        <!-- 统计数据 -->
        <div class="user-stats" v-if="row">
          <div class="stat-item">
            <span class="stat-value">¥{{row.money}}</span>
            <span class="stat-label">账户余额</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{row.dd}}</span>
            <span class="stat-label">我的订单</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">¥{{row.zcz || '0'}}</span>
            <span class="stat-label">总充值</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{row.addprice}}</span>
            <span class="stat-label">我的费率</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="button-group" v-if="row">
          <template v-if="row.key == 0">
            <el-button type="success" size="small" @click="ktapi" icon="el-icon-key">开通KEY</el-button>
          </template>
          <template v-else>
            <el-button type="info" size="small" @click="ghapi" icon="el-icon-refresh">更换KEY</el-button>
            <el-button type="warning" size="small" @click="gbapi" icon="el-icon-close">关闭KEY</el-button>
          </template>
          <el-button type="primary" size="small" @click="szyqprice" icon="el-icon-setting">设置邀请费率</el-button>
          <el-button type="primary" size="small" @click="szgg" icon="el-icon-bell">设置下级公告</el-button>
          <el-button type="primary" size="small" @click="szyx(row.email, row.wxpushertoken)" icon="el-icon-message">设置推送</el-button>
        </div>
      </div>
    </div>
  </div>

  <!-- 信息卡片网格 -->
  <div class="info-grid" v-if="row">
    <!-- 基本信息（合并：账户信息 + 代理信息） -->
    <div class="info-card">
      <div class="card-header">
        <i class="el-icon-user card-icon"></i>
        <span>基本信息</span>
      </div>
      <div class="card-body">
        <!-- 账户信息 -->
        <div class="info-item">
          <span class="info-label">用户名</span>
          <span class="info-value">{{row.name}}</span>
        </div>
        <div class="info-item">
          <span class="info-label">账号</span>
          <span class="info-value">{{row.user}}</span>
        </div>
        <div class="info-item">
          <span class="info-label">邮箱</span>
          <span class="info-value">{{row.email || '未设置'}}</span>
        </div>
        <div class="info-item">
          <span class="info-label">推送Token</span>
          <span class="info-value">{{row.wxpushertoken ? (row.wxpushertoken.substring(0, 20) + '...') : '未设置'}}</span>
        </div>
        <div class="info-item">
          <span class="info-label">API状态</span>
          <span class="info-value">
            <el-tag :type="row.key == 0 ? 'danger' : 'success'" size="mini">
              {{row.key == 0 ? '未开通' : '已开通'}}
            </el-tag>
          </span>
        </div>
        <!-- 代理信息 -->
        <div class="info-item">
          <span class="info-label">邀请码</span>
          <span class="info-value">
            <el-tag v-if="row.yqm" type="success" size="medium">{{row.yqm}}</el-tag>
            <span v-else style="color: #909399;">未设置</span>
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">邀请费率</span>
          <span class="info-value">
            <el-tag v-if="row.yqprice" type="warning" size="medium">{{row.yqprice}}</el-tag>
            <span v-else style="color: #909399;">未设置</span>
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">API KEY</span>
          <span class="info-value">
            <div style="display: flex; align-items: center; gap: 8px;">
              <span v-if="row.key == '0'" style="color: #909399;">未开通</span>
              <el-tag v-else type="primary" size="medium">{{row.key.substring(0, 8) + '...' + row.key.substring(row.key.length - 4)}}</el-tag>
              <el-button
                v-if="row.key && row.key != '0'"
                type="text"
                size="mini"
                @click="copyApiKey"
                icon="el-icon-document-copy">
              </el-button>
            </div>
          </span>
        </div>
      </div>
    </div>

    <!-- 通知与统计（合并：通知设置 + 统计信息） -->
    <div class="info-card">
      <div class="card-header">
        <i class="el-icon-bell card-icon"></i>
        <span>通知与统计</span>
      </div>
      <div class="card-body">
        <!-- 通知设置 -->
        <div class="notify-settings" style="margin-bottom: 8px;">
          <div class="notify-item">
            <span class="notify-label">余额提醒</span>
            <el-switch
              v-model="notifySettings.balanceAlert"
              @change="updateNotifySettings">
            </el-switch>
          </div>
          <div class="notify-item" v-if="notifySettings.balanceAlert">
            <span class="notify-label">提醒阈值</span>
            <el-input-number
              v-model="notifySettings.balanceThreshold"
              @change="updateNotifySettings"
              :min="1"
              :max="100000"
              size="mini"
              controls-position="right">
            </el-input-number>
          </div>
          <div class="notify-item">
            <span class="notify-label">订单异常通知</span>
            <el-switch
              v-model="notifySettings.orderAlert"
              @change="updateNotifySettings">
            </el-switch>
          </div>
          <div class="notify-item">
            <span class="notify-label">工单通知</span>
            <el-switch
              v-model="notifySettings.ticketAlert"
              @change="updateNotifySettings">
            </el-switch>
          </div>
          <div class="notify-item">
            <span class="notify-label">登录邮件通知</span>
            <el-switch
              v-model="notifySettings.loginEmailAlert"
              @change="updateNotifySettings">
            </el-switch>
      </div>
    </div>

        <!-- 统计信息 -->
        <div class="info-item">
          <span class="info-label">代理注册</span>
          <span class="info-value">
            <el-tag type="success" size="medium">{{row.dailitongji.dlzc || 0}}人</el-tag>
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">代理登录</span>
          <span class="info-value">
            <el-tag type="info" size="medium">{{row.dailitongji.dldl || 0}}人</el-tag>
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">代理总数</span>
          <span class="info-value">
            <el-tag type="primary" size="medium">{{row.dailitongji.dlzs || 0}}人</el-tag>
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">今日接单</span>
          <span class="info-value">
            <el-tag type="warning" size="medium">{{row.dailitongji.jrjd || 0}}单</el-tag>
          </span>
        </div>
      </div>
    </div>
  </div>




</div>

<!-- 引入必要的JS文件 -->
<script src="js/vue.min.js"></script>
<script src="js/vue-resource.min.js"></script>
<script src="/assets/element/index.js"></script>
<script src="layer/3.1.1/layer.js"></script>

<script>
var vm = new Vue({
  el: "#userindex",
  data: {
    row: null,
    inte: '',
    
    // 通知设置
    notifySettings: {
      balanceAlert: false, // 余额提醒通知
      balanceThreshold: 10, // 余额阈值，默认为10
      orderAlert: false,   // 订单异常通知
      ticketAlert: false,  // 工单通知
      loginEmailAlert: false // 登录邮件通知
    },
    
    loading: false,
    orders: [],
    currentPage: 1,
    pageSize: 10,
    total: 0
  },
  methods: {
    userinfo: function(){
      var load = layer.load(2);
      this.$http.post("/apisub.php?act=userinfo")
        .then(function(data){	
          layer.close(load);
          if(data.data.code==1){			                     	
            this.row = data.data;			             			                     
          }else{
            layer.alert(data.data.msg,{icon:2});
          }
        });	
    },
    yecz:function(){
      layer.alert('请联系您的上级QQ：'+this.row.sjuser+'，进行充值。（下级点充值，此处将显示您的QQ）',{icon:1,title:"温馨提示"});
    },
    ktapi: function(){
      var that = this; // 保存 Vue 实例的引用
      layer.confirm('后台剩余积分满300积分可免费开通，反之需花费10积分开通', {
        title:'温馨提示',
        icon:1,
        btn: ['确定','取消']
      }, function(){
        var load = layer.load(2);
        $.get("/apisub.php?act=ktapi&type=1", function(data) {
          layer.close(load);
          if(data.code == 1){			                     	
            layer.alert(data.msg, {
              icon:1,
              title:"温馨提示"
            }, function(){
              setTimeout(function(){
                window.location.href = "";
              });
            });							          				             			                     
          }else{
            layer.msg(data.msg,{icon:2});
          }
        });	
      });
    },
    ghapi: function(){
      var that = this;
      layer.confirm('确定更换key吗，更换之后原有的key将失效！', {
        title:'温馨提示',
        icon:1,
        btn: ['确定','取消']
      }, function(){
        var load = layer.load(2);
        $.get("/apisub.php?act=ktapi&type=3", function(data) {
          layer.close(load);
          if(data.code == 1){			                     	
            layer.alert(data.msg, {
              icon:1,
              title:"温馨提示"
            }, function(){
              setTimeout(function(){
                window.location.href = "";
              });
            });							          				             			                     
          }else{
            layer.msg(data.msg,{icon:2});
          }
        });	
      });
    },
    gbapi: function(){
      var that = this;
      layer.confirm('确定关闭key吗，关闭之后无法使用对接功能！', {
        title:'温馨提示',
        icon:1,
        btn: ['确定','取消']
      }, function(){
        var load = layer.load(2);
        $.get("/apisub.php?act=ktapi&type=4", function(data) {
          layer.close(load);
          if(data.code == 1){			                     	
            layer.alert(data.msg, {
              icon:1,
              title:"温馨提示"
            }, function(){
              setTimeout(function(){
                window.location.href = "";
              });
            });							          				             			                     
          }else{
            layer.msg(data.msg,{icon:2});
          }
        });	
      });
    },
    szyqprice:function(){		    	    	
      layer.prompt({title: '设置下级默认费率，首次自动生成邀请码', formType: 3}, function(yqprice, index){
        layer.close(index);
        var load=layer.load(2);
        $.post("/apisub.php?act=yqprice",{yqprice},function (data) {
          layer.close(load);
          if (data.code==1){
            vm.userinfo();  
            layer.alert(data.msg,{icon:1});
          }else{
            layer.msg(data.msg,{icon:2});
          }
        });		    		    
      });
    },
    connect_qq:function(){
      var ii = layer.load(0, {
        shade: [0.1, '#fff']
      });
      $.ajax({
        type: "POST",
        url: "../qq_login.php",
        data: {"type":'qq'},
        dataType: 'json',
        success: function(data) {
          layer.close(ii);
          if (data.code == 1) {
            window.location.href = data.url;
          } else {
            layer.alert(data.msg, {
              icon: 7
            });
          }
        }
      });
    },szgg:function(){
      layer.prompt({title: '设置代理公告，您的代理可看到', formType: 2}, function(notice, index){
        layer.close(index);
        var load=layer.load(2);
        $.post("/apisub.php?act=user_notice",{notice},function (data) {
          layer.close(load);
          if (data.code==1){
            vm.userinfo();  
            layer.msg(data.msg,{icon:1});
          }else{
            layer.msg(data.msg,{icon:2});
          }
        });		    		    
      });   	  	
    },szyx: function(existingEmail, existingToken) {
      // 创建一个自定义的HTML结构，用于输入邮箱和token，填入已有的值
      var content = `
        <div style="font-family: Arial, sans-serif; line-height: 1.5;">
          <div style="margin-bottom: 15px;">
            <label for="email" style="display: block; font-weight: bold;">邮箱:</label>
            <input type="email" id="email" placeholder="请输入邮箱" value="${existingEmail}" 
                   style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1); transition: border 0.3s;">
          </div>
          <div>
            <label for="token" style="display: block; font-weight: bold;">推送Token:</label>
            <input type="text" id="token" placeholder="请输入推送Token" value="${existingToken}" 
                   style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1); transition: border 0.3s;">
          </div>
          <div style="margin-top: 5px;">
            <button style="background-color: #f56c6c; color: white; border: none; border-radius: 4px; 
                           padding: 5px 8px; cursor: pointer; font-size: 12px; transition: background-color 0.3s;" 
                    onclick="window.open('https://wxpusher.zjiecode.com/api/qrcode/RwjGLMOPTYp35zSYQr0HxbCPrV9eU0wKVBXU1D5VVtya0cQXEJWPjqBdW3gKLifS.jpg', '_blank')">
                获取 Token
            </button>
          </div>

        </div>
      `;
    
      // 显示自定义输入框
      layer.open({
        title: '设置邮箱和Token',
        content: content,
        area: ['400px', '320px'], // 调整高度以容纳按钮
        btn: ['确定'],
        yes: function(index, layero) {
          // 获取输入框的值
          var email = layero.find('#email').val();
          var token = layero.find('#token').val();
    
          layer.close(index);
          var load = layer.load(2);
    
          // 发送POST请求，包含邮箱和token
          $.post("/apisub.php?act=user_email", { email: email, tuisongtoken: token }, function(data) {
            layer.close(load);
            if (data.code == 1) {
              vm.userinfo();
              layer.msg(data.msg, { icon: 1 });
            } else {
              layer.msg(data.msg, { icon: 2 });
            }
          });
        }
      });
    },




    // 已移除充值相关的订单加载逻辑
    copyApiKey() {
      if (this.row.key && this.row.key != '0') {
        const textarea = document.createElement('textarea');
        textarea.value = this.row.key;
        document.body.appendChild(textarea);
        textarea.select();
        try {
          document.execCommand('copy');
          this.$message({
            message: 'API KEY 已复制到剪贴板',
            type: 'success'
          });
        } catch (err) {
          this.$message({
            message: '复制失败，请手动复制',
            type: 'error'
          });
        }
        document.body.removeChild(textarea);
      }
    },
    updateNotifySettings() {
      var load = layer.load(2);
      
      // 直接发送对象，让jQuery处理序列化
      this.$http.post('/apisub.php?act=update_notify_settings', {
        balanceAlert: this.notifySettings.balanceAlert ? 1 : 0,
        balanceThreshold: this.notifySettings.balanceThreshold || 10, // 发送余额阈值
        orderAlert: this.notifySettings.orderAlert ? 1 : 0,
        ticketAlert: this.notifySettings.ticketAlert ? 1 : 0,
        loginEmailAlert: this.notifySettings.loginEmailAlert ? 1 : 0
      }, {
        emulateJSON: true
      }).then(response => {
        layer.close(load);
        if (response.data.code == 1) {
          layer.msg(response.data.msg, { icon: 1 });
        } else {
          layer.msg(response.data.msg, { icon: 2 });
          // 恢复旧的设置
          this.initNotifySettings();
        }
      }).catch(() => {
        layer.close(load);
        this.$message.error('更新通知设置失败');
        // 恢复旧的设置
        this.initNotifySettings();
      });
    },
    
    // 初始化通知设置
    initNotifySettings() {
      if (this.row && this.row.notify) {
        try {
          const notifyData = JSON.parse(this.row.notify);
          this.notifySettings = {
            balanceAlert: !!notifyData.balanceAlert,
            balanceThreshold: notifyData.balanceThreshold || 10, // 读取余额阈值，默认10
            orderAlert: !!notifyData.orderAlert,
            ticketAlert: !!notifyData.ticketAlert,
            loginEmailAlert: !!notifyData.loginEmailAlert
          };
        } catch (e) {
          console.error('解析通知设置失败:', e);
          // 使用默认设置（余额提醒需要用户主动开启）
          this.notifySettings = {
            balanceAlert: false,  // 默认关闭余额提醒，需要用户主动开启
            balanceThreshold: 10, // 默认阈值为10
            orderAlert: true,
            ticketAlert: true,
            loginEmailAlert: false // 默认关闭登录邮件通知，需要用户主动开启
          };
        }
      } else {
        // 默认设置（余额提醒需要用户主动开启）
        this.notifySettings = {
          balanceAlert: false,  // 默认关闭余额提醒，需要用户主动开启
          balanceThreshold: 10, // 默认阈值为10
          orderAlert: true,
          ticketAlert: true,
          loginEmailAlert: false // 默认关闭登录邮件通知，需要用户主动开启
        };
      }
    }
  },
  mounted() {
    this.userinfo();
  },
  watch: {
    row: {
      handler(newVal) {
        if (newVal) {
          // 初始化通知设置
          this.initNotifySettings();
        }
      },
      deep: true
    }
  }
});
</script>
