-- 实习盖章模块数据库安装脚本
-- 执行前请确保已备份数据库
--
-- 版本更新说明：
-- 1. 添加退款相关状态：refund_requested, refunded
-- 2. 添加退款原因字段：refund_reason
-- 3. 移除已交付状态：delivered（与已完成状态冲突）

-- 检查表是否存在，如果存在则跳过创建
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = DATABASE() AND table_name = 'FD_sxgz_orders');

-- 创建实习盖章订单表
SET @sql = IF(@table_exists = 0,
'CREATE TABLE `FD_sxgz_orders` (
  `order_id` int(11) NOT NULL AUTO_INCREMENT COMMENT \'订单ID\',
  `uid` int(11) NOT NULL COMMENT \'用户ID\',
  `order_no` varchar(32) NOT NULL COMMENT \'订单号\',

  -- 服务信息
  `service_type` enum(\'electronic\',\'mail\',\'both\') NOT NULL COMMENT \'服务类型：electronic=电子版，mail=邮寄，both=邮寄+电子版\',
  `company_id` int(11) NOT NULL COMMENT \'公司ID（关联qingka_wangke_class表）\',
  `company_name` varchar(255) NOT NULL COMMENT \'公司名称\',
  `business_license` tinyint(1) DEFAULT 0 COMMENT \'是否需要营业执照\',
  `only_business_license` tinyint(1) DEFAULT 0 COMMENT \'是否仅需要营业执照\',

  -- 材料信息
  `material_type` enum(\'upload\',\'mail\') DEFAULT NULL COMMENT \'材料方式：upload=在线上传，mail=邮寄纸质\',
  `uploaded_file` varchar(500) DEFAULT NULL COMMENT \'上传文件路径\',
  `original_filename` varchar(255) DEFAULT NULL COMMENT \'原始文件名\',
  `file_size` int(11) DEFAULT NULL COMMENT \'文件大小（字节）\',

  -- 客户信息
  `customer_name` varchar(100) NOT NULL COMMENT \'客户姓名\',
  `customer_email` varchar(255) DEFAULT NULL COMMENT \'客户邮箱\',
  `customer_phone` varchar(20) DEFAULT NULL COMMENT \'客户手机号\',
  `customer_address` text DEFAULT NULL COMMENT \'收货地址\',

  -- 快递信息（邮寄纸质材料时使用）
  `courier_company` varchar(50) DEFAULT NULL COMMENT \'快递公司\',
  `tracking_number` varchar(100) DEFAULT NULL COMMENT \'快递单号\',

  -- 打印选项
  `print_copies` int(11) DEFAULT 0 COMMENT \'打印章数\',
  `print_options` text DEFAULT NULL COMMENT \'打印选项（JSON格式）\',
  `special_requirements` text DEFAULT NULL COMMENT \'特殊要求说明\',

  -- 价格信息
  `base_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT \'基础费用\',
  `print_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT \'打印费用\',
  `license_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT \'营业执照费用\',
  `total_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT \'总价格\',

  -- 订单状态
  `status` enum(\'pending\',\'processing\',\'completed\',\'cancelled\',\'failed\',\'refund_requested\',\'refunded\') NOT NULL DEFAULT \'pending\' COMMENT \'订单状态\',
  `admin_notes` text DEFAULT NULL COMMENT \'管理员备注\',
  `refund_reason` text DEFAULT NULL COMMENT \'退款原因\',
  `processed_files` text DEFAULT NULL COMMENT \'已处理文件路径（JSON格式）\',
  `processed_file_url` varchar(500) DEFAULT NULL COMMENT \'处理后文件URL（用于代理商下载）\',

  -- 代理商相关字段
  `source` enum(\'direct\',\'agent\') NOT NULL DEFAULT \'direct\' COMMENT \'订单来源：direct=直接下单，agent=代理商订单\',
  `agent_uid` int(11) DEFAULT NULL COMMENT \'代理商用户ID（如果是代理商订单）\',
  `agent_order_id` int(11) DEFAULT NULL COMMENT \'代理商端的订单ID\',

  -- 时间信息
  `created_at` datetime NOT NULL COMMENT \'创建时间\',
  `updated_at` datetime DEFAULT NULL COMMENT \'更新时间\',
  `completed_at` datetime DEFAULT NULL COMMENT \'完成时间\',

  -- 邮件发送记录
  `order_email_sent` tinyint(1) DEFAULT 0 COMMENT \'下单邮件是否已发送\',
  `completion_email_sent` tinyint(1) DEFAULT 0 COMMENT \'完成邮件是否已发送\',

  PRIMARY KEY (`order_id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `uid` (`uid`),
  KEY `company_id` (`company_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`),
  KEY `source` (`source`),
  KEY `agent_uid` (`agent_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=\'实习盖章订单表\'',
'SELECT \'Table FD_sxgz_orders already exists\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;



-- 检查邮件队列表是否存在
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = DATABASE() AND table_name = 'FD_sxgz_email_queue');

-- 创建实习盖章邮件队列表
SET @sql = IF(@table_exists = 0,
'CREATE TABLE `FD_sxgz_email_queue` (
  `queue_id` int(11) NOT NULL AUTO_INCREMENT COMMENT \'队列ID\',
  `order_id` int(11) NOT NULL COMMENT \'订单ID\',
  `email_type` enum(\'order_confirmation\',\'completion_notice\') NOT NULL COMMENT \'邮件类型\',
  `recipient_email` varchar(255) NOT NULL COMMENT \'收件人邮箱\',
  `recipient_name` varchar(100) NOT NULL COMMENT \'收件人姓名\',
  `email_data` text DEFAULT NULL COMMENT \'邮件数据（JSON格式）\',
  `status` enum(\'pending\',\'sent\',\'failed\') NOT NULL DEFAULT \'pending\' COMMENT \'发送状态\',
  `attempts` int(11) DEFAULT 0 COMMENT \'尝试次数\',
  `error_message` text DEFAULT NULL COMMENT \'错误信息\',
  `created_at` datetime NOT NULL COMMENT \'创建时间\',
  `sent_at` datetime DEFAULT NULL COMMENT \'发送时间\',

  PRIMARY KEY (`queue_id`),
  KEY `order_id` (`order_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=\'实习盖章邮件队列表\'',
'SELECT \'Table FD_sxgz_email_queue already exists\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 首先确保实习盖章分类存在
SET @category_exists = (SELECT COUNT(*) FROM qingka_wangke_fenlei WHERE name = '实习盖章');

SET @sql = IF(@category_exists = 0,
'INSERT INTO qingka_wangke_fenlei (sort, name, status, time) VALUES (10, \'实习盖章\', 1, UNIX_TIMESTAMP())',
'SELECT \'Category already exists\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 获取实习盖章分类的ID
SET @sxgz_category_id = (SELECT id FROM qingka_wangke_fenlei WHERE name = '实习盖章' LIMIT 1);

-- 检查是否已存在实习盖章相关的公司数据
SET @company_exists = (SELECT COUNT(*) FROM qingka_wangke_class WHERE fenlei = @sxgz_category_id);

-- 插入示例公司数据（如果不存在）
SET @sql = IF(@company_exists = 0,
CONCAT('INSERT INTO qingka_wangke_class (name, getnoun, noun, price, queryplat, docking, fenlei, fenlei1, status, sort, content, addtime) VALUES
(\'营业执照专用公司\', \'sxgz_license\', \'sxgz_license\', \'100.00\', \'sxgz\', \'sxgz\', \'', @sxgz_category_id, '\', \'0\', 1, 1, \'提供营业执照盖章服务\', UNIX_TIMESTAMP()),
(\'实习盖章公司A\', \'sxgz_company_a\', \'sxgz_company_a\', \'50.00\', \'sxgz\', \'sxgz\', \'', @sxgz_category_id, '\', \'0\', 1, 2, \'提供实习证明盖章服务\', UNIX_TIMESTAMP()),
(\'实习盖章公司B\', \'sxgz_company_b\', \'sxgz_company_b\', \'60.00\', \'sxgz\', \'sxgz\', \'', @sxgz_category_id, '\', \'0\', 1, 3, \'提供实习证明盖章服务\', UNIX_TIMESTAMP()),
(\'实习盖章公司C\', \'sxgz_company_c\', \'sxgz_company_c\', \'55.00\', \'sxgz\', \'sxgz\', \'', @sxgz_category_id, '\', \'0\', 1, 4, \'提供实习证明盖章服务\', UNIX_TIMESTAMP())'),
'SELECT \'Company data already exists\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 确保营业执照专用公司的ID为25709（如果不存在）
SET @license_company_exists = (SELECT COUNT(*) FROM qingka_wangke_class WHERE cid = 25709);

SET @sql = IF(@license_company_exists = 0,
CONCAT('INSERT INTO qingka_wangke_class (cid, name, getnoun, noun, price, queryplat, docking, fenlei, fenlei1, status, sort, content, addtime) VALUES
(25709, \'营业执照专用公司\', \'sxgz_license_25709\', \'sxgz_license_25709\', \'100.00\', \'sxgz\', \'sxgz\', \'', @sxgz_category_id, '\', \'0\', 1, 1, \'提供营业执照盖章服务\', UNIX_TIMESTAMP())
ON DUPLICATE KEY UPDATE
name = \'营业执照专用公司\',
fenlei = \'', @sxgz_category_id, '\',
status = 1'),
'SELECT \'License company with ID 25709 already exists\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示安装结果
SELECT 
    'FD_sxgz_orders' as table_name,
    CASE WHEN COUNT(*) > 0 THEN 'EXISTS' ELSE 'NOT FOUND' END as status
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'FD_sxgz_orders'

UNION ALL



SELECT 
    'FD_sxgz_email_queue' as table_name,
    CASE WHEN COUNT(*) > 0 THEN 'EXISTS' ELSE 'NOT FOUND' END as status
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'FD_sxgz_email_queue'

UNION ALL

SELECT
    'sxgz_companies' as table_name,
    CONCAT(COUNT(*), ' companies found') as status
FROM qingka_wangke_class
WHERE fenlei = (SELECT id FROM qingka_wangke_fenlei WHERE name = '实习盖章' LIMIT 1);

-- 性能优化：添加索引（使用兼容语法）
-- 为FD_sxgz_orders表添加索引
SET @sql = 'ALTER TABLE FD_sxgz_orders ADD INDEX idx_uid_status (uid, status)';
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_schema = DATABASE() AND table_name = 'FD_sxgz_orders' AND index_name = 'idx_uid_status') = 0, @sql, 'SELECT "Index idx_uid_status already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE FD_sxgz_orders ADD INDEX idx_status_created (status, created_at)';
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_schema = DATABASE() AND table_name = 'FD_sxgz_orders' AND index_name = 'idx_status_created') = 0, @sql, 'SELECT "Index idx_status_created already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE FD_sxgz_orders ADD INDEX idx_order_no (order_no)';
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_schema = DATABASE() AND table_name = 'FD_sxgz_orders' AND index_name = 'idx_order_no') = 0, @sql, 'SELECT "Index idx_order_no already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 为FD_sxgz_email_queue表添加索引
SET @sql = 'ALTER TABLE FD_sxgz_email_queue ADD INDEX idx_status_created (status, created_at)';
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_schema = DATABASE() AND table_name = 'FD_sxgz_email_queue' AND index_name = 'idx_status_created') = 0, @sql, 'SELECT "Index idx_status_created already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE FD_sxgz_email_queue ADD INDEX idx_order_id (order_id)';
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_schema = DATABASE() AND table_name = 'FD_sxgz_email_queue' AND index_name = 'idx_order_id') = 0, @sql, 'SELECT "Index idx_order_id already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;



-- 为qingka_wangke_class表添加索引
SET @sql = 'ALTER TABLE qingka_wangke_class ADD INDEX idx_fenlei_status (fenlei, status)';
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_schema = DATABASE() AND table_name = 'qingka_wangke_class' AND index_name = 'idx_fenlei_status') = 0, @sql, 'SELECT "Index idx_fenlei_status already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 分析表以更新统计信息
ANALYZE TABLE FD_sxgz_orders;
ANALYZE TABLE FD_sxgz_email_queue;

-- 现有数据库升级脚本（如果表已存在）
-- 检查是否需要升级status字段
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                      WHERE table_schema = DATABASE()
                      AND table_name = 'FD_sxgz_orders'
                      AND column_name = 'status'
                      AND column_type LIKE '%refund_requested%');

-- 如果status字段不包含退款状态，则升级
SET @sql = IF(@column_exists = 0,
'ALTER TABLE FD_sxgz_orders
MODIFY COLUMN `status` enum(\'pending\',\'processing\',\'completed\',\'cancelled\',\'failed\',\'refund_requested\',\'refunded\') NOT NULL DEFAULT \'pending\' COMMENT \'订单状态\'',
'SELECT \'Status column already upgraded\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查是否需要添加refund_reason字段
SET @refund_reason_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                             WHERE table_schema = DATABASE()
                             AND table_name = 'FD_sxgz_orders'
                             AND column_name = 'refund_reason');

-- 如果refund_reason字段不存在，则添加
SET @sql = IF(@refund_reason_exists = 0,
'ALTER TABLE FD_sxgz_orders
ADD COLUMN `refund_reason` text DEFAULT NULL COMMENT \'退款原因\' AFTER `admin_notes`',
'SELECT \'Refund reason column already exists\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查是否需要添加代理商相关字段
SET @source_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                      WHERE table_schema = DATABASE()
                      AND table_name = 'FD_sxgz_orders'
                      AND column_name = 'source');

-- 如果source字段不存在，则添加
SET @sql = IF(@source_exists = 0,
'ALTER TABLE FD_sxgz_orders
ADD COLUMN `source` enum(\'direct\',\'agent\') NOT NULL DEFAULT \'direct\' COMMENT \'订单来源：direct=直接下单，agent=代理商订单\' AFTER `processed_files`,
ADD COLUMN `agent_uid` int(11) DEFAULT NULL COMMENT \'代理商用户ID（如果是代理商订单）\' AFTER `source`,
ADD COLUMN `agent_order_id` int(11) DEFAULT NULL COMMENT \'代理商端的订单ID\' AFTER `agent_uid`,
ADD INDEX `source` (`source`),
ADD INDEX `agent_uid` (`agent_uid`)',
'SELECT \'Agent fields already exist\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;



-- 检查是否需要添加processed_file_url字段
SET @processed_file_url_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                                  WHERE table_schema = DATABASE()
                                  AND table_name = 'FD_sxgz_orders'
                                  AND column_name = 'processed_file_url');

-- 如果processed_file_url字段不存在，则添加
SET @sql = IF(@processed_file_url_exists = 0,
'ALTER TABLE FD_sxgz_orders
ADD COLUMN `processed_file_url` varchar(500) DEFAULT NULL COMMENT \'处理后文件URL（用于代理商下载）\' AFTER `processed_files`',
'SELECT \'Processed file URL column already exists\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 安装完成提示
SELECT '实习盖章模块数据库安装/升级完成！索引已优化！代理商功能已启用！' as message;
