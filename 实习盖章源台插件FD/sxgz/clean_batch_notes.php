<?php
/**
 * 清理管理员备注中的批量操作记录
 * 将批量操作记录从admin_notes中移除，这些记录应该通过wlog记录到日志中
 */

require_once('../config.php');

// 验证管理员权限
if (!isset($userrow) || $userrow['uid'] != 1) {
    die('权限不足，只有管理员可以执行此操作');
}

// 获取所有包含批量操作记录的订单
$orders = $DB->query("SELECT order_id, admin_notes FROM FD_sxgz_orders WHERE admin_notes LIKE '%批量%操作%'");

$cleanedCount = 0;
$totalCount = 0;

echo "<h2>清理管理员备注中的批量操作记录</h2>\n";
echo "<p>开始处理...</p>\n";

while ($order = $DB->fetch_array($orders)) {
    $totalCount++;
    $orderId = $order['order_id'];
    $originalNotes = $order['admin_notes'];
    
    // 分割备注为行
    $lines = explode("\n", $originalNotes);
    $cleanedLines = [];
    $removedLines = [];
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // 检查是否是批量操作记录
        if (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}: 批量.*操作$/', $line)) {
            $removedLines[] = $line;
        } else {
            $cleanedLines[] = $line;
        }
    }
    
    // 如果有移除的行，更新数据库
    if (!empty($removedLines)) {
        $cleanedNotes = implode("\n", $cleanedLines);
        $cleanedNotes = trim($cleanedNotes);
        
        // 如果清理后为空，设置为NULL
        if (empty($cleanedNotes)) {
            $updateSql = "UPDATE FD_sxgz_orders SET admin_notes = NULL WHERE order_id = '{$orderId}'";
        } else {
            $escapedNotes = $DB->escape($cleanedNotes);
            $updateSql = "UPDATE FD_sxgz_orders SET admin_notes = '{$escapedNotes}' WHERE order_id = '{$orderId}'";
        }
        
        if ($DB->query($updateSql)) {
            $cleanedCount++;
            echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;'>";
            echo "<strong>订单 {$orderId}</strong><br>";
            echo "<strong>移除的记录:</strong><br>";
            foreach ($removedLines as $removedLine) {
                echo "<span style='color: #f56c6c; text-decoration: line-through;'>{$removedLine}</span><br>";
            }
            if (!empty($cleanedNotes)) {
                echo "<strong>保留的备注:</strong><br>";
                echo "<span style='color: #67c23a;'>" . nl2br(htmlspecialchars($cleanedNotes)) . "</span>";
            } else {
                echo "<span style='color: #909399;'>备注已清空</span>";
            }
            echo "</div>";
        } else {
            echo "<div style='color: #f56c6c;'>订单 {$orderId} 更新失败</div>";
        }
    }
}

echo "<h3>处理完成</h3>";
echo "<p>总共检查了 {$totalCount} 个订单，清理了 {$cleanedCount} 个订单的批量操作记录。</p>";

if ($cleanedCount > 0) {
    echo "<div style='background: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #409eff; margin: 20px 0;'>";
    echo "<h4>说明</h4>";
    echo "<p>已成功清理管理员备注中的批量操作记录。今后的批量操作将通过wlog记录到系统日志中，不再写入订单备注。</p>";
    echo "<p><strong>清理的记录类型：</strong></p>";
    echo "<ul>";
    echo "<li>批量开始处理操作</li>";
    echo "<li>批量完成操作</li>";
    echo "<li>批量标记失败操作</li>";
    echo "<li>批量批准退款操作</li>";
    echo "<li>批量拒绝退款操作</li>";
    echo "</ul>";
    echo "</div>";
}

// 记录清理操作到日志
wlog("清理批量操作记录", "管理员清理了 {$cleanedCount} 个订单的批量操作记录，总共检查了 {$totalCount} 个订单");

echo "<p><a href='sxgz_admin.php'>返回管理员界面</a></p>";
?>
