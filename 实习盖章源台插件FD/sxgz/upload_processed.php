<?php
/**
 * 管理员上传处理后文件接口
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once('../confing/common.php');
require_once('file_manager.php');

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 验证管理员权限
if (!isset($userrow) || $userrow['uid'] != 1) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => '权限不足',
        'debug' => [
            'userrow_exists' => isset($userrow),
            'uid' => isset($userrow) ? $userrow['uid'] : 'not_set'
        ]
    ]);
    exit;
}

try {
    // 调试信息
    $debug = [
        'files_received' => isset($_FILES['file']),
        'order_id_received' => isset($_POST['order_id']),
        'post_data' => $_POST,
        'files_data' => isset($_FILES['file']) ? [
            'name' => $_FILES['file']['name'],
            'size' => $_FILES['file']['size'],
            'error' => $_FILES['file']['error']
        ] : null,
        'base_dir' => __DIR__,
        'processed_dir_exists' => is_dir(__DIR__ . '/processed/'),
        'processed_dir_writable' => is_writable(__DIR__ . '/processed/'),
        'processed_dir_perms' => is_dir(__DIR__ . '/processed/') ? substr(sprintf('%o', fileperms(__DIR__ . '/processed/')), -4) : 'not_exists'
    ];

    if (!isset($_FILES['file']) || !isset($_POST['order_id'])) {
        throw new Exception('缺少必要参数: ' . json_encode($debug), 400);
    }

    $orderId = $_POST['order_id'];
    $file = $_FILES['file'];

    // 验证文件上传错误
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('文件上传错误，错误代码: ' . $file['error'], 400);
    }

    // 验证订单存在
    $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}'");
    if (!$order) {
        throw new Exception('订单不存在: ' . $orderId, 404);
    }
    
    // 验证文件大小（20MB）
    if ($file['size'] > 20 * 1024 * 1024) {
        throw new Exception('文件大小不能超过20MB', 400);
    }

    // 验证文件类型
    $allowedExts = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'zip', 'rar', '7z'];
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if (!in_array($fileExtension, $allowedExts)) {
        throw new Exception('不支持的文件类型，只支持：' . implode(', ', $allowedExts), 400);
    }

    // 创建处理后文件目录 - 使用带标识的目录结构
    $baseProcessedDir = __DIR__ . '/processed/';
    $userDir = $baseProcessedDir . 'uid_' . $order['uid'] . '/';
    $processedDir = $userDir . 'orderid_' . $orderId . '/';

    // 检查基础目录权限
    if (!is_writable($baseProcessedDir)) {
        throw new Exception('基础目录不可写: ' . $baseProcessedDir . ' (权限: ' . substr(sprintf('%o', fileperms($baseProcessedDir)), -4) . ')', 500);
    }

    // 逐级创建目录
    if (!is_dir($userDir)) {
        if (!mkdir($userDir, 0755, false)) {
            throw new Exception('无法创建用户目录: ' . $userDir . ' (错误: ' . error_get_last()['message'] . ')', 500);
        }
    }

    if (!is_dir($processedDir)) {
        if (!mkdir($processedDir, 0755, false)) {
            throw new Exception('无法创建订单目录: ' . $processedDir . ' (错误: ' . error_get_last()['message'] . ')', 500);
        }
    }

    // 验证目录可写
    if (!is_writable($processedDir)) {
        throw new Exception('目录不可写: ' . $processedDir . ' (权限: ' . substr(sprintf('%o', fileperms($processedDir)), -4) . ')', 500);
    }

    // 生成安全的文件名 - 使用新的命名规范
    $originalBaseName = pathinfo($file['name'], PATHINFO_FILENAME);
    $safeBaseName = preg_replace('/[^a-zA-Z0-9\-_\u4e00-\u9fa5]/u', '_', $originalBaseName);
    $safeFileName = 'processed_' . $order['uid'] . '_' . $orderId . '_' . time() . '_' . $safeBaseName . '.' . $fileExtension;
    $targetPath = $processedDir . $safeFileName;
    
    if (move_uploaded_file($file['tmp_name'], $targetPath)) {
        // 更新订单的已处理文件记录
        $processedFiles = [];
        if (!empty($order['processed_files'])) {
            $processedFiles = json_decode($order['processed_files'], true) ?: [];
        }
        
        $processedFiles[] = [
            'filename' => $safeFileName,
            'original_name' => $file['name'],
            'upload_time' => date('Y-m-d H:i:s'),
            'operator' => $userrow['user']
        ];
        
        $processedFilesJson = json_encode($processedFiles);
        $DB->query("UPDATE FD_sxgz_orders SET 
            processed_files = '{$processedFilesJson}',
            updated_at = NOW()
            WHERE order_id = '{$orderId}'");
        
        // 记录操作日志
        wlog($userrow['uid'], '上传处理文件', "订单: {$orderId}, 文件: {$file['name']}", 0);
        
        // 生成下载URL
        $downloadUrl = '/sxgz/download.php?uid=' . $order['uid'] . '&order_id=' . $orderId . '&file=' . urlencode($safeFileName) . '&type=processed';

        // 生成包含token的完整文件URL用于代理商下载
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $token = md5($orderId . 'sxgz_download' . date('Y-m-d'));
        $fullDownloadUrl = $protocol . '://' . $host . $downloadUrl . '&token=' . $token;

        // 更新processed_file_url字段，供代理商下载
        $DB->query("UPDATE FD_sxgz_orders SET
            processed_file_url = '{$DB->escape($fullDownloadUrl)}'
            WHERE order_id = '{$orderId}'");

        echo json_encode([
            'success' => true,
            'message' => '文件上传成功',
            'data' => [
                'filename' => $safeFileName,
                'original_name' => $file['name'],
                'file_size' => $file['size'],
                'download_url' => $downloadUrl,
                'full_download_url' => $fullDownloadUrl,
                'file_path' => $targetPath
            ]
        ]);
    } else {
        throw new Exception('文件保存失败，请检查目录权限: ' . $processedDir, 500);
    }
    
} catch (Exception $e) {
    http_response_code($e->getCode() ?: 500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'error_code' => $e->getCode(),
            'error_line' => $e->getLine(),
            'error_file' => basename($e->getFile()),
            'php_version' => PHP_VERSION,
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'max_execution_time' => ini_get('max_execution_time')
        ]
    ]);
}
?>
