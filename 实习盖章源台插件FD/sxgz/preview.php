<?php
/**
 * 实习盖章文件预览接口
 * 提供安全的文件在线预览功能
 */

require_once('../confing/common.php');

// 验证用户登录
if (!isset($userrow) || !$userrow) {
    http_response_code(401);
    die('用户未登录');
}

$uid = $_GET['uid'] ?? '';
$orderId = $_GET['order_id'] ?? '';
$filename = $_GET['file'] ?? '';
$type = $_GET['type'] ?? 'upload'; // upload 或 processed

if (empty($uid) || empty($orderId) || empty($filename)) {
    http_response_code(400);
    die('参数错误');
}

// 验证订单存在
$order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}' AND uid = '{$uid}'");
if (!$order) {
    http_response_code(404);
    die('订单不存在');
}

// 权限验证：用户只能预览自己的订单文件，管理员可以预览所有文件
if ($userrow['uid'] != 1 && $order['uid'] != $userrow['uid']) {
    http_response_code(403);
    die('权限不足');
}

// 构建文件路径 - 只支持带标识的目录结构：uid_X/orderid_Y/filename
$baseDir = __DIR__ . '/';

if ($type === 'processed') {
    $filePath = $baseDir . 'processed/uid_' . $uid . '/orderid_' . $orderId . '/' . $filename;
} else {
    $filePath = $baseDir . 'uploads/uid_' . $uid . '/orderid_' . $orderId . '/' . $filename;
}

// 验证文件存在
if (!file_exists($filePath) || !is_file($filePath)) {
    http_response_code(404);
    die('文件不存在');
}

// 验证文件在允许的目录内（防止目录遍历攻击）
$realPath = realpath($filePath);
$allowedDir = realpath($baseDir . ($type === 'processed' ? 'processed' : 'uploads'));

if (strpos($realPath, $allowedDir) !== 0) {
    http_response_code(403);
    die('非法访问');
}

// 获取文件信息
$fileExtension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

// 检查是否支持预览
$previewableTypes = ['pdf', 'jpg', 'jpeg', 'png', 'gif'];
if (!in_array($fileExtension, $previewableTypes)) {
    http_response_code(400);
    die('该文件类型不支持在线预览');
}

// 设置MIME类型
$mimeTypes = [
    'pdf' => 'application/pdf',
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif'
];

$mimeType = $mimeTypes[$fileExtension] ?? 'application/octet-stream';

// 设置预览头
header('Content-Type: ' . $mimeType);
header('Content-Length: ' . filesize($filePath));
header('Cache-Control: public, max-age=3600'); // 缓存1小时
header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');

// 对于PDF文件，设置内联显示
if ($fileExtension === 'pdf') {
    header('Content-Disposition: inline; filename="' . basename($filename) . '"');
} else {
    // 对于图片文件，直接显示
    header('Content-Disposition: inline');
}

// 清除输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

// 输出文件内容
readfile($filePath);

// 记录预览日志
wlog($userrow['uid'], '文件预览', "订单: {$orderId}, 文件: {$filename}, 类型: {$type}", 0);

exit;
?>
