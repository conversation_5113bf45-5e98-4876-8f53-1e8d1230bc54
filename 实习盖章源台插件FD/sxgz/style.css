/* 实习盖章模块专用样式 */

/* 基础样式重置 */
.sxgz-container {
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* 卡片样式 */
.sxgz-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #ebeef5;
    margin-bottom: 20px;
    overflow: hidden;
}

.sxgz-card-header {
    padding: 18px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
}

.sxgz-card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
}

.sxgz-card-body {
    padding: 20px;
}

/* 步骤条样式 */
.sxgz-steps {
    margin-bottom: 30px;
}

.sxgz-step {
    display: inline-block;
    position: relative;
    margin-right: 40px;
    padding: 10px 20px;
    background: #f5f7fa;
    border-radius: 20px;
    color: #909399;
    font-size: 14px;
    transition: all 0.3s;
}

.sxgz-step.active {
    background: #409eff;
    color: #fff;
}

.sxgz-step.completed {
    background: #67c23a;
    color: #fff;
}

.sxgz-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -20px;
    width: 0;
    height: 0;
    border-left: 8px solid #f5f7fa;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    transform: translateY(-50%);
    transition: all 0.3s;
}

.sxgz-step.active:not(:last-child)::after {
    border-left-color: #409eff;
}

.sxgz-step.completed:not(:last-child)::after {
    border-left-color: #67c23a;
}

/* 表单样式 */
.sxgz-form-group {
    margin-bottom: 20px;
}

.sxgz-form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #606266;
    font-size: 14px;
}

.sxgz-form-label.required::before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
}

.sxgz-input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
    box-sizing: border-box;
}

.sxgz-input:focus {
    outline: none;
    border-color: #409eff;
}

.sxgz-textarea {
    min-height: 80px;
    resize: vertical;
}

/* 选择框样式 */
.sxgz-select {
    position: relative;
    display: inline-block;
    width: 100%;
}

.sxgz-select-input {
    cursor: pointer;
    background-color: #fff;
}

/* 复选框和单选框样式 */
.sxgz-checkbox,
.sxgz-radio {
    display: inline-flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 10px;
    cursor: pointer;
    font-size: 14px;
    color: #606266;
}

.sxgz-checkbox input,
.sxgz-radio input {
    margin-right: 8px;
    transform: scale(1.2);
}

/* 按钮样式 */
.sxgz-btn {
    display: inline-block;
    padding: 12px 20px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    text-align: center;
    text-decoration: none;
    transition: all 0.3s;
    background: #fff;
    color: #606266;
    box-sizing: border-box;
}

.sxgz-btn:hover {
    color: #409eff;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
}

.sxgz-btn-primary {
    background: #409eff;
    border-color: #409eff;
    color: #fff;
}

.sxgz-btn-primary:hover {
    background: #66b1ff;
    border-color: #66b1ff;
    color: #fff;
}

.sxgz-btn-success {
    background: #67c23a;
    border-color: #67c23a;
    color: #fff;
}

.sxgz-btn-success:hover {
    background: #85ce61;
    border-color: #85ce61;
    color: #fff;
}

.sxgz-btn-warning {
    background: #e6a23c;
    border-color: #e6a23c;
    color: #fff;
}

.sxgz-btn-warning:hover {
    background: #ebb563;
    border-color: #ebb563;
    color: #fff;
}

.sxgz-btn-danger {
    background: #f56c6c;
    border-color: #f56c6c;
    color: #fff;
}

.sxgz-btn-danger:hover {
    background: #f78989;
    border-color: #f78989;
    color: #fff;
}

.sxgz-btn-small {
    padding: 8px 15px;
    font-size: 12px;
}

.sxgz-btn-large {
    padding: 15px 25px;
    font-size: 16px;
}

/* 文件上传样式 */
.sxgz-upload {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: border-color 0.3s;
    padding: 20px;
    text-align: center;
}

.sxgz-upload:hover {
    border-color: #409eff;
}

.sxgz-upload-icon {
    font-size: 28px;
    color: #c0c4cc;
    margin-bottom: 10px;
}

.sxgz-upload-text {
    color: #606266;
    font-size: 14px;
}

.sxgz-upload-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 7px;
}

/* 文件列表样式 */
.sxgz-file-list {
    margin-top: 15px;
}

.sxgz-file-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-bottom: 8px;
    background: #f9f9f9;
}

.sxgz-file-icon {
    margin-right: 10px;
    color: #409eff;
    font-size: 16px;
}

.sxgz-file-name {
    flex: 1;
    font-size: 14px;
    color: #606266;
}

.sxgz-file-size {
    margin-left: 10px;
    font-size: 12px;
    color: #909399;
}

.sxgz-file-actions {
    margin-left: 10px;
}

/* 价格显示样式 */
.sxgz-price {
    font-size: 18px;
    font-weight: bold;
    color: #f56c6c;
}

.sxgz-price-breakdown {
    font-size: 14px;
    color: #909399;
    margin-top: 5px;
}

/* 状态标签样式 */
.sxgz-tag {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
    color: #fff;
    font-weight: 500;
}

.sxgz-tag-pending {
    background: #e6a23c;
}

.sxgz-tag-processing {
    background: #409eff;
}

.sxgz-tag-completed {
    background: #67c23a;
}

.sxgz-tag-cancelled {
    background: #909399;
}

.sxgz-tag-failed {
    background: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sxgz-card-body {
        padding: 15px;
    }
    
    .sxgz-step {
        margin-right: 20px;
        padding: 8px 15px;
        font-size: 12px;
    }
    
    .sxgz-step:not(:last-child)::after {
        right: -10px;
        border-left-width: 6px;
        border-top-width: 6px;
        border-bottom-width: 6px;
    }
    
    .sxgz-btn {
        padding: 10px 15px;
        font-size: 13px;
    }
    
    .sxgz-checkbox,
    .sxgz-radio {
        margin-right: 15px;
        font-size: 13px;
    }
}

/* 动画效果 */
@keyframes sxgz-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.sxgz-fade-in {
    animation: sxgz-fade-in 0.3s ease-out;
}

/* 加载状态 */
.sxgz-loading {
    position: relative;
    pointer-events: none;
}

.sxgz-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.sxgz-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #409eff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: sxgz-spin 1s linear infinite;
    z-index: 1;
}

@keyframes sxgz-spin {
    to {
        transform: rotate(360deg);
    }
}
