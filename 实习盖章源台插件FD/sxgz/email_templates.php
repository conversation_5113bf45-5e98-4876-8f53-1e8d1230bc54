<?php
/**
 * 实习盖章邮件模板
 */

class SxgzEmailTemplates {
    
    /**
     * 获取下单确认邮件模板
     */
    public static function getOrderConfirmationTemplate($orderData) {
        $subject = "【实习盖章】订单确认 - {$orderData['order_no']}";
        
        $serviceTypeText = self::getServiceTypeText($orderData['service_type']);
        $statusText = self::getStatusText($orderData['status']);
        
        $body = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
            <div style='background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
                <h2 style='color: #007bff; margin: 0;'>实习盖章订单确认</h2>
            </div>
            
            <div style='background-color: #fff; padding: 20px; border: 1px solid #dee2e6; border-radius: 8px;'>
                <p>尊敬的 <strong>{$orderData['customer_name']}</strong>，您好！</p>
                
                <p>您的实习盖章订单已成功提交，我们将尽快为您处理。</p>
                
                <h3 style='color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px;'>订单信息</h3>
                <table style='width: 100%; border-collapse: collapse; margin: 15px 0;'>
                    <tr>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold; width: 120px;'>订单号：</td>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6;'>{$orderData['order_no']}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;'>公司名称：</td>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6;'>{$orderData['company_name']}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;'>服务类型：</td>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6;'>{$serviceTypeText}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;'>订单金额：</td>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6;'>¥{$orderData['total_price']}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;'>订单状态：</td>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6;'>{$statusText}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;'>下单时间：</td>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6;'>{$orderData['created_at']}</td>
                    </tr>
                </table>
                
                " . (isset($orderData['special_requirements']) && !empty($orderData['special_requirements']) ? "
                <h3 style='color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px;'>特殊要求</h3>
                <p style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>{$orderData['special_requirements']}</p>
                " : "") . "
                
                <div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <h4 style='color: #0056b3; margin: 0 0 10px 0;'>温馨提示</h4>
                    <ul style='margin: 0; padding-left: 20px;'>
                        <li>我们将在1-3个工作日内完成您的订单处理</li>
                        <li>如有任何问题，请及时联系我们的客服</li>
                        <li>订单完成后，我们会第一时间通过邮件通知您</li>
                    </ul>
                </div>
                
                <div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;'>
                    <p style='color: #6c757d; margin: 0;'>感谢您选择我们的服务！</p>
                    <p style='color: #6c757d; margin: 5px 0 0 0; font-size: 14px;'>如有疑问，请联系客服</p>
                </div>
            </div>
        </div>";
        
        return [
            'subject' => $subject,
            'body' => $body
        ];
    }
    
    /**
     * 获取订单完成邮件模板
     */
    public static function getCompletionNoticeTemplate($orderData, $attachments = []) {
        $subject = "【实习盖章】订单完成 - {$orderData['order_no']}";
        
        $serviceTypeText = self::getServiceTypeText($orderData['service_type']);
        
        $attachmentList = '';
        if (!empty($attachments)) {
            $attachmentList = "<h3 style='color: #495057; border-bottom: 2px solid #28a745; padding-bottom: 10px;'>附件文件</h3>";
            $attachmentList .= "<ul style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            foreach ($attachments as $attachment) {
                $attachmentList .= "<li style='margin: 5px 0;'>{$attachment['name']}</li>";
            }
            $attachmentList .= "</ul>";
        }
        
        $body = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
            <div style='background-color: #d4edda; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
                <h2 style='color: #155724; margin: 0;'>🎉 订单处理完成！</h2>
            </div>
            
            <div style='background-color: #fff; padding: 20px; border: 1px solid #dee2e6; border-radius: 8px;'>
                <p>尊敬的 <strong>{$orderData['customer_name']}</strong>，您好！</p>
                
                <p>您的实习盖章订单已完成处理，请查收相关文件。</p>
                
                <h3 style='color: #495057; border-bottom: 2px solid #28a745; padding-bottom: 10px;'>订单信息</h3>
                <table style='width: 100%; border-collapse: collapse; margin: 15px 0;'>
                    <tr>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold; width: 120px;'>订单号：</td>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6;'>{$orderData['order_no']}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;'>公司名称：</td>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6;'>{$orderData['company_name']}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;'>服务类型：</td>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6;'>{$serviceTypeText}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6; font-weight: bold;'>完成时间：</td>
                        <td style='padding: 8px; border-bottom: 1px solid #dee2e6;'>{$orderData['completed_at']}</td>
                    </tr>
                </table>
                
                {$attachmentList}
                
                " . ($orderData['service_type'] === 'mail' || $orderData['service_type'] === 'both' ? "
                <div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;'>
                    <h4 style='color: #856404; margin: 0 0 10px 0;'>📦 邮寄信息</h4>
                    <p style='margin: 0; color: #856404;'>您的实习盖章文件将通过快递寄送到您提供的地址，请注意查收。</p>
                </div>
                " : "") . "
                
                <div style='background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <h4 style='color: #0c5460; margin: 0 0 10px 0;'>重要提醒</h4>
                    <ul style='margin: 0; padding-left: 20px; color: #0c5460;'>
                        <li>请妥善保管您的实习盖章文件</li>
                        <li>如发现文件有任何问题，请及时联系我们</li>
                        <li>感谢您对我们服务的信任与支持</li>
                    </ul>
                </div>
                
                <div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;'>
                    <p style='color: #28a745; font-weight: bold; margin: 0;'>订单处理完成！</p>
                    <p style='color: #6c757d; margin: 5px 0 0 0; font-size: 14px;'>如有疑问，请联系客服</p>
                </div>
            </div>
        </div>";
        
        return [
            'subject' => $subject,
            'body' => $body
        ];
    }
    
    /**
     * 获取服务类型文本
     */
    private static function getServiceTypeText($serviceType) {
        $typeMap = [
            'electronic' => '电子版',
            'mail' => '邮寄服务',
            'both' => '邮寄+电子版'
        ];
        
        return $typeMap[$serviceType] ?? $serviceType;
    }
    
    /**
     * 获取状态文本
     */
    private static function getStatusText($status) {
        $statusMap = [
            'pending' => '待处理',
            'processing' => '处理中',
            'completed' => '已完成',
            'cancelled' => '已取消',
            'failed' => '失败'
        ];
        
        return $statusMap[$status] ?? $status;
    }
}
?>
