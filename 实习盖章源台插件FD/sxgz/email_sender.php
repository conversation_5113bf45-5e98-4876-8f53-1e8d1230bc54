<?php
/**
 * 实习盖章邮件发送处理类
 */

require_once('../confing/email.php');
require_once('email_templates.php');

class SxgzEmailSender {
    
    private $DB;
    
    public function __construct($database) {
        $this->DB = $database;
    }
    
    /**
     * 发送下单确认邮件
     */
    public function sendOrderConfirmation($orderId) {
        try {
            // 获取订单信息
            $order = $this->DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}'");
            if (!$order) {
                throw new Exception("订单不存在: {$orderId}");
            }
            
            if (empty($order['customer_email'])) {
                throw new Exception("订单缺少邮箱地址: {$orderId}");
            }
            
            // 生成邮件内容
            $template = SxgzEmailTemplates::getOrderConfirmationTemplate($order);
            
            // 发送邮件
            $result = $this->sendEmail(
                $order['customer_email'],
                $order['customer_name'],
                $template['subject'],
                $template['body']
            );
            
            if ($result) {
                // 更新发送状态
                $this->DB->query("UPDATE FD_sxgz_orders SET order_email_sent = 1 WHERE order_id = '{$orderId}'");
                
                // 更新邮件队列状态
                $this->updateEmailQueueStatus($orderId, 'order_confirmation', 'sent');
                
                return true;
            } else {
                throw new Exception("邮件发送失败");
            }
            
        } catch (Exception $e) {
            // 记录错误
            $this->updateEmailQueueStatus($orderId, 'order_confirmation', 'failed', $e->getMessage());
            error_log("发送下单确认邮件失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 发送订单完成邮件
     */
    public function sendCompletionNotice($orderId) {
        try {
            // 获取订单信息
            $order = $this->DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}'");
            if (!$order) {
                throw new Exception("订单不存在: {$orderId}");
            }
            
            if (empty($order['customer_email'])) {
                throw new Exception("订单缺少邮箱地址: {$orderId}");
            }
            
            // 获取附件文件
            $attachments = $this->getOrderAttachments($orderId);
            
            // 生成邮件内容
            $template = SxgzEmailTemplates::getCompletionNoticeTemplate($order, $attachments);
            
            // 发送邮件（带附件）
            $result = $this->sendEmailWithAttachments(
                $order['customer_email'],
                $order['customer_name'],
                $template['subject'],
                $template['body'],
                $attachments
            );
            
            if ($result) {
                // 更新发送状态
                $this->DB->query("UPDATE FD_sxgz_orders SET completion_email_sent = 1 WHERE order_id = '{$orderId}'");
                
                // 更新邮件队列状态
                $this->updateEmailQueueStatus($orderId, 'completion_notice', 'sent');
                
                return true;
            } else {
                throw new Exception("邮件发送失败");
            }
            
        } catch (Exception $e) {
            // 记录错误
            $this->updateEmailQueueStatus($orderId, 'completion_notice', 'failed', $e->getMessage());
            error_log("发送完成通知邮件失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取订单附件文件
     */
    private function getOrderAttachments($orderId) {
        global $DB;

        $attachments = [];

        // 获取订单信息
        $order = $DB->get_row("SELECT uid FROM FD_sxgz_orders WHERE order_id = '{$orderId}'");
        if (!$order) {
            return $attachments;
        }

        // 检查多种可能的目录结构
        $possibleDirs = [
            // 新格式：uid_X/orderid_Y/
            __DIR__ . '/processed/uid_' . $order['uid'] . '/orderid_' . $orderId . '/',
            // 旧格式：orderid/
            __DIR__ . '/processed/' . $orderId . '/',
            // 其他可能格式：uid/orderid/
            __DIR__ . '/processed/' . $order['uid'] . '/' . $orderId . '/'
        ];

        foreach ($possibleDirs as $processedDir) {
            if (is_dir($processedDir)) {
                $files = scandir($processedDir);
                foreach ($files as $file) {
                    if ($file !== '.' && $file !== '..' && is_file($processedDir . $file)) {
                        $attachments[] = [
                            'name' => $file,
                            'path' => $processedDir . $file,
                            'size' => filesize($processedDir . $file)
                        ];
                    }
                }
                break; // 找到文件就停止搜索其他目录
            }
        }

        return $attachments;
    }
    
    /**
     * 发送普通邮件
     */
    private function sendEmail($toEmail, $toName, $subject, $body) {
        try {
            // 使用PHPMailer发送邮件
            global $conf;

            require_once('../confing/my_vendor/vendor/autoload.php');

            $mail = new PHPMailer\PHPMailer\PHPMailer(true);

            // 服务器配置
            $mail->isSMTP();
            $mail->Host = $conf['smtp_server'] ?? $conf['mail_host'] ?? '';
            $mail->SMTPAuth = true;
            $mail->Username = $conf['mailusername'] ?? $conf['mail_username'] ?? '';
            $mail->Password = $conf['mailpassword'] ?? $conf['mail_password'] ?? '';

            // 设置加密方式和端口
            $encryption = strtoupper($conf['encryption'] ?? 'TLS');
            $port = intval($conf['smtp_port'] ?? $conf['mail_port'] ?? 587);

            if ($encryption === 'SSL') {
                $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
                // SSL通常使用465或994端口
                if ($port == 994 || $port == 465) {
                    $mail->Port = $port;
                } else {
                    $mail->Port = 465; // 默认SSL端口
                }
            } else {
                $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
                $mail->Port = $port == 994 ? 587 : $port; // TLS通常使用587端口
            }

            // 添加调试信息
            error_log("邮件配置 - 服务器: {$mail->Host}, 端口: {$mail->Port}, 加密: {$mail->SMTPSecure}, 用户: {$mail->Username}");
            $mail->CharSet = 'UTF-8';

            // 发件人
            $fromEmail = $conf['from_email'] ?? $conf['mail_username'] ?? $conf['mailusername'] ?? '';
            $fromName = $conf['from_name'] ?? $conf['mail_name'] ?? '实习盖章系统';
            $mail->setFrom($fromEmail, $fromName);

            // 收件人
            $mail->addAddress($toEmail, $toName);

            // 邮件内容
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $body;

            // 启用调试模式
            $mail->SMTPDebug = 0; // 0=关闭调试, 1=客户端消息, 2=客户端和服务器消息

            $result = $mail->send();

            if ($result) {
                error_log("邮件发送成功: {$toEmail}");
            } else {
                error_log("邮件发送失败: {$toEmail}, 错误: " . $mail->ErrorInfo);
            }

            return $result;

        } catch (Exception $e) {
            error_log("邮件发送异常: " . $e->getMessage() . " 文件: " . $e->getFile() . " 行号: " . $e->getLine());
            return false;
        }
    }
    
    /**
     * 发送带附件的邮件
     */
    private function sendEmailWithAttachments($toEmail, $toName, $subject, $body, $attachments = []) {
        try {
            // 如果没有附件，使用普通发送
            if (empty($attachments)) {
                return $this->sendEmail($toEmail, $toName, $subject, $body);
            }

            // 引入全局配置
            global $conf;

            // 使用PHPMailer发送带附件的邮件
            require_once('../confing/my_vendor/vendor/autoload.php');

            $mail = new PHPMailer\PHPMailer\PHPMailer(true);

            // 服务器配置
            $mail->isSMTP();
            $mail->Host = $conf['smtp_server'] ?? $conf['mail_host'] ?? '';
            $mail->SMTPAuth = true;
            $mail->Username = $conf['mailusername'] ?? $conf['mail_username'] ?? '';
            $mail->Password = $conf['mailpassword'] ?? $conf['mail_password'] ?? '';

            // 设置加密方式和端口
            $encryption = strtoupper($conf['encryption'] ?? 'TLS');
            $port = intval($conf['smtp_port'] ?? $conf['mail_port'] ?? 587);

            if ($encryption === 'SSL') {
                $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
                // SSL通常使用465或994端口
                if ($port == 994 || $port == 465) {
                    $mail->Port = $port;
                } else {
                    $mail->Port = 465; // 默认SSL端口
                }
            } else {
                $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
                $mail->Port = $port == 994 ? 587 : $port; // TLS通常使用587端口
            }
            $mail->CharSet = 'UTF-8';

            // 发件人
            $fromEmail = $conf['from_email'] ?? $conf['mail_username'] ?? $conf['mailusername'] ?? '';
            $fromName = $conf['from_name'] ?? $conf['mail_name'] ?? '实习盖章系统';
            $mail->setFrom($fromEmail, $fromName);
            
            // 收件人
            $mail->addAddress($toEmail, $toName);
            
            // 邮件内容
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $body;
            
            // 添加附件
            foreach ($attachments as $attachment) {
                if (file_exists($attachment['path'])) {
                    $mail->addAttachment($attachment['path'], $attachment['name']);
                }
            }
            
            return $mail->send();
            
        } catch (Exception $e) {
            error_log("带附件邮件发送异常: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 更新邮件队列状态
     */
    private function updateEmailQueueStatus($orderId, $emailType, $status, $errorMessage = '') {
        $updateData = [
            'status' => $status,
            'attempts' => 'attempts + 1'
        ];
        
        if ($status === 'sent') {
            $updateData['sent_at'] = 'NOW()';
        } elseif ($status === 'failed' && !empty($errorMessage)) {
            $updateData['error_message'] = "'{$this->DB->escape($errorMessage)}'";
        }
        
        $updateFields = [];
        foreach ($updateData as $field => $value) {
            if ($field === 'attempts') {
                $updateFields[] = "{$field} = {$value}";
            } elseif ($value === 'NOW()') {
                $updateFields[] = "{$field} = NOW()";
            } elseif ($field === 'error_message') {
                $updateFields[] = "{$field} = {$value}"; // 已经包含引号
            } else {
                $updateFields[] = "{$field} = '{$value}'"; // 字符串需要引号
            }
        }
        
        $sql = "UPDATE FD_sxgz_email_queue SET " . implode(', ', $updateFields) . 
               " WHERE order_id = '{$orderId}' AND email_type = '{$emailType}'";
        
        $this->DB->query($sql);
    }
    
    /**
     * 处理邮件队列
     */
    public function processEmailQueue($limit = 10) {
        $pendingEmails = $this->DB->get_results(
            "SELECT * FROM FD_sxgz_email_queue 
             WHERE status = 'pending' OR (status = 'failed' AND attempts < 3)
             ORDER BY created_at ASC 
             LIMIT {$limit}"
        );
        
        $processed = 0;
        foreach ($pendingEmails as $email) {
            if ($email['email_type'] === 'order_confirmation') {
                $result = $this->sendOrderConfirmation($email['order_id']);
            } elseif ($email['email_type'] === 'completion_notice') {
                $result = $this->sendCompletionNotice($email['order_id']);
            } else {
                continue;
            }
            
            if ($result) {
                $processed++;
            }
            
            // 避免发送过快
            sleep(1);
        }
        
        return $processed;
    }
}
?>
