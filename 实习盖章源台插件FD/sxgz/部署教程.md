 实习盖章模块部署教程

 🚀 快速部署

 1. 文件部署
将整个 sxgz 目录上传到网站根目录


 2. 数据库安装

- 执行数据库安装脚本
mysql -u username -p database_name < sxgz/install.sql

或者可直接右键选择文件导入指定数据库


 3. 目录权限设置
 根目录打开终端，设置文件上传目录权限
chmod 755 sxgz/uploads/
chmod 755 sxgz/processed/
chmod 755 sxgz/logs/

 确保 PHP 可写
chown -R www-data:www-data sxgz/uploads/
chown -R www-data:www-data sxgz/processed/
chown -R www-data:www-data sxgz/logs/

配置邮件进程守护：nohup php email_queue_monitor.php &
或者计划任务 直接访问sxgz/email_queue_monitor.php即可
二选一


 4. 访问地址

 添加导航到index/index中即可使用
- 用户界面：/index/sxgz.php
- 管理员界面：/index/sxgz_admin.php //验证用户uid，只有管理员可以打开访问
- API接口：/sxgz/api.php

 5. 目录结构介绍


sxgz/
├── api.php                  核心API接口
├── file_manager.php         文件管理类
├── email_sender.php         邮件发送功能
├── install.sql             数据库安装脚本
├── uploads/                用户上传文件目录
├── processed/              已处理文件目录
├── logs/                   日志文件目录
└── 部署教程.md             本文档

index/
├── sxgz.php               用户下单界面
└── sxgz_admin.php         管理员管理界面


 6. 数据库表结构介绍

 主要数据表
- FD_sxgz_orders - 订单主表
- FD_sxgz_order_logs - 订单状态日志
- FD_sxgz_email_queue - 邮件队列表



 7. 配置说明

 1. 公司配置
在 qingka_wangke_class 表中配置可选公司：
- 前往分类设置，添加一个分类名称为实习盖章（状态选未启用，只能创建一个，后端接口自动查找该名称分类下的课程也就是企业）
- 通过课程管理添加企业或者管理员界面公司管理添加企业，配置基础价格和服务内容

 2. 文件上传限制
在 file_manager.php 中配置：
- 允许的文件类型
- 文件大小限制
- 存储路径设置

 3. 邮件配置
在 email_sender.php 中配置：
- SMTP服务器设置（fd模板直接在系统设置→邮件配置中设计即可）
- sxgz/email_templates.php为邮件模板定制，可以自定义样式

 8. 权限说明

 用户权限 (uid ≠ 1)
- 查看自己的订单
- 创建新订单
- 上传文件
- 申请退款

 管理员权限 (uid = 1)
- 查看所有用户订单
- 处理订单状态
- 管理文件
- 处理退款申请
- 手动标记失败并退款

 🔧 功能特性

 订单管理
- 智能排序：处理中 > 申请退款 > 待处理
- 搜索筛选：多字段搜索，状态筛选
- 导出功能：Excel格式或者CSV格式导出

 文件管理
- 安全上传：文件类型验证，大小限制
- 分类存储：按订单ID分目录存储
- 处理流程：上传 → 处理 → 下载
- 权限控制：用户只能访问自己的文件


 📞 技术支持
- 如有问题随时联系作者


---

版本：v1.0  
更新时间：2025-06-25
兼容性：PHP 7.4+, MySQL 5.7+，FreeDom模板定制
