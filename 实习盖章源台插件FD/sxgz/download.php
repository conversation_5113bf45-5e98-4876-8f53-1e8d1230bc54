<?php
/**
 * 实习盖章文件下载接口
 * 提供安全的文件下载功能
 */

require_once('../confing/common.php');

$uid = $_GET['uid'] ?? '';
$orderId = $_GET['order_id'] ?? '';
$filename = $_GET['file'] ?? '';
$type = $_GET['type'] ?? 'upload'; // upload 或 processed
$token = $_GET['token'] ?? '';

// 验证用户登录或token
$hasPermission = false;

// 1. 检查用户登录
if (isset($userrow) && $userrow) {
    $hasPermission = true;
}

// 2. 检查token验证（用于代理商下载）
if (!$hasPermission && !empty($token) && !empty($orderId)) {
    // 简单的token验证：md5(order_id + 'sxgz_download' + date)
    $expectedToken = md5($orderId . 'sxgz_download' . date('Y-m-d'));

    // 调试信息
    error_log("Token验证: orderId={$orderId}, token={$token}, expected={$expectedToken}, date=" . date('Y-m-d'));

    if ($token === $expectedToken) {
        $hasPermission = true;
    }
}

if (!$hasPermission) {
    http_response_code(401);
    $debugInfo = "用户未登录或token无效。";
    $debugInfo .= " 用户登录: " . (isset($userrow) && $userrow ? '是' : '否');
    $debugInfo .= " Token: " . ($token ? '有' : '无');
    $debugInfo .= " OrderID: " . ($orderId ? $orderId : '无');
    die($debugInfo);
}

if (empty($orderId) || empty($filename)) {
    http_response_code(400);
    die('参数错误');
}

// 验证订单存在
$order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}'");
if (!$order) {
    http_response_code(404);
    die('订单不存在');
}

// 如果没有提供uid，从订单中获取
if (empty($uid)) {
    $uid = $order['uid'];
}

// 权限验证：用户只能下载自己的订单文件，管理员可以下载所有文件
if ($userrow['uid'] != 1 && $order['uid'] != $userrow['uid']) {
    http_response_code(403);
    die('权限不足');
}

// 构建文件路径 - 支持多种目录结构
$baseDir = __DIR__ . '/';
$filePath = null;

if ($type === 'processed') {
    // 处理后文件路径
    $filePath = $baseDir . 'processed/uid_' . $uid . '/orderid_' . $orderId . '/' . $filename;
} else {
    // 上传文件路径 - 支持多种结构
    $possiblePaths = [
        // 新格式：uid_X/orderid_Y/filename
        $baseDir . 'uploads/uid_' . $uid . '/orderid_' . $orderId . '/' . $filename,
        // 临时文件格式：temp/filename
        $baseDir . 'uploads/temp/' . $filename,
        // 旧格式：uid/orderid/filename
        $baseDir . 'uploads/' . $uid . '/' . $orderId . '/' . $filename
    ];

    foreach ($possiblePaths as $path) {
        if (file_exists($path) && is_file($path)) {
            $filePath = $path;
            break;
        }
    }
}

// 验证文件存在
if (!$filePath || !file_exists($filePath) || !is_file($filePath)) {
    http_response_code(404);
    die('文件不存在: ' . $filename);
}

// 验证文件在允许的目录内（防止目录遍历攻击）
$realPath = realpath($filePath);
$allowedDir = realpath($baseDir . ($type === 'processed' ? 'processed' : 'uploads'));

if (strpos($realPath, $allowedDir) !== 0) {
    http_response_code(403);
    die('非法访问');
}

// 获取文件信息
$fileSize = filesize($filePath);
$fileExtension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

// 设置MIME类型
$mimeTypes = [
    'pdf' => 'application/pdf',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'zip' => 'application/zip',
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif'
];

$mimeType = $mimeTypes[$fileExtension] ?? 'application/octet-stream';

// 设置下载头
header('Content-Type: ' . $mimeType);
header('Content-Length: ' . $fileSize);
header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// 清除输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

// 输出文件内容
readfile($filePath);

// 记录下载日志
wlog($userrow['uid'], '文件下载', "订单: {$orderId}, 文件: {$filename}, 类型: {$type}", 0);

exit;
?>
