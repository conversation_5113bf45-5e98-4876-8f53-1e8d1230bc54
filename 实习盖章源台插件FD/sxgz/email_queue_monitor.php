<?php
/**
 * 实习盖章邮件队列监控脚本
 * 用于监控订单状态变化并自动发送邮件通知
 */

require_once('../confing/common.php');
require_once('email_sender.php');

// 设置脚本运行时间限制
set_time_limit(0);

// 日志文件路径
$logFile = __DIR__ . '/logs/email_queue_' . date('Y-m-d') . '.log';

// 确保日志目录存在
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

/**
 * 写入日志
 */
function writeLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    echo $logMessage;
}

/**
 * 监控订单状态变化
 */
function monitorOrderStatus() {
    global $DB;
    
    try {
        // 查找需要发送下单确认邮件的订单（只查找未发送且不在队列中的）
        $newOrders = $DB->get_results(
            "SELECT o.order_id, o.customer_email, o.customer_name
             FROM FD_sxgz_orders o
             WHERE o.order_email_sent = 0
             AND o.customer_email IS NOT NULL
             AND o.customer_email != ''
             AND o.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
             AND NOT EXISTS (
                 SELECT 1 FROM FD_sxgz_email_queue eq
                 WHERE eq.order_id = o.order_id
                 AND eq.email_type = 'order_confirmation'
             )
             ORDER BY o.created_at ASC
             LIMIT 20"
        );

        foreach ($newOrders as $order) {
            // 添加到邮件队列
            addToEmailQueue($order['order_id'], 'order_confirmation', $order['customer_email'], $order['customer_name']);
            writeLog("添加下单确认邮件到队列: 订单 {$order['order_id']}");
        }
        
        // 查找需要发送完成通知邮件的订单（只查找未发送且不在队列中的）
        $completedOrders = $DB->get_results(
            "SELECT o.order_id, o.customer_email, o.customer_name
             FROM FD_sxgz_orders o
             WHERE o.status = 'completed'
             AND o.completion_email_sent = 0
             AND o.customer_email IS NOT NULL
             AND o.customer_email != ''
             AND o.completed_at IS NOT NULL
             AND o.completed_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
             AND NOT EXISTS (
                 SELECT 1 FROM FD_sxgz_email_queue eq
                 WHERE eq.order_id = o.order_id
                 AND eq.email_type = 'completion_notice'
             )
             ORDER BY o.completed_at ASC
             LIMIT 20"
        );

        foreach ($completedOrders as $order) {
            // 添加到邮件队列
            addToEmailQueue($order['order_id'], 'completion_notice', $order['customer_email'], $order['customer_name']);
            writeLog("添加完成通知邮件到队列: 订单 {$order['order_id']}");
        }
        
    } catch (Exception $e) {
        writeLog("监控订单状态异常: " . $e->getMessage());
    }
}

/**
 * 添加到邮件队列
 */
function addToEmailQueue($orderId, $emailType, $recipientEmail, $recipientName) {
    global $DB;
    
    // 检查是否已存在相同的邮件队列记录
    $existing = $DB->get_row(
        "SELECT queue_id FROM FD_sxgz_email_queue 
         WHERE order_id = '{$orderId}' AND email_type = '{$emailType}'"
    );
    
    if ($existing) {
        return; // 已存在，不重复添加
    }
    
    $emailData = json_encode([
        'order_id' => $orderId,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
    $DB->insert_array('FD_sxgz_email_queue', [
        'order_id' => $orderId,
        'email_type' => $emailType,
        'recipient_email' => $recipientEmail,
        'recipient_name' => $recipientName,
        'email_data' => $emailData,
        'status' => 'pending',
        'created_at' => date('Y-m-d H:i:s')
    ]);
}

/**
 * 处理邮件队列
 */
function processEmailQueue() {
    global $DB;
    
    try {
        $emailSender = new SxgzEmailSender($DB);
        $processed = $emailSender->processEmailQueue(10);
        
        if ($processed > 0) {
            writeLog("处理了 {$processed} 封邮件");
        }
        
    } catch (Exception $e) {
        writeLog("处理邮件队列异常: " . $e->getMessage());
    }
}

/**
 * 清理过期的邮件队列记录
 */
function cleanupEmailQueue() {
    global $DB;
    
    try {
        // 删除7天前已发送的邮件记录
        $deleted = $DB->query(
            "DELETE FROM FD_sxgz_email_queue 
             WHERE status = 'sent' 
             AND sent_at < DATE_SUB(NOW(), INTERVAL 7 DAY)"
        );
        
        if ($deleted) {
            writeLog("清理了过期的邮件队列记录");
        }
        
        // 重置失败次数过多的邮件状态（超过3次失败且24小时前创建的）
        $reset = $DB->query(
            "UPDATE FD_sxgz_email_queue 
             SET status = 'pending', attempts = 0, error_message = NULL 
             WHERE status = 'failed' 
             AND attempts >= 3 
             AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)"
        );
        
        if ($reset) {
            writeLog("重置了失败的邮件队列记录");
        }
        
    } catch (Exception $e) {
        writeLog("清理邮件队列异常: " . $e->getMessage());
    }
}

/**
 * 主监控循环
 */
function mainLoop() {
    writeLog("邮件队列监控脚本启动");
    
    $loopCount = 0;
    
    while (true) {
        try {
            $loopCount++;
            
            // 监控订单状态变化
            monitorOrderStatus();
            
            // 处理邮件队列
            processEmailQueue();
            
            // 每10次循环清理一次过期记录
            if ($loopCount % 10 === 0) {
                cleanupEmailQueue();
            }
            
            // 每100次循环输出一次状态
            if ($loopCount % 100 === 0) {
                writeLog("监控脚本运行正常，已完成 {$loopCount} 次循环");
            }
            
        } catch (Exception $e) {
            writeLog("主循环异常: " . $e->getMessage());
        }
        
        // 休眠30秒
        sleep(30);
    }
}

// 检查是否为命令行运行
if (php_sapi_name() === 'cli') {
    // 命令行模式，启动监控循环
    mainLoop();
} else {
    // Web模式，执行一次处理
    writeLog("Web模式执行邮件队列处理");
    
    monitorOrderStatus();
    processEmailQueue();
    cleanupEmailQueue();
    
    echo json_encode([
        'success' => true,
        'message' => '邮件队列处理完成',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
