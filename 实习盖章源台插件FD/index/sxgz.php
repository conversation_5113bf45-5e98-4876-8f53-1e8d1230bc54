<?php
$title='实习盖章';
require_once('head.php');
// 在页面渲染时将用户信息传递给JavaScript
echo '<script>';
echo 'var userrow = { uid: ' . $userrow['uid'] . ' , addprice: ' . $userrow['addprice'] . ' };'; // 将用户的uid传递给JavaScript
// 可以将其他用户信息也传递给JavaScript，例如：用户名、角色等
echo '</script>';

?>
<!-- Element UI CSS -->
<link rel="stylesheet" href="/sxgz/element/element.css" />
<!-- 实习盖章专用样式 -->
<link rel="stylesheet" href="/sxgz/style.css" />
<!-- Font Awesome 图标库 -->
<link rel="stylesheet" href="/sxgz/element/font-awesome.min.css" />
<style>
/* ==================== CSS变量系统 - 简约设计 ==================== */
:root {
	/* 简约色彩系统 */
	--primary-color: #2c3e50;      /* 深蓝灰 - 主色 */
	--secondary-color: #34495e;    /* 中蓝灰 - 辅助色 */
	--accent-color: #3498db;       /* 蓝色 - 强调色 */
	--success-color: #27ae60;      /* 绿色 - 成功 */
	--warning-color: #f39c12;      /* 橙色 - 警告 */
	--danger-color: #e74c3c;       /* 红色 - 危险 */
	--text-primary: #2c3e50;       /* 主要文字 */
	--text-secondary: #7f8c8d;     /* 次要文字 */
	--text-muted: #95a5a6;         /* 弱化文字 */
	--border-color: #ecf0f1;       /* 边框色 */
	--border-active: #2c3e50;      /* 激活状态边框 */
	--bg-light: #f8f9fa;           /* 浅背景 */
	--bg-active: #e6f3ff;          /* 激活状态浅蓝背景 */
	--bg-white: #ffffff;           /* 白色背景 */

	/* 尺寸系统 */
	--border-radius: 4px;          /* 统一圆角 */
	--font-size-small: 12px;       /* 小字体 */
	--font-size-base: 12px;        /* 基础字体 */
	--font-size-medium: 14px;      /* 中等字体 */
}

/* ==================== 表格样式 ==================== */
.el-table {
	border-radius: var(--border-radius);
	overflow: hidden;
}

.el-table th, .el-table td {
	padding: 8px 10px;
	border-bottom: 1px solid var(--border-light);
	vertical-align: middle;
	font-size: 14px;
	text-align: center;
}

.el-table th {
	background-color: var(--bg-light);
	font-weight: 600;
	color: var(--text-primary);
	font-size: 14px;
}

.el-table .cell {
	line-height: 1.4;
	word-break: break-word;
	white-space: normal;
	overflow: visible;
}

.el-table .el-table__row {
	height: auto;
	min-height: 60px;
}

/* ==================== 操作按钮 ==================== */
.action-buttons {
	display: flex;
	gap: 2px;
	justify-content: center;
	flex-wrap: wrap;
}

.action-buttons .el-button {
	padding: 4px;
	width: auto;
	height: 26px;
	font-size: 12px;
}

/* ==================== 按钮组和搜索区域 ==================== */
.btn-group, .search-wrapper {
	display: flex;
	gap: 8px;
	flex-wrap: wrap;
	margin-bottom: 10px;
	align-items: center;
}

.search-wrapper {
	padding: 10px 0;
}

.btn-group .el-button, .search-wrapper .el-button {
	border-radius: var(--border-radius);
	font-weight: 500;
	border: 1px solid var(--border-light);
	font-size: var(--font-size-small);
}


/* ==================== 卡片样式 ==================== */
.service-type-card, .material-option {
	border: 2px solid var(--border-light);
	border-radius: var(--border-radius);
	padding: 16px 12px;
	cursor: pointer;
	background: var(--bg-white);
}

.service-type-card {
	text-align: center;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.material-option {
	margin-bottom: 12px;
}

.service-type-card:hover, .service-type-card.active,
.material-option:hover, .material-option.active {
	border-color: var(--border-active);
	border-width: 3px;
}

.service-type-card.active,
.material-option.active {
	background-color: var(--bg-active);
	box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
	transform: translateY(-2px);
}

.service-type-card.active h3,
.material-option.active h3 {
	color: var(--primary-color);
}

.service-type-card.active i,
.material-option.active i {
	color: var(--primary-color);
}

.service-type-card i {
	font-size: 28px;
	margin-bottom: 8px;
	color: var(--text-primary);
}

.service-type-card h3 {
	margin: 0 0 6px 0;
	font-size: var(--font-size-medium);
	font-weight: 600;
	color: var(--text-primary);
}

.service-type-card p {
	margin: 0;
	font-size: var(--font-size-small);
	color: var(--text-secondary);
	line-height: 1.4;
}

/* ==================== 步骤表单 ==================== */
.step-actions {
	display: flex;
	justify-content: space-between;
	margin-top: 24px;
	padding-top: 16px;
	border-top: 1px solid var(--border-light);
	background: var(--bg-light);
	border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.step-actions .el-button {
	border-radius: var(--border-radius);
	font-size: var(--font-size-small);
}

/* ==================== 特殊样式 ==================== */
/* 订单号显示 */
.order-no-cell {
	font-family: 'Courier New', monospace;
	font-size: 12px;
	line-height: 1.3;
	white-space: normal;
	word-break: break-all;
	overflow-wrap: break-word;
	max-width: 140px;
}

/* 时间显示 */
.time-cell {
	font-size: 12px;
	line-height: 1.3;
	white-space: normal;
	word-break: break-word;
	color: var(--text-regular);
}

/* 危险操作项 */
.danger-item {
	color: var(--danger-color) !important;
}

.danger-item:hover {
	background-color: var(--bg-light) !important;
	color: var(--danger-color) !important;
}

/* 订单详情对话框 */
.order-detail-dialog .el-message-box__content {
	white-space: pre-line !important;
	font-family: monospace !important;
	font-size: 12px !important;
	line-height: 1.6 !important;
	text-align: left !important;
}

/* 文件快递列图标样式 */
.file-courier-icon {
	font-size: 12px;
	margin-right: 4px;
	flex-shrink: 0;
}

.file-icon {
	color: var(--accent-color);
}

.courier-icon {
	color: var(--success-color);
}

.tracking-icon {
	color: var(--text-secondary);
}

/* 备注文本自动换行样式 */
.notes-text {
	white-space: pre-wrap !important;
	word-wrap: break-word !important;
	word-break: break-word !important;
	overflow-wrap: break-word !important;
	line-height: 1.4 !important;
	max-width: 100% !important;
}

/* ==================== 响应式设计 ==================== */

/* 平板端适配 (768px - 992px) */
@media (max-width: 992px) and (min-width: 769px) {
	.wrapper-md {
		padding: 15px !important;
	}

	.panel-body {
		padding: 15px !important;
	}

	.el-button {
		font-size: 13px !important;
		padding: 8px 15px !important;
	}
}

/* 移动端适配 (481px - 768px) */
@media (max-width: 768px) {
	/* 页面布局优化 */
	.wrapper-md {
		padding: 10px !important;
	}

	.panel {
		margin: 0 0 10px 0 !important;
		border-radius: 8px !important;
	}

	.panel-body {
		padding: 12px !important;
	}

	.panel-heading {
		padding: 10px 12px !important;
	}

	.panel-heading h4 {
		font-size: 16px !important;
		margin: 0 !important;
	}

	/* 按钮组优化 */
	.btn-group {
		flex-direction: column !important;
		gap: 6px !important;
		align-items: stretch !important;
	}

	.btn-group .el-button {
		width: 100% !important;
		margin: 0 !important;
		font-size: 14px !important;
		padding: 10px !important;
		height: auto !important;
	}

	/* 搜索区域优化 */
	.search-wrapper {
		flex-direction: column !important;
		gap: 8px !important;
		padding: 8px 0 !important;
	}

	.search-wrapper .el-input-group {
		width: 100% !important;
	}

	.search-wrapper .el-select {
		width: 100% !important;
	}

	/* 表格优化 */
	.table-container {
		overflow-x: auto !important;
		-webkit-overflow-scrolling: touch !important;
		border-radius: 8px !important;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
	}

	.el-table {
		min-width: 800px !important;
		font-size: 12px !important;
	}

	.el-table th {
		padding: 8px 4px !important;
		font-size: 11px !important;
		white-space: nowrap !important;
	}

	.el-table td {
		padding: 6px 4px !important;
		font-size: 11px !important;
	}

	.el-table .cell {
		padding: 2px 4px !important;
		line-height: 1.3 !important;
	}

	/* 操作按钮优化 */
	.action-buttons {
		flex-direction: column !important;
		gap: 2px !important;
	}

	.action-buttons .el-button {
		width: 100% !important;
		font-size: 10px !important;
		padding: 3px 6px !important;
		height: 24px !important;
	}

	/* 对话框优化 */
	.el-dialog {
		width: 95% !important;
		margin: 3vh auto !important;
		max-height: 94vh !important;
		border-radius: 12px !important;
	}

	.el-dialog__header {
		padding: 15px 15px 10px !important;
	}

	.el-dialog__body {
		padding: 10px 15px !important;
		max-height: 70vh !important;
		overflow-y: auto !important;
	}

	.el-dialog__footer {
		padding: 10px 15px 15px !important;
	}

	/* 表单优化 */
	.el-form-item {
		margin-bottom: 15px !important;
	}

	.el-form-item__label {
		font-size: 13px !important;
		line-height: 1.4 !important;
		margin-bottom: 5px !important;
	}

	.el-input__inner,
	.el-textarea__inner,
	.el-select .el-input__inner {
		font-size: 14px !important;
		padding: 8px 12px !important;
	}

	/* 分页优化 */
	.el-pagination {
		text-align: center !important;
		padding: 15px 0 !important;
	}

	.el-pagination .el-pager li {
		min-width: 32px !important;
		height: 32px !important;
		line-height: 32px !important;
		font-size: 13px !important;
	}

	.el-pagination .btn-prev,
	.el-pagination .btn-next {
		min-width: 32px !important;
		height: 32px !important;
		line-height: 32px !important;
	}

	/* 标签优化 */
	.el-tag {
		font-size: 10px !important;
		padding: 2px 6px !important;
		height: auto !important;
		line-height: 1.2 !important;
	}

	/* 客户信息列样式 */
	.customer-info-cell {
		text-align: left !important;
		line-height: 1.4;
	}

	.customer-info-cell .customer-name {
		margin-bottom: 4px;
		padding-bottom: 3px;
		border-bottom: 1px solid #f0f0f0;
	}

	/* 服务详情列样式 */
	.service-detail-cell {
		text-align: left !important;
		line-height: 1.4;
	}

	.service-detail-cell .el-tag {
		margin-bottom: 3px;
		font-size: 10px !important;
		padding: 1px 4px !important;
	}

	/* 公司信息列样式 */
	.company-info-cell {
		text-align: left !important;
	}

	.company-info-cell .main-company {
		margin-bottom: 4px;
		padding-bottom: 3px;
		border-bottom: 1px solid #f0f0f0;
	}

	.company-info-cell .license-companies {
		background: #fff7e6;
		padding: 4px 6px;
		border-radius: 3px;
		border-left: 2px solid #E6A23C;
		margin-top: 3px;
	}

	.company-info-cell .license-companies .el-tag {
		margin-right: 3px;
		margin-bottom: 1px;
		font-size: 9px !important;
		padding: 1px 3px !important;
		border-radius: 2px !important;
	}

	/* 消息提示优化 */
	.el-message {
		min-width: 280px !important;
		max-width: 90% !important;
		left: 50% !important;
		transform: translateX(-50%) !important;
		font-size: 13px !important;
	}

	/* 批量操作区域移动端优化 */
	.batch-actions {
		margin: 8px 0 !important;
		padding: 8px 10px !important;
		border-radius: 8px !important;
	}

	.batch-actions > div {
		flex-direction: column !important;
		align-items: flex-start !important;
		gap: 8px !important;
	}

	.batch-actions .el-button {
		font-size: 12px !important;
		padding: 6px 12px !important;
		width: 100% !important;
	}

	/* 分页组件移动端优化 */
	.pagination-wrapper {
		text-align: center !important;
		padding: 15px 0 !important;
		margin-top: 10px !important;
	}

	.pagination-wrapper .el-pagination {
		justify-content: center !important;
	}

	.pagination-wrapper .el-pagination .el-pagination__total {
		font-size: 12px !important;
		margin-right: 8px !important;
	}

	.pagination-wrapper .el-pagination .el-pagination__jump {
		font-size: 12px !important;
		margin-left: 8px !important;
	}

	/* 通知区域移动端优化 */
	.notice-tag {
		margin-bottom: 8px !important;
	}

	.notice-tag .el-alert {
		border-radius: 8px !important;
	}

	.notice-tag .el-alert__title {
		font-size: 13px !important;
		line-height: 1.4 !important;
	}

	.notice-tag .el-alert__content {
		font-size: 12px !important;
		line-height: 1.4 !important;
		margin-top: 5px !important;
	}

	.notice-tag .el-alert__content p {
		margin: 3px 0 !important;
	}

	/* 下拉菜单移动端优化 */
	.el-dropdown-menu {
		min-width: 120px !important;
	}

	.el-dropdown-menu__item {
		font-size: 13px !important;
		padding: 8px 12px !important;
		line-height: 1.4 !important;
	}

	/* 操作按钮下拉菜单优化 */
	.action-buttons .el-dropdown .el-button {
		width: 100% !important;
		font-size: 10px !important;
		padding: 3px 6px !important;
	}

	/* 对话框内容移动端优化 */
	.dialog-form .el-dialog__body {
		padding: 10px 15px !important;
	}

	/* 步骤条移动端优化 */
	.el-steps {
		margin-bottom: 15px !important;
	}

	.el-steps--simple .el-step__title {
		font-size: 12px !important;
		line-height: 1.3 !important;
	}

	/* 文件上传区域移动端优化 */
	.el-upload {
		width: 100% !important;
	}

	.el-upload-dragger {
		width: 100% !important;
		height: 120px !important;
	}

	.el-upload-dragger .el-icon-upload {
		font-size: 40px !important;
		margin: 20px 0 10px !important;
	}

	.el-upload-dragger .el-upload__text {
		font-size: 12px !important;
		line-height: 1.4 !important;
	}
}

/* 超小屏幕适配 (≤ 480px) */
@media (max-width: 480px) {
	/* 页面布局进一步优化 */
	.wrapper-md {
		padding: 8px !important;
	}

	.panel-body {
		padding: 8px !important;
	}

	.panel-heading {
		padding: 8px 10px !important;
	}

	.panel-heading h4 {
		font-size: 15px !important;
	}

	/* 按钮进一步优化 */
	.btn-group .el-button {
		font-size: 13px !important;
		padding: 8px !important;
	}

	/* 表格进一步优化 */
	.el-table {
		min-width: 700px !important;
		font-size: 11px !important;
	}

	.el-table th,
	.el-table td {
		padding: 4px 2px !important;
		font-size: 10px !important;
	}

	.action-buttons .el-button {
		font-size: 9px !important;
		padding: 2px 4px !important;
		height: 22px !important;
	}

	/* 对话框进一步优化 */
	.el-dialog {
		width: 98% !important;
		margin: 2vh auto !important;
		max-height: 96vh !important;
	}

	.el-dialog__body {
		max-height: 75vh !important;
	}

	/* 表单进一步优化 */
	.el-form-item__label {
		font-size: 12px !important;
	}

	.el-input__inner,
	.el-textarea__inner {
		font-size: 13px !important;
		padding: 6px 10px !important;
	}

	/* 分页进一步优化 */
	.el-pagination .el-pager li {
		min-width: 28px !important;
		height: 28px !important;
		line-height: 28px !important;
		font-size: 12px !important;
	}

	.el-pagination .btn-prev,
	.el-pagination .btn-next {
		min-width: 28px !important;
		height: 28px !important;
		line-height: 28px !important;
	}

	/* 超小屏幕进一步优化 */
	.notice-tag .el-alert__title {
		font-size: 12px !important;
	}

	.notice-tag .el-alert__content {
		font-size: 11px !important;
	}

	/* 搜索区域超小屏优化 */
	.search-wrapper .el-input-group__prepend {
		padding: 0 8px !important;
	}

	.search-wrapper .el-select .el-input__inner {
		font-size: 12px !important;
	}

	/* 对话框超小屏优化 */
	.dialog-form .el-dialog__header {
		padding: 10px 15px 5px !important;
	}

	.dialog-form .el-dialog__title {
		font-size: 16px !important;
	}

	.el-upload-dragger {
		height: 100px !important;
	}

	.el-upload-dragger .el-icon-upload {
		font-size: 35px !important;
		margin: 15px 0 8px !important;
	}
}


/* ==================== 表单元素样式 ==================== */
/* 单选框样式 */
.material-option .el-radio {
	width: 100%;
	display: flex;
	align-items: flex-start;
}

.material-option .el-radio__label {
	white-space: normal;
	line-height: 1.6;
	font-size: var(--font-size-small);
	color: var(--text-regular);
}

/* 上传区域样式 */
.upload-container {
	margin: 16px 0;
	border: 2px dashed #d9d9d9;
	border-radius: 6px;
	background: #fafafa;
	transition: border-color 0.3s;
}

.upload-container:hover {
	border-color: var(--accent-color);
}

.upload-container .el-upload {
	width: 100%;
}

/* 隐藏ElementUI自动生成的文件列表和按钮 */
.el-upload__input {
	display: none !important;
}

.el-upload-list {
	display: none !important;
}

/* 确保拖拽区域样式正确 */
.el-upload-dragger {
	width: 100% !important;
	height: auto !important;
	border: none !important;
	background: transparent !important;
}

/* 表格容器自适应 */
.table-responsive {
	width: 100%;
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
}

/* 宽屏幕优化 */
@media (min-width: 1400px) {
	.el-table {
		min-width: 1400px;
	}


}

@media (min-width: 1600px) {
	.el-table {
		min-width: 1600px;
	}


}

/* 打印选项样式 */
.print-option-group {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 12px;
	margin-bottom: 20px;
}

.print-option {
	padding: 12px;
	border: 2px solid var(--border-light);
	border-radius: var(--border-radius);
	text-align: center;
	cursor: pointer;
	background: var(--bg-white);
	font-size: var(--font-size-small);
	font-weight: 500;
}

.print-option:hover, .print-option.active {
	border-color: var(--border-active);
	border-width: 3px;
}

.print-option.active {
	background-color: var(--bg-light);
}

/* 表格滚动容器样式 */
.table-container {
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	background: white;
}

.table-container::-webkit-scrollbar {
	height: 8px;
}

.table-container::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
	background: #c1c1c1;
	border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
	background: #a8a8a8;
}

/* 移动端表格滚动提示 */
@media (max-width: 768px) {
	.table-container::before {
		content: "← 左右滑动查看更多信息 →";
		display: block;
		text-align: center;
		font-size: 12px;
		color: #999;
		padding: 8px 0;
		background: #f8f9fa;
		border-bottom: 1px solid #e9ecef;
		border-radius: 8px 8px 0 0;
	}

	/* 打印选项移动端优化 */
	.print-option-group {
		grid-template-columns: 1fr !important;
		gap: 8px !important;
	}

	.print-option {
		padding: 8px !important;
		font-size: 12px !important;
	}
}


</style>
<div class="app-content-body sxgz-container">

	<div class="wrapper-md control" id="add">

		<div class="panel panel-default">
			<div class="panel-heading font-bold bg-white">
				<!-- 页面标题 -->
				<div style="margin-bottom: 10px;">
					<h2 style="margin: 0; color: var(--text-primary); font-size: 16px; font-weight: 600;">
						<i class="el-icon-document-copy" style="color: var(--primary-color); margin-right: 8px;"></i>
						实习盖章订单管理
						<el-tag v-if="userrow.uid === 1" type="warning" size="mini" style="margin-left: 8px;">管理员模式</el-tag>
					</h2>
					<p style="margin: 5px 0 0 0; color: var(--text-secondary); font-size: 14px;">
						<span v-if="userrow.uid === 1">管理员视图：查看和管理所有用户的实习证明盖章订单</span>
						<span v-else>管理和处理实习证明盖章订单，支持电子版和邮寄服务</span>
					</p>
				</div>

				<!-- 重要通知 -->
				<div class="notice-tag" style="margin-bottom: 10px;">
					<el-alert
						title="重要通知"
						type="warning"
						:closable="false"
						show-icon>
						<div slot="default">
							<div class="notice-title">
								<strong>纸质材料盖章邮寄地址：</strong>广东省广州市白云区均禾街道平沙村平沙东街63号 厂老师 手机号填你自己的 只接顺丰
							</div>
							<div class="notice-content">
								<p>• 文件内必须附上纸条：顾客的姓名、手机号、专业、盖章公司</p>
								<p>• 多文件盖章材料请压缩为一个文件，并命名为下单客户名称</p>
								<p>• 邮寄订单打印张数超过10张，务必选择对应数量，否则无法处理</p>
							</div>
						</div>
					</el-alert>
				</div>

				<!-- 操作按钮组 -->
				<div class="btn-group">
					<el-button type="primary" size="small" @click="showAddDialog" icon="el-icon-plus">新增订单</el-button>
					<a href="https://kdocs.cn/l/cjpHHdLz5JzE" target="_blank">
						<el-button type="success" size="small" icon="el-icon-download">拉取公司</el-button>
					</a>
					<el-button type="info" size="small" @click="get(1)" icon="el-icon-refresh">刷新数据</el-button>
					<el-button type="warning" @click="showExportDialog" icon="el-icon-download" size="small">导出数据</el-button>
				</div>

				<!-- 搜索区域 -->
				<div class="search-wrapper">
					<el-row :gutter="12">
						<el-col :xs="24" :sm="24" :md="12" :lg="12">
							<el-input placeholder="请输入搜索内容" size="small" v-model="cx.mh" @keyup.enter.native="get(1)" clearable>
								<el-select slot="prepend" v-model="cx.search" style="width: 120px" placeholder="搜索条件">
									<el-option label="全部" value=""></el-option>
									<el-option label="UID" value="uid" v-if="userrow.uid === 1"></el-option>
									<el-option label="用户名" value="username" v-if="userrow.uid === 1"></el-option>
									<el-option label="订单ID" value="oid" v-if="userrow.uid === 1"></el-option>
									<el-option label="客户姓名" value="customer_name"></el-option>
									<el-option label="公司名称" value="company_name"></el-option>
									<el-option label="订单号" value="order_no"></el-option>
									<el-option label="特殊要求" value="special_requirements"></el-option>
									<el-option label="管理员备注" value="admin_notes"></el-option>
								</el-select>
								<el-button slot="append" icon="el-icon-search" @click="get(1)">搜索</el-button>
							</el-input>
						</el-col>
						<el-col :xs="24" :sm="12" :md="8" :lg="8">
							<el-select v-model="cx.status" placeholder="订单状态" size="small" clearable @change="get(1)" style="width: 100%;">
								<el-option label="全部状态" value=""></el-option>
								<el-option label="待处理" value="pending"></el-option>
								<el-option label="处理中" value="processing"></el-option>
								<el-option label="已完成" value="completed"></el-option>
								<el-option label="已取消" value="cancelled"></el-option>
								<el-option label="失败" value="failed"></el-option>
								<el-option label="申请退款" value="refund_requested"></el-option>
								<el-option label="已退款" value="refunded"></el-option>
							</el-select>
						</el-col>
						<el-col :xs="24" :sm="12" :md="4" :lg="4">
							<el-button @click="resetSearch" icon="el-icon-refresh" size="small" style="width: 100%;">重置</el-button>
						</el-col>
					</el-row>
				</div>

				<!-- 批量操作区域 -->
				<div v-if="selectedOrders.length > 0" class="batch-actions" style="margin: 10px 0; padding: 8px 12px; background: var(--bg-light); border: 1px solid var(--border-color); border-radius: 4px;">
					<div style="display: flex; align-items: center; justify-content: space-between;">
						<div style="color: var(--primary-color); font-size: 14px;">
							<i class="el-icon-info" style="margin-right: 5px;"></i>
							已选中 {{ selectedOrders.length }} 个订单
						</div>
						<div>
							<el-button type="primary" size="mini" @click="showExportDialog" icon="el-icon-download">
								导出选中
							</el-button>
							<el-button size="mini" @click="clearSelection" icon="el-icon-close">
								清空选择
							</el-button>
						</div>
					</div>
				</div>

				<div class="table-responsive">
					<div class="table-container" style="overflow-x: auto; -webkit-overflow-scrolling: touch;">
						<el-table
							:data="row.data"
							style="width: 100%; min-width: 1000px;"
							@selection-change="handleSelectionChange"
							max-height="650"
							ref="multipleTable"
							border
							stripe
							size="small"
							:header-cell-style="{padding: '8px 6px', fontSize: '13px', fontWeight: '600', background: '#f8f9fa'}"
							:cell-style="{padding: '8px 6px', fontSize: '12px', lineHeight: '1.4', whiteSpace: 'normal'}"
							v-loading="tableLoading"
							element-loading-text="加载中..."
							element-loading-spinner="el-icon-loading"
							:empty-text="row.data && row.data.length === 0 ? '暂无订单数据' : ''"
							empty-icon="el-icon-document-remove">
						<el-table-column
							type="selection"
							width="35">
						</el-table-column>
						<!-- 用户界面不显示订单ID -->
						<el-table-column
							label="操作"
							width="80"
							fixed="right">
							<template slot-scope="scope">
								<div class="action-buttons">
									<el-dropdown trigger="click" @command="(cmd) => handleCommand(cmd, scope.row)" placement="bottom">
										<el-button type="primary" icon="el-icon-more" size="mini">操作</el-button>
										<el-dropdown-menu slot="dropdown">
											<el-dropdown-item v-if="scope.row.status === 'pending' || scope.row.status === 'processing'"
												command="upload" icon="el-icon-upload2">补充文件</el-dropdown-item>
											<el-dropdown-item v-if="scope.row.status === 'completed' || scope.row.status === 'delivered'"
												command="download" icon="el-icon-download">下载文件</el-dropdown-item>
											<el-dropdown-item v-if="scope.row.status === 'pending' || scope.row.status === 'processing'"
												command="refund" icon="el-icon-money" divided>申请退款</el-dropdown-item>
										</el-dropdown-menu>
									</el-dropdown>
								</div>
							</template>
						</el-table-column>
						<!-- 管理员专用UID列 -->
						<el-table-column label="用户ID" width="80" v-if="userrow.uid === 1">
							<template slot-scope="scope">
								<el-tag size="mini" type="info">{{ scope.row.uid || 'N/A' }}</el-tag>
							</template>
						</el-table-column>

						<el-table-column label="订单号" width="110" show-overflow-tooltip>
							<template slot-scope="scope">
								<div class="order-no-cell">
									{{scope.row.order_no}}
								</div>
							</template>
						</el-table-column>
						<el-table-column
							label="客户信息"
							:width="userrow.uid === 1 ? 200 : 160"
							show-overflow-tooltip>
							<template slot-scope="scope">
								<div class="customer-info-cell" style="font-size: 12px; line-height: 1.4;">
									<!-- 管理员显示用户信息 -->
									<div v-if="userrow.uid === 1" style="background: var(--bg-light); padding: 2px 4px; margin-bottom: 3px; border-radius: 3px; border-left: 3px solid var(--primary-color);">
										<div style="font-size: 11px; color: var(--primary-color); font-weight: 600;">
											<i class="el-icon-user-solid" style="margin-right: 2px;"></i>
											用户: {{ scope.row.username || 'N/A' }}
											<span v-if="scope.row.user_realname">({{ scope.row.user_realname }})</span>
										</div>
									</div>

									<!-- 客户姓名 -->
									<div class="customer-name" style="margin-bottom: 3px;">
										<i class="el-icon-user" style="color: var(--primary-color); margin-right: 3px; font-size: 12px;"></i>
										<span style="font-weight: 600; color: var(--text-primary);">{{scope.row.customer_name}}</span>
									</div>

									<!-- 联系方式 -->
									<div style="margin-bottom: 3px;">
										<div v-if="scope.row.customer_phone" style="color: var(--text-secondary); margin-bottom: 2px; font-size: 11px;">
											<i class="el-icon-phone" style="margin-right: 3px; color: var(--success-color);"></i>
											{{scope.row.customer_phone}}
										</div>
										<div v-if="scope.row.customer_email" style="color: var(--text-secondary); margin-bottom: 2px; font-size: 11px;">
											<i class="el-icon-message" style="margin-right: 3px; color: var(--accent-color);"></i>
											{{scope.row.customer_email}}
										</div>
									</div>

									<!-- 收货地址 -->
									<div v-if="scope.row.customer_address"
										 style="margin-top: 4px; padding: 3px 5px; background: var(--bg-light); border-radius: 3px; border-left: 2px solid var(--text-secondary);">
										<div style="font-size: 9px; color: var(--text-secondary); margin-bottom: 1px;">
											<i class="el-icon-location" style="margin-right: 2px;"></i>收货地址
										</div>
										<div style="color: var(--text-secondary); font-size: 10px; line-height: 1.3; word-break: break-all;">
											{{scope.row.customer_address.replace(/\n/g, ' ')}}
										</div>
									</div>

									<!-- 快递信息 -->
									<div v-if="scope.row.courier_company || scope.row.tracking_number"
										 style="margin-top: 3px; font-size: 10px; color: var(--accent-color);">
										<i class="el-icon-truck" style="margin-right: 3px;"></i>
										<span v-if="scope.row.courier_company">{{ scope.row.courier_company }}</span>
										<span v-if="scope.row.tracking_number" style="margin-left: 4px;">{{ scope.row.tracking_number }}</span>
									</div>
								</div>
							</template>
						</el-table-column>
						<el-table-column label="公司信息" width="180" show-overflow-tooltip>
							<template slot-scope="scope">
								<div class="company-info-cell" style="font-size: 12px; line-height: 1.4;">
									<!-- 主要公司 -->
									<div class="main-company" style="margin-bottom: 4px;">
										<i class="el-icon-office-building" style="color: var(--primary-color); margin-right: 4px; font-size: 13px;"></i>
										<span style="font-weight: 600; color: var(--text-primary);">{{scope.row.company_name}}</span>
									</div>

									<!-- 仅营业执照模式 -->
									<div v-if="scope.row.only_business_license == '1'"
										 style="margin-top: 4px;">
										<div style="display: flex; align-items: center; margin-bottom: 3px;">
											<i class="el-icon-document" style="color: var(--danger-color); margin-right: 4px; font-size: 11px;"></i>
											<el-tag size="mini" type="danger" style="font-size: 9px; padding: 1px 4px; font-weight: 600;">
												仅营业执照服务
											</el-tag>
										</div>
										<!-- 显示营业执照公司详情 -->
										<div v-if="getLicenseCompaniesFromOrder(scope.row).length > 0"
											 style="background: var(--bg-light); padding: 4px 6px; border-radius: 3px; border-left: 2px solid var(--danger-color);">
											<div v-for="(company, index) in getLicenseCompaniesFromOrder(scope.row)"
												 :key="index"
												 style="font-size: 10px; color: var(--text-secondary); line-height: 1.2; margin-bottom: 1px;">
												<span style="color: var(--text-primary); font-weight: 500;">{{ company.name }}</span>
												<span style="color: var(--danger-color); font-weight: 600; margin-left: 4px;">¥{{ company.price }}</span>
											</div>
										</div>
									</div>

									<!-- 普通公司+营业执照模式 -->
									<div v-else-if="scope.row.business_license == '1' && getLicenseCompaniesFromOrder(scope.row)"
										 class="license-companies" style="background: var(--bg-light); padding: 4px 6px; border-radius: 4px; border-left: 3px solid var(--warning-color); margin-top: 4px;">
										<div style="display: flex; align-items: center; margin-bottom: 3px;">
											<i class="el-icon-document" style="color: var(--warning-color); margin-right: 4px; font-size: 11px;"></i>
											<span style="font-size: 10px; color: var(--warning-color); font-weight: 600;">附加营业执照</span>
										</div>
										<div style="padding-left: 15px;">
											<div v-for="(company, index) in getLicenseCompaniesFromOrder(scope.row)"
												 :key="index"
												 style="font-size: 10px; color: var(--text-secondary); line-height: 1.2; margin-bottom: 1px;">
												<span style="color: var(--text-primary); font-weight: 500;">{{ company.name }}</span>
												<span style="color: var(--danger-color); font-weight: 600; margin-left: 4px;">¥{{ company.price }}</span>
											</div>
										</div>
									</div>
								</div>
							</template>
						</el-table-column>
						<el-table-column
							label="业务类型"
							width="100">
							<template slot-scope="scope">
								<div style="font-size: 11px; line-height: 1.4;">
									<el-tag size="mobile" :type="getServiceTypeColor(scope.row.service_type)" style="margin-bottom: 3px; font-size: 12px; padding: 2px 5px;">
										{{getServiceTypeText(scope.row.service_type)}}
									</el-tag>
									<div v-if="scope.row.only_business_license === '1'" style="margin-top: 3px;">
										<el-tag size="mobile" type="info" style="font-size: 11px; padding: 2px 4px;">
											仅执照
										</el-tag>
									</div>
									<div v-else-if="scope.row.business_license === '1'" style="margin-top: 3px;">
										<el-tag size="mobile" type="warning" style="font-size: 11px; padding: 2px 4px;">
											+执照
										</el-tag>
									</div>
								</div>
							</template>
						</el-table-column>
						<el-table-column
							label="服务详情"
							width="180"
							show-overflow-tooltip>
							<template slot-scope="scope">
								<div class="service-detail-cell" style="font-size: 12px; line-height: 1.4;">
									<!-- 服务类型 -->
									<div style="margin-bottom: 4px;">
										<el-tag :type="getServiceTypeTag(scope.row.service_type)" size="mini">
											{{ getServiceTypeText(scope.row.service_type) }}
										</el-tag>
									</div>

									<!-- 材料方式 -->
									<div v-if="scope.row.material_type" style="margin-bottom: 3px; font-size: 10px; color: #606266;">
										<i class="el-icon-folder" style="margin-right: 3px; color: #909399;"></i>
										<span>{{ getMaterialTypeText(scope.row.material_type) }}</span>
									</div>

									<!-- 文件信息 -->
									<div v-if="scope.row.original_filename" style="margin-bottom: 3px; font-size: 10px; color: #606266;">
										<i class="el-icon-document" style="margin-right: 3px; color: #409EFF;"></i>
										<span style="word-break: break-all;">{{ scope.row.original_filename.length > 15 ? scope.row.original_filename.substring(0, 15) + '...' : scope.row.original_filename }}</span>
									</div>

									<!-- 打印信息 -->
									<div v-if="scope.row.print_copies > 0" style="margin-bottom: 3px; font-size: 10px; color: #E6A23C;">
										<i class="el-icon-printer" style="margin-right: 3px;"></i>
										<span>{{ scope.row.print_copies }}份</span>
									</div>

									<!-- 材料类型（无文件无快递时显示） -->
									<div v-if="!scope.row.original_filename && !scope.row.courier_company" style="color: #909399; display: flex; align-items: center; font-size: 10px;">
										<i class="el-icon-folder-opened" style="margin-right: 4px; flex-shrink: 0;"></i>
										<span>{{getMaterialTypeText(scope.row.material_type)}}</span>
									</div>

									<!-- 特殊要求（简化显示） -->
									<div v-if="getCleanSpecialRequirements(scope.row.special_requirements)"
										 style="margin-top: 3px; padding: 2px 4px; background: #fff7e6; border-radius: 2px; font-size: 9px; color: #E6A23C;">
										<i class="el-icon-warning" style="margin-right: 2px;"></i>
										{{ getCleanSpecialRequirements(scope.row.special_requirements) }}
									</div>
								</div>
							</template>
						</el-table-column>
						<el-table-column
							label="价格明细"
							width="110"
							show-overflow-tooltip>
							<template slot-scope="scope">
								<div style="font-size: 12px; line-height: 1.4;">
									<div v-if="scope.row.base_price > 0" style="margin-bottom: 2px;">基础: ¥{{scope.row.base_price}}</div>
									<div v-if="scope.row.print_price > 0" style="margin-bottom: 2px;">打印: ¥{{scope.row.print_price}}</div>
									<div v-if="scope.row.license_price > 0" style="margin-bottom: 2px;">执照: ¥{{scope.row.license_price}}</div>
									<div style="font-weight: 600; color: var(--danger-color); border-top: 1px solid var(--border-color); padding-top: 2px; margin-top: 2px; font-size: 12px;">
										总计: ¥{{scope.row.total_price}}
									</div>
								</div>
							</template>
						</el-table-column>
						<el-table-column
							label="文件/快递"
							width="130"
							show-overflow-tooltip>
							<template slot-scope="scope">
								<div style="font-size: 12px; line-height: 1.4; white-space: normal; word-break: break-word;">
									<!-- 文件信息 -->
									<div v-if="scope.row.original_filename" style="margin-bottom: 4px;">
										<div style="display: flex; align-items: flex-start; margin-bottom: 2px;">
											<i :class="getFileIcon(scope.row.original_filename)" style="color: var(--accent-color); margin-right: 4px; margin-top: 1px; flex-shrink: 0; font-size: 12px;"></i>
											<span style="word-break: break-all; flex: 1;">{{scope.row.original_filename}}</span>
										</div>
										<div v-if="scope.row.file_size" style="font-size: 12px; color: var(--text-muted); margin-left: 16px;">
											{{getFileSize(scope.row.file_size)}}
										</div>
									</div>

									<!-- 快递信息 -->
									<div v-if="scope.row.courier_company" style="margin-bottom: 3px;">
										<div style="display: flex; align-items: center; margin-bottom: 2px;">
											<i class="el-icon-truck" style="color: var(--success-color); margin-right: 4px; flex-shrink: 0; font-size: 12px;"></i>
											<span style="font-weight: 500;">{{getCourierName(scope.row.courier_company)}}</span>
										</div>
										<!-- 快递单号 -->
										<div v-if="scope.row.tracking_number" style="font-size: 12px; color: var(--text-secondary); margin-left: 16px;">
											<span style="font-family: 'Courier New', monospace;">{{scope.row.tracking_number}}</span>
										</div>
									</div>

									<!-- 材料类型（无文件无快递时显示） -->
									<div v-if="!scope.row.original_filename && !scope.row.courier_company" style="color: var(--text-secondary); display: flex; align-items: center;">
										<i class="el-icon-folder-opened" style="margin-right: 4px; flex-shrink: 0; font-size: 12px;"></i>
										<span>{{getMaterialTypeText(scope.row.material_type)}}</span>
									</div>
								</div>
							</template>
						</el-table-column>
						<el-table-column
							label="创建时间"
							width="120"
							show-overflow-tooltip>
							<template slot-scope="scope">
								<div class="time-cell">
									{{formatDate(scope.row.created_at)}}
								</div>
							</template>
						</el-table-column>
						<el-table-column
							label="订单状态"
							width="80">
							<template slot-scope="scope">
								<div style="font-size: 12px;">
									<el-tag
										:type="getOrderStatusType(scope.row.status)"
										size="mini"
										:effect="scope.row.status === 'refund_requested' || scope.row.status === 'refunded' ? 'dark' : 'plain'"
										style="font-size: 12px;">
										<i v-if="scope.row.status === 'refund_requested'" class="el-icon-warning" style="margin-right: 3px;"></i>
										<i v-if="scope.row.status === 'refunded'" class="el-icon-circle-check" style="margin-right: 3px;"></i>
										{{ getOrderStatusText(scope.row.status) }}
									</el-tag>
								</div>
							</template>
						</el-table-column>
						<el-table-column
							label="备注信息"
							width="140"
							show-overflow-tooltip>
							<template slot-scope="scope">
								<div style="font-size: 12px; line-height: 1.4; white-space: normal; word-break: break-word;">
									<!-- 退款相关信息 -->
									<div v-if="scope.row.status === 'refund_requested' || scope.row.status === 'refunded'">
										<div v-if="scope.row.refund_reason" style="color: var(--warning-color); margin-bottom: 3px; white-space: pre-wrap; word-wrap: break-word; word-break: break-word; overflow-wrap: break-word;">
											<span style="font-weight: 600;">退款原因:</span>
											{{scope.row.refund_reason}}
										</div>
										<div v-if="scope.row.admin_notes && scope.row.status === 'refunded'" style="color: var(--success-color); margin-bottom: 3px; white-space: pre-wrap; word-wrap: break-word; word-break: break-word; overflow-wrap: break-word;">
											<span style="font-weight: 600;">处理回复:</span>
											{{scope.row.admin_notes}}
										</div>
										<div v-if="scope.row.admin_notes && scope.row.status === 'pending'" style="color: var(--danger-color); margin-bottom: 3px; white-space: pre-wrap; word-wrap: break-word; word-break: break-word; overflow-wrap: break-word;">
											<span style="font-weight: 600;">拒绝原因:</span>
											{{scope.row.admin_notes}}
										</div>
									</div>
									<!-- 普通备注信息 -->
									<div v-else>
										<div v-if="scope.row.remarks" style="color: var(--text-secondary); margin-bottom: 3px; white-space: pre-wrap; word-wrap: break-word; word-break: break-word; overflow-wrap: break-word;">
											<span style="font-weight: 600;">管理员:</span>
											{{scope.row.remarks}}
										</div>
										<div v-if="scope.row.customer_notes" style="color: var(--text-muted); white-space: pre-wrap; word-wrap: break-word; word-break: break-word; overflow-wrap: break-word;">
											<span style="font-weight: 600;">客户:</span>
											{{scope.row.customer_notes}}
										</div>
										<div v-if="!scope.row.remarks && !scope.row.customer_notes" style="color: var(--text-muted);">
											无备注
										</div>
									</div>
								</div>
							</template>
						</el-table-column>

					</el-table>
				</div>
			</div>

			<!-- 管理员统计信息 -->
			<div v-if="userrow.uid === 1" style="background: var(--bg-light); padding: 10px; margin: 10px 0; border-radius: 6px; border-left: 4px solid var(--primary-color);">
				<div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 10px;">
					<div style="color: var(--text-secondary); font-size: 13px;">
						<i class="el-icon-info" style="color: var(--primary-color); margin-right: 5px;"></i>
						<strong>管理员视图</strong> - 显示所有用户订单
					</div>
					<div style="display: flex; gap: 15px; font-size: 12px;">
						<span style="color: var(--success-color);">
							<i class="el-icon-user"></i>
							当前页用户数: {{ getUniqueUsersCount() }}
						</span>
						<span style="color: var(--accent-color);">
							<i class="el-icon-document"></i>
							总订单数: {{ row.total }}
						</span>
					</div>
				</div>
			</div>

			<div class="pagination-wrapper">
				<el-pagination
					@current-change="handleCurrentChange"
					:current-page="row.current_page"
					:page-size="15"
					background
					small
					layout="total, prev, pager, next, jumper"
					:total="row.total">
				</el-pagination>
			</div>
					<!-- 新增订单模态框 -->
<el-dialog 
	title="新增订单" 
	:visible.sync="dialogVisible" 
	:fullscreen="windowWidth < 768" 
	:width="windowWidth < 992 ? '90%' : '70%'" 
	:before-close="handleClose" 
	custom-class="dialog-form"
	:top="windowWidth < 768 ? '0' : '5vh'">
	<div class="panel-body">
		<!-- 步骤条 -->
		<el-steps :active="activeStep" finish-status="success" align-center :simple="windowWidth <= 768" size="small">
			<el-step title="服务类型"></el-step>
			<el-step title="选择公司"></el-step>
			<el-step title="材料处理"></el-step>
			<el-step title="收货信息"></el-step>
			<el-step title="打印选项"></el-step>
			<el-step title="确认订单"></el-step>
		</el-steps>
		
		<!-- 步骤1: 选择服务类型 -->
		<div v-if="activeStep === 0" class="step-container">
			<div class="step-form-title">选择服务类型</div>
			<div class="step-form-subtitle">根据您的需求选择合适的服务类型</div>

			<el-row :gutter="16" style="margin-top: 16px;">
				<el-col :xs="24" :sm="8" :style="{ marginBottom: windowWidth <= 768 ? '12px' : '0' }">
					<div :class="['service-type-card', serviceType === 'mail' ? 'active' : '']" @click="selectServiceType('mail')">
						<i class="el-icon-s-promotion"></i>
						<h3>邮寄服务</h3>
						<p>快递寄送到指定地址</p>
					</div>
				</el-col>
				<el-col :xs="24" :sm="8" :style="{ marginBottom: windowWidth <= 768 ? '12px' : '0' }">
					<div :class="['service-type-card', serviceType === 'electronic' ? 'active' : '']" @click="selectServiceType('electronic')">
						<i class="el-icon-document"></i>
						<h3>电子版</h3>
						<p>仅提供电子文件</p>
					</div>
				</el-col>
				<el-col :xs="24" :sm="8">
					<div :class="['service-type-card', serviceType === 'both' ? 'active' : '']" @click="selectServiceType('both')">
						<i class="el-icon-s-cooperation"></i>
						<h3>邮寄+电子版</h3>
						<p>电子文件+实物邮寄</p>
					</div>
				</el-col>
			</el-row>

			<div class="step-actions">
				<span></span>
				<el-button type="primary" @click="nextStep" :disabled="!serviceType" size="small">下一步</el-button>
			</div>
		</div>
		
		<!-- 步骤2: 选择公司 -->
		<div v-if="activeStep === 1" class="step-container">
			<div class="step-form-title">选择公司</div>
			<div class="step-form-subtitle">请选择需要盖章的公司</div>

			<el-form label-position="top" size="small">
				<el-form-item label="选择公司">
					<el-select
						style="width: 100%;"
						v-model="cid"
						@change="handleCompanyChange"
						filterable
						placeholder="请选择公司，可输入搜索"
						value-key="cid"
						:disabled="onlyBusinessLicense"
						size="small">
						<el-option
							v-for="class2 in class1"
							:key="class2.cid"
							:label="`${class2.name} - ¥${class2.price}`"
							:value="class2">
						</el-option>
					</el-select>
					<div v-if="onlyBusinessLicense" style="color: #E6A23C; margin-top: 4px; font-size: 12px;">
						已选择"仅需要营业执照"，使用指定营业执照服务公司
					</div>
				</el-form-item>

				<div v-if="businessLicenseOption" style="margin-top: 16px;" class="license-options">
					<el-checkbox
						v-model="businessLicenseEnabled"
						:disabled="onlyBusinessLicense"
						size="small">
						需要营业执照
						<span style="color: #F56C6C; font-size: 12px;">(额外收费)</span>
					</el-checkbox>
					<el-checkbox
						v-model="onlyBusinessLicense"
						style="margin-left: 16px; margin-top: 8px;"
						size="small">
						仅需要营业执照
						<span style="color: #F56C6C; font-size: 12px;">(只收营业执照费)</span>
					</el-checkbox>

					<!-- 营业执照公司选择 -->
					<div v-if="businessLicenseEnabled || onlyBusinessLicense" style="margin-top: 12px;">
						<div style="margin-bottom: 8px; font-size: 13px; color: #606266;">
							<i class="el-icon-office-building"></i> 选择营业执照公司：
						</div>
						<el-select
							v-model="selectedLicenseCompanies"
							multiple
							placeholder="请选择营业执照公司"
							style="width: 100%;"
							size="small"
							@change="updateLicensePrice">
							<el-option
								v-for="company in licenseCompanies"
								:key="company.cid"
								:label="`${company.name} - ¥${company.price}`"
								:value="company.cid">
								<span style="float: left">{{ company.name }}</span>
								<span style="float: right; color: #8492a6; font-size: 13px">¥{{ company.price }}</span>
							</el-option>
						</el-select>
						<div v-if="selectedLicenseCompanies.length > 0" style="margin-top: 6px; font-size: 12px; color: #909399;">
							已选择 {{ selectedLicenseCompanies.length }} 个营业执照公司，总费用：
							<span style="color: #f56c6c; font-weight: bold;">¥{{ (totalLicensePrice * (userrow.addprice || 1)).toFixed(2) }}</span>
							<span v-if="(userrow.addprice || 1) !== 1" style="margin-left: 5px;">
								(原价: ¥{{ totalLicensePrice.toFixed(2) }})
							</span>
						</div>
					</div>
				</div>
			</el-form>
			
			<div class="step-actions">
				<el-button @click="prevStep" size="small">上一步</el-button>
				<el-button type="primary" @click="goToNextStepAfterCompany" :disabled="!canGoToNextStep" :loading="companyLoading" size="small">下一步</el-button>
			</div>
		</div>
		
		<!-- 步骤3: 材料上传/邮寄 -->
		<div v-if="activeStep === 2" class="step-container">
			<div class="step-form-title">材料准备</div>
			<div class="step-form-subtitle" v-if="onlyBusinessLicense">
				<span style="color: #E6A23C; font-weight: bold;">您已选择仅需要营业执照，无需上传材料</span>
			</div>
			<div class="step-form-subtitle" v-else>
				<span v-if="serviceType === 'electronic'">请上传需要盖章的文件</span>
				<span v-else>请选择材料提供方式</span>
			</div>
			
			<div v-if="onlyBusinessLicense" style="margin: 30px 0; text-align: center;">
				<el-alert
					title="仅需要营业执照服务"
					type="info"
					description="您选择了仅需要营业执照服务，将直接生成营业执照电子版并发送至您的邮箱。"
					show-icon
					:closable="false">
				</el-alert>
			</div>
			
			<div v-else-if="serviceType === 'electronic'" class="upload-container">
				<el-upload
					action="/sxgz/api.php?action=upload_file"
					:on-success="handleSupplementUploadSuccess"
					:on-error="handleSupplementUploadError"
					:before-upload="beforeUpload"
					:show-file-list="true"
					drag>
					<i class="el-icon-upload"></i>
					<div class="el-upload__text">
						将文件拖到此处，或<em>点击上传</em>
					</div>
					<div class="el-upload__tip" slot="tip">
						支持格式：PDF、Word、图片、压缩包 | 大小限制：10MB以内
					</div>
				</el-upload>

				<div v-if="supplementFile" style="margin-top: 15px; padding: 12px; background: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px;">
					<div style="display: flex; align-items: center; justify-content: space-between;">
						<div style="display: flex; align-items: center;">
							<i class="el-icon-document" style="color: #67C23A; margin-right: 8px;"></i>
							<span style="color: #67C23A; font-weight: 600;">{{ supplementFile.name }}</span>
							<span style="color: #909399; margin-left: 10px;">({{ (supplementFile.size / 1024).toFixed(2) }} KB)</span>
						</div>
						<el-button type="text" icon="el-icon-delete" @click="clearUploadedFile" style="color: #f56c6c;">
							删除
						</el-button>
					</div>
				</div>
			</div>
			
			<div v-else>
				<div class="material-option">
					<el-radio v-model="materialOption" label="upload">
						<div>
							<div style="font-weight: bold; margin-bottom: 5px;">在线上传文件</div>
							<div style="color: #606266;">上传电子版文件，我们将为您打印并盖章</div>
						</div>
					</el-radio>
				</div>
				
				<div class="material-option">
					<el-radio v-model="materialOption" label="mail">
						<div>
							<div style="font-weight: bold; margin-bottom: 5px;">邮寄纸质文件</div>
							<div style="color: #606266;">您将纸质文件邮寄到我们的工作室，我们将为您盖章后寄回</div>
						</div>
					</el-radio>
				</div>
				
				<div v-if="materialOption === 'upload'" style="margin-top: 20px;" class="upload-container">
					<el-upload
						action="/sxgz/api.php?action=upload_file"
						:on-success="handleSupplementUploadSuccess"
						:on-error="handleSupplementUploadError"
						:before-upload="beforeUpload"
						:show-file-list="true"
						drag>
						<i class="el-icon-upload"></i>
						<div class="el-upload__text">
							将文件拖到此处，或<em>点击上传</em>
						</div>
						<div class="el-upload__tip" slot="tip">
							支持格式：PDF、Word、图片、压缩包 | 大小限制：10MB以内
						</div>
					</el-upload>

					<div v-if="supplementFile" style="margin-top: 15px; padding: 12px; background: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px;">
						<div style="display: flex; align-items: center; justify-content: space-between;">
							<div style="display: flex; align-items: center;">
								<i class="el-icon-document" style="color: #67C23A; margin-right: 8px;"></i>
								<span style="color: #67C23A; font-weight: 600;">{{ supplementFile.name }}</span>
								<span style="color: #909399; margin-left: 10px;">({{ (supplementFile.size / 1024).toFixed(2) }} KB)</span>
							</div>
							<el-button type="text" icon="el-icon-delete" @click="clearUploadedFile" style="color: #f56c6c;">
								删除
							</el-button>
						</div>
					</div>
				</div>
			</div>
			
			<div class="step-actions">
				<el-button @click="prevStep" size="medium">
					<i class="el-icon-arrow-left"></i> 上一步
				</el-button>
				<el-button type="primary" @click="validateMaterialAndContinue" size="medium">
					<i class="el-icon-arrow-right"></i> 下一步
				</el-button>
			</div>
		</div>
		
		<!-- 步骤4: 收货信息 -->
		<div v-if="activeStep === 3" class="step-container">
			<div class="step-form-title">回货信息</div>
			<div class="step-form-subtitle" v-if="onlyBusinessLicense">请填写客户姓名和接收营业执照的邮箱</div>
			<div class="step-form-subtitle" v-else-if="serviceType === 'electronic'">请填写客户姓名和接收电子版文件的邮箱</div>
			<div class="step-form-subtitle" v-else>请填写收件人信息</div>
			
			<el-form label-position="top">
				<!-- 仅需要营业执照或电子版服务需要客户姓名 -->
				<el-form-item label="客户姓名" v-if="onlyBusinessLicense || serviceType === 'electronic'" required>
					<el-input placeholder="请输入客户姓名" v-model="userinfo">
						<template slot="prepend">
							<i class="el-icon-user"></i>
						</template>
					</el-input>
					<div v-if="onlyBusinessLicense" style="font-size: 12px; color: #909399; margin-top: 5px;">
						此姓名将作为营业执照上的法人姓名，请确保准确无误
					</div>
					<div v-else-if="serviceType === 'electronic'" style="font-size: 12px; color: #909399; margin-top: 5px;">
						请输入接收电子版文件的客户姓名
					</div>
				</el-form-item>
			
				<!-- 仅需要营业执照或电子版需要邮箱 -->
				<el-form-item label="邮箱地址" v-if="onlyBusinessLicense || serviceType === 'electronic' || (serviceType === 'both' && materialOption === 'upload')" required>
					<el-input placeholder="请输入接收电子版文件的邮箱地址" v-model="userEmail" type="email">
						<template slot="prepend">
							<i class="el-icon-message"></i>
						</template>
					</el-input>
					<div v-if="onlyBusinessLicense" style="font-size: 12px; color: #909399; margin-top: 5px;">
						营业执照将发送至您提供的邮箱
					</div>
					<div v-else-if="serviceType === 'electronic'" style="font-size: 12px; color: #909399; margin-top: 5px;">
						电子版文件将发送至您提供的邮箱
					</div>
				</el-form-item>
				
				<!-- 仅在非仅营业执照模式且非电子版模式下显示收件人信息 -->
				<template v-if="!onlyBusinessLicense && serviceType !== 'electronic'">
					<el-form-item label="收件人姓名">
						<el-input placeholder="请输入收件人姓名" v-model="userinfo" style="width: 100%;">
							<template slot="prepend">
								<i class="el-icon-user"></i>
							</template>
						</el-input>
					</el-form-item>
					
					<el-form-item label="手机号">
						<el-input placeholder="请输入收件人手机号" v-model="userinfo2" maxlength="11" style="width: 100%;">
							<template slot="prepend">
								<i class="el-icon-phone"></i>
							</template>
						</el-input>
					</el-form-item>
					
					<el-form-item label="收件地址">
						<el-input placeholder="请输入详细收件地址（省市区镇详细地址）" v-model="userinfo3" type="textarea" :rows="2" style="width: 100%;"></el-input>
					</el-form-item>
					
					<!-- 新增快递单号字段，仅在选择"纸质文件邮寄"时显示 -->
					<el-form-item label="快递单号" v-if="materialOption === 'mail'" required>
						<el-input placeholder="请输入寄到工作室的快递单号" v-model="trackingNumber" style="width: 100%;">
							<template slot="prepend">
								<el-select v-model="courierCompany" placeholder="快递公司" class="courier-select">
									<el-option label="顺丰" value="SF"></el-option>
									<el-option label="圆通" value="YTO"></el-option>
									<el-option label="中通" value="ZTO"></el-option>
									<el-option label="申通" value="STO"></el-option>
									<el-option label="韵达" value="YD"></el-option>
									<el-option label="京东" value="JD"></el-option>
									<el-option label="EMS" value="EMS"></el-option>
									<el-option label="其他" value="其他"></el-option>
								</el-select>
							</template>
						</el-input>
						<div style="font-size: 12px; color: #909399; margin-top: 5px;">
							必填！请输入您寄送纸质材料到工作室的快递单号，以便我们查收
						</div>
					</el-form-item>
				</template>
			</el-form>
			
			<div class="step-actions">
				<el-button @click="prevStep" size="medium">
					<i class="el-icon-arrow-left"></i> 上一步
				</el-button>
				<el-button type="primary" @click="validateAddressAndContinue" size="medium">
					<i class="el-icon-arrow-right"></i> 下一步
				</el-button>
			</div>
		</div>
		
		<!-- 步骤5: 打印选项 -->
		<div v-if="activeStep === 4" class="step-container">
			<div class="step-form-title">打印选项</div>
			<div class="step-form-subtitle" v-if="onlyBusinessLicense">仅需要营业执照服务确认</div>
			<div class="step-form-subtitle" v-else-if="serviceType === 'electronic'">电子版服务信息确认</div>
			<div class="step-form-subtitle" v-else>请选择打印相关的选项</div>
			
			<el-form label-position="top">
				<!-- 仅需要营业执照提示信息 -->
				<div v-if="onlyBusinessLicense" style="margin: 20px 0; text-align: center;">
					<el-alert
						title="仅需要营业执照服务无需选择打印选项"
						type="info"
						description="您将只收到电子版营业执照，将通过邮件发送至您提供的邮箱地址。"
						show-icon
						:closable="false">
					</el-alert>
				</div>
				
				<!-- 只有非电子版且非仅营业执照服务才显示打印张数 -->
				<el-form-item label="打印张数" v-if="!onlyBusinessLicense && serviceType !== 'electronic'">
					<el-input-number v-model="num" :min="1" style="width: 100%;"></el-input-number>
					<div style="font-size: 12px; color: #909399; margin-top: 5px;">
						<span v-if="num > 10" style="color: #F56C6C;">注意: 超过10张将额外收取每张0.5元的费用</span>
						<span v-else>10张以下无额外费用</span>
					</div>
				</el-form-item>
				
				<!-- 配送方式显示 - 根据服务类型显示不同的选项 -->
				<div class="checkbox-group-title" v-if="!onlyBusinessLicense">配送方式</div>
				<div style="margin-bottom: 15px;" v-if="!onlyBusinessLicense">
					<el-tag v-if="serviceType === 'electronic'" type="info" effect="plain">无需工作室寄给客户只要电子版</el-tag>
					<el-tag v-else-if="serviceType === 'both'" type="success" effect="plain">需要工作室寄给客户，同时也需要电子版</el-tag>
					<el-tag v-else type="primary" effect="plain">需要工作室寄给客户</el-tag>
					<div style="font-size: 12px; color: #909399; margin-top: 5px;">
						配送方式已根据您选择的服务类型确定
					</div>
				</div>
				
				<!-- 仅非电子版服务且非仅营业执照才显示印刷选项 -->
				<template v-if="!onlyBusinessLicense && serviceType !== 'electronic'">
					<div class="checkbox-group-title">印刷选项</div>
					<div class="print-option-group">
						<div v-for="(option, index) in getPrintOptions()" :key="index"
							:class="['print-option', selectedOptions.includes(option.name) ? 'active' : '']"
							@click="togglePrintOption(option.name)">
							{{ option.name }}
						</div>
					</div>
				</template>
				
				<div class="checkbox-group-title">其他说明</div>
				<el-form-item>
					<el-input type="textarea" :rows="3" placeholder="请输入详细说明或特殊要求" v-model="remarks"></el-input>
				</el-form-item>
			</el-form>
			
			<div class="step-actions">
				<el-button @click="prevStep" size="medium">
					<i class="el-icon-arrow-left"></i> 上一步
				</el-button>
				<el-button type="primary" @click="nextStep" size="medium">
					<i class="el-icon-arrow-right"></i> 下一步
				</el-button>
			</div>
		</div>
		
		<!-- 步骤6: 确认订单 -->
		<div v-if="activeStep === 5" class="step-container">
			<div class="step-form-title">确认订单信息</div>
			<div class="step-form-subtitle">请核对以下订单信息</div>
			
			<el-card class="box-card" style="margin-bottom: 20px;">
				<div slot="header" class="clearfix">
					<span style="font-weight: bold;">服务信息</span>
				</div>
				<div style="margin-bottom: 15px;">
					<div v-if="!onlyBusinessLicense"><b>服务类型:</b> {{ getServiceTypeText(serviceType) }}</div>
					<div v-if="serviceType !== 'electronic' && !onlyBusinessLicense"><b>材料方式:</b> {{ materialOption === 'upload' ? '在线上传文件' : '邮寄纸质文件' }}</div>
					<div v-if="!onlyBusinessLicense"><b>盖章公司:</b> {{ cid ? cid.name : '未选择' }}</div>

					<!-- 营业执照信息显示 -->
					<div v-if="onlyBusinessLicense" style="color: #E6A23C; font-weight: bold; margin-bottom: 10px;">
						<i class="el-icon-office-building"></i> <b>仅需要营业执照服务</b>
					</div>
					<div v-if="onlyBusinessLicense && originalCompany" style="margin-bottom: 8px; color: #909399;">
						<b>原选择公司:</b> {{ originalCompany.name }}
					</div>

					<!-- 营业执照公司列表 -->
					<div v-if="(businessLicenseEnabled || onlyBusinessLicense) && selectedLicenseCompanies.length > 0">
						<b>营业执照公司:</b>
						<div style="margin-left: 20px; margin-top: 5px;">
							<div v-for="(cid, index) in selectedLicenseCompanies" :key="cid" style="margin-bottom: 3px;">
								<span style="color: #409EFF;">{{ getLicenseCompanyName(cid) }}</span>
								<span style="color: #909399; margin-left: 10px;">¥{{ getLicenseCompanyPrice(cid) }}</span>
							</div>
							<div style="margin-top: 8px; padding: 5px 10px; background: #f0f9ff; border-radius: 4px; font-size: 13px;">
								<b>营业执照总费用:</b>
								<span style="color: #f56c6c;">¥{{ (totalLicensePrice * (userrow.addprice || 1)).toFixed(2) }}</span>
								<span v-if="(userrow.addprice || 1) !== 1" style="color: #909399; margin-left: 5px;">
									(原价: ¥{{ totalLicensePrice.toFixed(2) }})
								</span>
							</div>
						</div>
					</div>

					<div v-if="businessLicenseEnabled && !onlyBusinessLicense && selectedLicenseCompanies.length === 0" style="color: #f56c6c;">
						<b>营业执照:</b> 已勾选但未选择公司
					</div>
				</div>
			</el-card>
			
			<el-card class="box-card" style="margin-bottom: 20px;">
				<div slot="header" class="clearfix">
					<span style="font-weight: bold;" v-if="serviceType === 'electronic'">邮箱信息</span>
					<span style="font-weight: bold;" v-else>收货信息</span>
				</div>
				<div style="margin-bottom: 15px;">
					<div v-if="onlyBusinessLicense"><b>客户姓名:</b> {{ userinfo }}</div>
					<div v-else-if="serviceType === 'electronic'"><b>邮箱地址:</b> {{ userEmail }}</div>
					<div v-else><b>收件人姓名:</b> {{ userinfo }}</div>
					<div v-if="onlyBusinessLicense"><b>邮箱地址:</b> {{ userEmail }}</div>
					<div v-else-if="serviceType !== 'electronic'"><b>手机号:</b> {{ userinfo2 }}</div>
					<div v-if="serviceType !== 'electronic' && !onlyBusinessLicense"><b>收件地址:</b> {{ userinfo3 }}</div>
					<div v-if="serviceType !== 'electronic' && materialOption === 'mail' && !onlyBusinessLicense"><b>快递单号:</b> {{ courierCompany }}-{{ trackingNumber }}</div>
				</div>
			</el-card>
			
			<el-card class="box-card" style="margin-bottom: 20px;">
				<div slot="header" class="clearfix">
					<span style="font-weight: bold;">打印选项</span>
				</div>
				<div style="margin-bottom: 15px;">
					<div><b>打印章数:</b> {{ num }}</div>
					<div><b>选择选项:</b> {{ selectedOptions.join(', ') || '无' }}</div>
					<div v-if="remarks"><b>其他说明:</b> {{ remarks }}</div>
				</div>
			</el-card>
			
			<el-card class="box-card">
				<div slot="header" class="clearfix">
					<span style="font-weight: bold;"><i class="el-icon-money"></i> 费用信息</span>
				</div>
				<div class="price-summary">
					<div class="price-item">
						<span v-if="onlyBusinessLicense">营业执照服务费</span>
						<span v-else>基础费用</span>
						<span>
							<span style="text-decoration: line-through; color: #909399; font-size: 12px;">¥{{ jichuprice.toFixed(2) }}</span>
							<span style="margin-left: 5px;">¥{{ (jichuprice * (userrow.addprice || 1)).toFixed(2) }}</span>
						</span>
					</div>
					<div class="price-item" v-if="!onlyBusinessLicense && numprice > 0">
						<span>打印费用 ({{ num > 10 ? (num - 10) + '张超量' : '无超量' }})</span>
						<span>¥{{ numprice.toFixed(2) }}</span>
					</div>
					<div class="price-item" v-if="!onlyBusinessLicense && businessLicenseEnabled && totalLicensePrice > 0">
						<span>营业执照费用 ({{ selectedLicenseCompanies.length }}个公司)</span>
						<span>
							<span style="text-decoration: line-through; color: #909399; font-size: 12px;">¥{{ totalLicensePrice.toFixed(2) }}</span>
							<span style="margin-left: 5px;">¥{{ (totalLicensePrice * (userrow.addprice || 1)).toFixed(2) }}</span>
						</span>
					</div>
					<div class="price-item">
						<span style="font-size: 18px; font-weight: 600;">总计</span>
						<span style="font-size: 18px; font-weight: 600; color: #f56c6c;">¥{{ calculatedTotalPrice }}</span>
					</div>
				</div>

				<!-- 价格说明 -->
				<div style="margin-top: 15px; padding: 12px; background: #f0f8ff; border-radius: 6px; border-left: 4px solid #409EFF;">
					<div style="font-size: 13px; color: #606266; line-height: 1.6;">
						<div v-if="onlyBusinessLicense">
							<i class="el-icon-info"></i> 仅营业执照服务，包含电子版营业执照生成和邮件发送
						</div>
						<div v-else>
							<i class="el-icon-info"></i> 基础费用包含盖章服务
							<span v-if="num > 10">，超过10张打印每张收费0.5元</span>
							<span v-if="businessLicenseEnabled && selectedLicenseCompanies.length > 0">，营业执照额外收费{{ (totalLicensePrice * (userrow.addprice || 1)).toFixed(2) }}元</span>
						</div>
						<div style="margin-top: 8px;">
							<i class="el-icon-star-on" style="color: #f56c6c;"></i>
							<strong>您的费率：{{ (userrow.addprice || 1) }}</strong>
							<span v-if="(userrow.addprice || 1) < 1">（享受{{ Math.round((1 - (userrow.addprice || 1)) * 100) }}%折扣）</span>
							<span v-else-if="(userrow.addprice || 1) === 1">（标准价格）</span>
						</div>
					</div>
				</div>
			</el-card>
			
			<div class="step-actions">
				<el-button @click="prevStep" size="medium">
					<i class="el-icon-arrow-left"></i> 上一步
				</el-button>
				<el-button type="success" @click="submitOrder" size="medium">
					<i class="el-icon-check"></i> 提交订单
				</el-button>
			</div>
		</div>
	</div>
</el-dialog>
<!-- 补充上传文件模态框 -->
<el-dialog 
	title="补充上传文件" 
	:visible.sync="uploadDialogVisible" 
	:width="windowWidth < 768 ? '95%' : '50%'" 
	:before-close="handleUploadDialogClose" 
	custom-class="dialog-form"
	:top="windowWidth < 768 ? '5vh' : '15vh'">
	<div class="panel-body">
		<el-form label-position="right" label-width="90px">
			<el-form-item label="文件上传">
				<el-upload
					action="/sxgz/api.php?action=upload_file"
					:on-success="handleSupplementUploadSuccess"
					:on-error="handleSupplementUploadError"
					:before-upload="beforeUpload"
					:show-file-list="true"
					drag>
					<i class="el-icon-upload"></i>
					<div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
					<div slot="tip" class="el-upload__tip">支持格式：PDF、Word、图片、压缩包 | 大小限制：10MB以内</div>
				</el-upload>
			</el-form-item>
			<el-form-item v-if="supplementFile">
				<div style="padding: 12px; background: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px;">
					<div style="display: flex; align-items: center; justify-content: space-between;">
						<div style="display: flex; align-items: center;">
							<i class="el-icon-document" style="color: #67C23A; margin-right: 8px;"></i>
							<span style="color: #67C23A; font-weight: 600;">{{ supplementFile.name }}</span>
							<span style="color: #909399; margin-left: 10px;">({{ (supplementFile.size / 1024).toFixed(2) }} KB)</span>
						</div>
						<el-link type="primary" :href="supplementFile.downloadUrl" target="_blank" icon="el-icon-download" style="margin-left: 10px;">预览</el-link>
					</div>
				</div>
			</el-form-item>
		</el-form>
	</div>
	<span slot="footer" class="dialog-footer">
		<el-button @click="uploadDialogVisible = false" size="small">取 消</el-button>
		<el-button type="primary" @click="submitSupplementFile" icon="el-icon-check" size="small">确 定</el-button>
	</span>
</el-dialog>
<!-- 导出条件选择模态框 -->
<el-dialog 
	title="导出订单数据" 
	:visible.sync="exportDialogVisible" 
	:width="windowWidth < 768 ? '95%' : '50%'" 
	:before-close="handleExportDialogClose" 
	custom-class="dialog-form"
	:top="windowWidth < 768 ? '10vh' : '15vh'">
	<div class="panel-body">
		<el-form label-position="right" label-width="90px">
			<el-form-item label="订单状态">
				<el-select v-model="exportForm.status" placeholder="请选择状态" clearable style="width: 100%;">
					<el-option label="全部状态" value=""></el-option>
					<el-option label="待处理" value="pending"></el-option>
					<el-option label="处理中" value="processing"></el-option>
					<el-option label="已完成" value="completed"></el-option>
					<el-option label="已取消" value="cancelled"></el-option>
					<el-option label="失败" value="failed"></el-option>
					<el-option label="申请退款" value="refund_requested"></el-option>
					<el-option label="已退款" value="refunded"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="导出格式">
				<el-select v-model="exportForm.format" placeholder="请选择导出格式" style="width: 100%;">
					<el-option label="CSV格式" value="csv"></el-option>
					<el-option label="Excel格式" value="excel"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="导出范围">
				<el-radio-group v-model="exportForm.exportType">
					<el-radio label="all">导出所有订单</el-radio>
					<el-radio label="filtered">导出当前筛选结果</el-radio>
					<el-radio label="selected">导出选中订单</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item v-if="exportForm.exportType === 'selected'" label="选中订单">
				<div style="color: #666; font-size: 12px;">
					已选中 {{ selectedOrders.length }} 个订单
					<el-button v-if="selectedOrders.length === 0" type="text" size="mini" @click="exportDialogVisible = false; selectAllOrders()">
						去选择订单
					</el-button>
				</div>
			</el-form-item>
		</el-form>
	</div>
	<span slot="footer" class="dialog-footer">
		<el-button @click="exportDialogVisible = false" size="small">取 消</el-button>
		<el-button type="primary" @click="exportExcel" icon="el-icon-download" size="small">确 定</el-button>
	</span>
</el-dialog>
			</div>
		</div>
	</div>

</div>



<script type="text/javascript" src="/sxgz/element/jquery.min.js"></script>
<script type="text/javascript" src="/sxgz/element/bootstrap.min.js"></script>
<script type="text/javascript" src="/sxgz/element/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/sxgz/element/main.min.js"></script>
<script src="/sxgz/element/aes.js" type="text/javascript"></script>
<script src="/sxgz/element/vue.min.js" type="text/javascript"></script>
<script src="/sxgz/element/vue-resource.min.js" type="text/javascript"></script>
<script src="/sxgz/element/element.js" type="text/javascript"></script>



<script type="text/javascript">
	var vm = new Vue({
		el: "#add",
		data: {
			row: {
				data: [],
				current_page: 1,
				total: 0
			},
			userrow: userrow,
			cx: {
				status_text: '',
				status: '', // 新增状态筛选字段
				dock: '',
				mh: '',
				search: '',
				oid: '',
				uid: '',
				school: '',
				kcname: '',
				cid: '',
				limit: '15'
			},
			// 新增订单相关数据
			dialogVisible: false,
			cid: '',
			userinfo: '',
			userinfo2: '',
			userinfo3: '',
			trackingNumber: '', // 快递单号
			courierCompany: 'SF', // 快递公司，默认顺丰
			num: 0,
			class1: [],
			uploadedFile: null,
			selectedOptions: [],
			totalPrice: 0,
			businessLicenseEnabled: false,
			orderOptions: [
				{name: "需要工作室寄给客户"},
				{name: "无需工作室寄给客户只要电子版"},
				{name: "需要工作室寄给客户，同时也需要电子版"},
				{name: "一式三份"},
				{name: "一式两份"},
				{name: "一式一份"},
				{name: "单面打印"},
				{name: "双面打印"},
				{name: "彩印"},
				{name: "黑白"},
				{name: "骑缝章"},
				{name: "补打印费只需选择此项"},
				{name: "只盖章"}
			],
			form: {},
			remarks: '', // 用户备注信息
			numprice: 0, // 打印费用
			jichuprice: 0, // 基础费用
			yingyeprice: 0, // 营业执照费用（保留兼容性）
			onlyBusinessLicense: false, // 仅需要营业执照
			selectedLicenseCompanies: [], // 选中的营业执照公司ID列表
			licenseCompanies: [], // 营业执照公司列表
			totalLicensePrice: 0, // 营业执照总费用

			multipleSelection: [], //多选
			currentOid: null,
			uploadDialogVisible: false,
			supplementFile: null,
			// 导出相关
			exportDialogVisible: false,
			exportForm: {
				status: '',
				format: 'csv',
				exportType: 'filtered'
			},
			selectedOrders: [], // 选中的订单
			activeStep: 0,
			serviceType: '',
			materialOption: null,
			businessLicenseOption: false,
			companyLoading: false, // 添加公司选择的加载状态
			userEmail: '', // 新增邮箱输入字段
			originalCompany: null, // 存储原始选择的公司信息
			windowWidth: window.innerWidth, // 新增属性，保存窗口宽度
			tableLoading: false // 表格加载状态
		},
 		watch: {
			sex(newVal) {
				// 性别变化监听
			},
			searchKeyword(newVal) {
				if (newVal.length > 0) {
					this.class1_temp = this.class1.filter(e => {
						return e.name.indexOf(newVal) >= 0;
					})
				} else {
					this.class1_temp = JSON.parse(JSON.stringify(this.class1));
				}
				this.cid = this.class1_temp[0];
			},
			// 监听cid
			cid(newVal) {
				// 检查newVal是否为null或undefined
				if (!newVal) {
					return;
				}

				this.updateLicensePrice();

				// 如果仅需要营业执照，使用营业执照专用公司价格
				if (!this.onlyBusinessLicense) {
					// 确保价格正确解析为数字
					this.jichuprice = parseFloat(newVal.price) || 0;
				} else {
					// 对于营业执照模式，不在这里设置价格为0
					// 保留之前设置的价格，避免被重置
					if (!this.jichuprice || this.jichuprice <= 0) {
						// 如果当前营业执照公司价格为0，查找正确的价格
						if (this.class1 && this.class1.length > 0) {
							const licenseCompany = this.class1.find(company => company.cid == 25709);
							if (licenseCompany) {
								let price = parseFloat(licenseCompany.price);
								this.jichuprice = price > 0 ? price : 100;
							} else {
								this.jichuprice = 100; // 默认价格
							}
						}
					}
				}
			},
			num(newVal) {
				// 判断如果大于10就*0.5
				if (newVal >= 10) {
					this.numprice = (Number(newVal) - 10) * 0.5;
				} else {
					this.numprice = 0;
				}
				// this.totalPrice = this.jichuprice + this.numprice + this.yingyeprice;
			},
		    businessLicenseEnabled(newVal){
				if (!newVal) {
					// 如果取消勾选营业执照，清空选择
					this.selectedLicenseCompanies = [];
					this.totalLicensePrice = 0;
				}
			},
			// 监听仅需要营业执照选项
			onlyBusinessLicense(newVal) {
				if (newVal) {
					// 取消勾选需要营业执照，避免重复计费
					this.businessLicenseEnabled = false;

					// 保存用户选择的原始公司信息到备注
					if (this.cid && this.cid.name) {
						// 如果已有备注内容，添加到前面
						if (this.remarks) {
							this.remarks = `原选择公司：${this.cid.name} | ` + this.remarks;
						} else {
							this.remarks = `原选择公司：${this.cid.name}`;
						}

						// 保存原始公司对象，以便后续需要恢复
						this.originalCompany = this.cid;
					}

					// 清空普通公司选择
					this.cid = null;

					// 清空营业执照公司选择，让用户重新选择
					this.selectedLicenseCompanies = [];
					this.totalLicensePrice = 0;

					// 设置基础费用为0，只收取营业执照费用
					this.jichuprice = 0;
				} else {
					// 如果取消勾选仅需要营业执照
					this.selectedLicenseCompanies = [];
					this.totalLicensePrice = 0;

					// 恢复原始公司选择
					if (this.originalCompany) {
						this.cid = this.originalCompany;
						this.jichuprice = parseFloat(this.originalCompany.price) || 0;
						this.originalCompany = null;
					}

					// 清理备注中的公司信息
					if (this.remarks && this.remarks.includes('原选择公司：')) {
						this.remarks = this.remarks.replace(/原选择公司：[^|]*\s*\|\s*/, '');
					}
				}
			}
		},
		methods: {
			// 计算当前页面的唯一用户数（管理员专用）
			getUniqueUsersCount() {
				if (!this.row.data || this.row.data.length === 0) {
					return 0;
				}
				const uniqueUids = new Set(this.row.data.map(order => order.uid));
				return uniqueUids.size;
			},

			// 重置搜索
			resetSearch() {
				this.cx.mh = '';
				this.cx.search = '';
				this.cx.status = '';
				this.get(1);
			},



			// 处理下拉菜单命令
			handleCommand(command, row) {
				switch (command) {
					case 'upload':
						this.uploadFiles(row.order_id);
						break;
					case 'download':
						this.downloadProcessedFiles(row.order_id);
						break;
					case 'refund':
						this.refundOrder(row.order_id);
						break;
				}
			},

			// 下载处理后的文件
			async downloadProcessedFiles(orderId) {
				try {
					const loading = this.$loading({
						lock: true,
						text: '获取文件列表...',
						spinner: 'el-icon-loading',
						background: 'rgba(0, 0, 0, 0.7)'
					});

					const response = await this.$http.get(`/sxgz/api.php?action=get_order&order_id=${orderId}`);
					loading.close();

					if (response.data.success) {
						const processedFiles = response.data.data.files.processed || [];

						if (processedFiles.length === 0) {
							this.$message.warning('暂无处理后的文件可下载');
							return;
						}

						if (processedFiles.length === 1) {
							// 只有一个文件，直接下载
							window.open(processedFiles[0].download_url, '_blank');
							this.$message.success('文件下载已开始');
						} else {
							// 多个文件，显示选择列表
							const fileList = processedFiles.map(file =>
								`<div style="margin: 5px 0;">
									<a href="${file.download_url}" target="_blank" style="color: #409EFF; text-decoration: none;">
										<i class="el-icon-download"></i> ${file.original_name || file.name}
									</a>
									<span style="color: #999; font-size: 12px; margin-left: 10px;">(${this.formatFileSize(file.size)})</span>
								</div>`
							).join('');

							this.$alert(fileList, '选择要下载的文件', {
								dangerouslyUseHTMLString: true,
								confirmButtonText: '关闭'
							});
						}
					} else {
						this.$message.error('获取文件列表失败');
					}
				} catch (error) {
					this.$message.error('获取文件列表失败，请检查网络连接');
				}
			},

			// 格式化文件大小
			formatFileSize(size) {
				if (!size) return '未知';

				const sizeNum = parseInt(size);
				if (sizeNum < 1024) return sizeNum + 'B';
				if (sizeNum < 1024 * 1024) return (sizeNum / 1024).toFixed(1) + 'KB';
				return (sizeNum / (1024 * 1024)).toFixed(1) + 'MB';
			},

			// 查看订单详情
			viewOrder(row) {
				let details = `
订单号: ${row.order_no}
客户: ${row.customer_name}
电话: ${row.customer_phone}
邮箱: ${row.customer_email || '未填写'}
地址: ${row.customer_address || '未填写'}
公司: ${row.company_name}
服务类型: ${this.getServiceTypeText(row.service_type)}
材料类型: ${this.getMaterialTypeText(row.material_type)}
打印份数: ${row.print_copies}份
基础价格: ¥${row.base_price}
打印费用: ¥${row.print_price}
执照费用: ¥${row.license_price}
总价: ¥${row.total_price}
状态: ${this.getOrderStatusText(row.status)}
创建时间: ${this.formatDate(row.created_at)}`;

				// 添加退款相关信息
				if (row.status === 'refund_requested' || row.status === 'refunded') {
					details += `\n\n=== 退款信息 ===`;
					if (row.refund_reason) {
						details += `\n退款原因: ${row.refund_reason}`;
					}
					if (row.admin_notes && row.status === 'refunded') {
						details += `\n管理员回复: ${row.admin_notes}`;
					} else if (row.admin_notes && row.status === 'pending') {
						details += `\n拒绝原因: ${row.admin_notes}`;
					}
					if (row.status === 'refunded') {
						details += `\n退款状态: ✅ 已退款到余额`;
					} else {
						details += `\n退款状态: ⏳ 等待管理员审核`;
					}
				}

				// 添加管理员备注（非退款情况）
				if (row.admin_notes && row.status !== 'refund_requested' && row.status !== 'refunded') {
					details += `\n\n管理员备注: ${row.admin_notes}`;
				}

				details = details.trim();

				this.$alert(details, '订单详情', {
					confirmButtonText: '确定',
					customClass: 'order-detail-dialog'
				});
			},

			// 解析营业执照公司信息
			getLicenseCompaniesFromOrder(order) {
				if (order.business_license != '1' || !order.special_requirements) {
					return [];
				}

				// 从special_requirements中提取营业执照公司信息
				const requirements = order.special_requirements;
				const licenseMatch = requirements.match(/营业执照公司:\s*([^|]+)/);

				if (!licenseMatch) {
					return [];
				}

				const licenseText = licenseMatch[1].trim();
				const companies = [];

				// 解析格式：公司A(¥100), 公司B(¥200)
				const companyMatches = licenseText.match(/([^(,]+)\(¥([^)]+)\)/g);

				if (companyMatches) {
					companyMatches.forEach(match => {
						const parts = match.match(/([^(]+)\(¥([^)]+)\)/);
						if (parts) {
							companies.push({
								name: parts[1].trim(),
								price: parts[2].trim()
							});
						}
					});
				}

				return companies;
			},

			// 清理特殊要求文本（移除营业执照公司信息）
			getCleanSpecialRequirements(requirements) {
				if (!requirements) return '';

				// 移除营业执照公司信息部分
				let cleaned = requirements.replace(/\s*\|\s*营业执照公司:[^|]*$/g, '');
				cleaned = cleaned.replace(/^营业执照公司:[^|]*\s*\|\s*/g, '');

				// 限制长度
				if (cleaned.length > 15) {
					cleaned = cleaned.substring(0, 15) + '...';
				}

				return cleaned.trim();
			},

			// 通用API请求方法
			async apiRequest(action, data, loadingText = '处理中...') {
				const loading = this.$loading({
					lock: true,
					text: loadingText,
					spinner: 'el-icon-loading',
					background: 'rgba(0, 0, 0, 0.7)'
				});

				try {
					const response = await this.$http.post(`/sxgz/api.php?action=${action}`, data);
					loading.close();
					return response.data;
				} catch (error) {
					loading.close();
					console.error(`${action} 失败:`, error);
					throw error;
				}
			},



			// 上传文件
			async uploadFiles(orderId) {
				try {
					// 先获取现有文件信息
					const orderResponse = await this.$http.get(`/sxgz/api.php?action=get_order&order_id=${orderId}`);
					let existingFiles = [];
					let uploadCount = 0;

					if (orderResponse.data.success) {
						existingFiles = orderResponse.data.data.files.uploads || [];
						uploadCount = existingFiles.length;

						// 检查文件数量限制
						if (uploadCount >= 5) {
							this.$message.warning('每个订单最多上传5个文件，建议使用压缩包格式');
							return;
						}

						// 文件数量建议
						if (uploadCount >= 3) {
							const confirmResult = await this.$confirm(
								`您已上传${uploadCount}个文件，建议将多个文件打包成压缩包上传，便于管理和传输。是否继续单独上传？`,
								'文件数量提醒',
								{
									confirmButtonText: '继续上传',
									cancelButtonText: '取消',
									type: 'warning'
								}
							);
							if (!confirmResult) return;
						}
					}

					// 创建文件输入元素
					const input = document.createElement('input');
					input.type = 'file';
					input.accept = '.pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.zip,.rar,.7z';

					input.onchange = async (event) => {
						const file = event.target.files[0];
						if (!file) return;

						// 验证文件大小（10MB）
						if (file.size > 10 * 1024 * 1024) {
							this.$message.error('文件大小不能超过10MB');
							return;
						}

						// 检查是否存在同名文件
						let isReplacement = false;
						const existingFile = existingFiles.find(f => f.original_name === file.name || f.name === file.name);

						if (existingFile) {
							const replaceConfirm = await this.$confirm(
								`文件"${file.name}"已存在，上传将替换原有文件。是否继续？`,
								'文件替换确认',
								{
									confirmButtonText: '替换文件',
									cancelButtonText: '取消',
									type: 'warning'
								}
							);
							if (!replaceConfirm) return;
							isReplacement = true;
						}

						const loading = this.$loading({
							lock: true,
							text: isReplacement ? '正在替换文件...' : '正在上传文件...',
							spinner: 'el-icon-loading',
							background: 'rgba(0, 0, 0, 0.7)'
						});

						// 创建FormData
						const formData = new FormData();
						formData.append('file', file);
						formData.append('order_id', orderId);
						if (isReplacement) {
							formData.append('is_replacement', 'true');
						}

						// 上传文件
						this.$http.post('/sxgz/api.php?action=upload_file', formData, {
							headers: {
								'Content-Type': 'multipart/form-data'
							}
						}).then(response => {
							loading.close();
							if (response.data.code === 1) {
								this.$message.success(response.data.msg || '文件上传成功');
								if (response.data.replaced_file) {
									this.$message.info(`已替换文件：${response.data.replaced_file}`);
								}
								this.get(this.row.current_page); // 刷新列表
							} else {
								if (response.data.suggest_compression) {
									this.$message.warning(response.data.msg + '，建议使用压缩包格式');
								} else {
									this.$message.error(response.data.msg || '上传失败');
								}
							}
						}).catch(error => {
							loading.close();
							console.error('文件上传失败:', error);
							this.$message.error('文件上传失败，请检查网络连接');
						});
					};

					// 触发文件选择
					input.click();
				} catch (error) {
					console.error('获取订单信息失败:', error);
					this.$message.warning('无法获取订单信息，将使用简化上传模式');

					// 如果获取订单信息失败，仍然允许上传
					const input = document.createElement('input');
					input.type = 'file';
					input.accept = '.pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.zip,.rar,.7z';
					input.onchange = (event) => {
						const file = event.target.files[0];
						if (!file) return;

						const formData = new FormData();
						formData.append('file', file);
						formData.append('order_id', orderId);

						const loading = this.$loading({
							lock: true,
							text: '正在上传文件...',
							spinner: 'el-icon-loading',
							background: 'rgba(0, 0, 0, 0.7)'
						});

						this.$http.post('/sxgz/api.php?action=upload_file', formData, {
							headers: {
								'Content-Type': 'multipart/form-data'
							}
						}).then(response => {
							loading.close();
							if (response.data.code === 1) {
								this.$message.success('文件上传成功');
								this.get(this.row.current_page);
							} else {
								this.$message.error(response.data.msg || '上传失败');
							}
						}).catch(error => {
							loading.close();
							this.$message.error('文件上传失败，请检查网络连接');
						});
					};
					input.click();
				}
			},

			// 申请退款
			refundOrder(orderId) {
				this.$prompt('请输入退款原因', '申请退款', {
					confirmButtonText: '提交申请',
					cancelButtonText: '取消',
					inputType: 'textarea',
					inputPlaceholder: '请详细说明退款原因...',
					inputValidator: (value) => {
						if (!value || value.trim().length < 5) {
							return '退款原因至少需要5个字符';
						}
						return true;
					}
				}).then(({ value }) => {
					const loading = this.$loading({
						lock: true,
						text: '提交退款申请中...',
						spinner: 'el-icon-loading'
					});

					this.$http.post('/sxgz/api.php?action=apply_refund', {
						order_id: orderId,
						reason: value.trim()
					}).then(response => {
						loading.close();
						if (response.data.success) {
							this.$message.success(response.data.message);
							this.get(this.row.current_page); // 刷新列表
						} else {
							this.$message.error(response.data.message || '申请失败');
						}
					}).catch(error => {
						loading.close();
						console.error('申请退款失败:', error);
						this.$message.error('申请失败，请检查网络连接');
					});
				}).catch(() => {
					this.$message.info('已取消退款申请');
				});
			},



			// 获取服务类型颜色
			getServiceTypeColor(type) {
				const colorMap = {
					'mail': 'primary',
					'electronic': 'success',
					'both': 'warning'
				};
				return colorMap[type] || 'info';
			},



			// 获取订单状态类型
			getOrderStatusType(status) {
				const statusMap = {
					'pending': '',           // 默认灰色，优先级较低
					'processing': 'danger',  // 红色，最高优先级，需要立即处理
					'completed': 'success',  // 绿色，已完成
					'cancelled': 'info',     // 蓝色，已取消
					'failed': 'danger',      // 红色，失败
					'refund_requested': 'warning', // 橙色，第二优先级
					'refunded': 'info'       // 蓝色，已退款
				};
				return statusMap[status] || 'info';
			},

			// 更新营业执照价格（新版本支持多选）
			updateLicensePrice() {
				if (!this.selectedLicenseCompanies || this.selectedLicenseCompanies.length === 0) {
					this.totalLicensePrice = 0;
					this.yingyeprice = 0; // 保持兼容性
					return;
				}

				// 计算选中营业执照公司的总价格
				let total = 0;
				this.selectedLicenseCompanies.forEach(cid => {
					const company = this.licenseCompanies.find(c => c.cid == cid);
					if (company) {
						total += parseFloat(company.price) || 0;
					}
				});

				this.totalLicensePrice = total;
				this.yingyeprice = total; // 保持兼容性

				// 如果是仅需要营业执照模式，将营业执照费用设为基础费用
				if (this.onlyBusinessLicense) {
					this.jichuprice = total;
					// 注意：这里不清零totalLicensePrice，因为确认页面需要显示
				}
			},

			// 获取营业执照公司名称
			getLicenseCompanyName(cid) {
				const company = this.licenseCompanies.find(c => c.cid == cid);
				return company ? company.name : `公司ID:${cid}`;
			},

			// 获取营业执照公司价格
			getLicenseCompanyPrice(cid) {
				const company = this.licenseCompanies.find(c => c.cid == cid);
				return company ? parseFloat(company.price).toFixed(2) : '0.00';
			},



			// 统一日期格式化方法
			formatDate(dateString, type = 'full') {
				if (!dateString) return '';
				const date = new Date(dateString);

				if (type === 'short') {
					const now = new Date();
					const diffTime = now - date;
					const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

					if (diffDays === 0) {
						return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
					} else if (diffDays < 7) {
						return `${diffDays}天前`;
					} else {
						return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
					}
				}

				return date.toLocaleDateString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit'
				});
			},

			

			// 解码打印选项
			decodePrintOptions(printOptionsStr) {
				if (!printOptionsStr) return [];

				try {
					const options = JSON.parse(printOptionsStr);
					if (Array.isArray(options)) {
						return options.map(option => {
							if (typeof option === 'string') {
								// 处理Unicode编码
								let decoded = option.replace(/\\u([\d\w]{4})/gi, (match, grp) =>
									String.fromCharCode(parseInt(grp, 16))
								);

								// 处理没有反斜杠的Unicode
								if (decoded === option && option.includes('u')) {
									decoded = option.replace(/u([\d\w]{4})/gi, (match, grp) =>
										String.fromCharCode(parseInt(grp, 16))
									);
								}

								return decoded;
							}
							return option;
						});
					}
					return [];
				} catch (e) {
					// 如果JSON解析失败，尝试直接处理字符串
					if (typeof printOptionsStr === 'string') {
						try {
							const decoded = printOptionsStr.replace(/\\u([\d\w]{4})/gi, (match, grp) =>
								String.fromCharCode(parseInt(grp, 16))
							);
							return [decoded];
						} catch (e2) {
							return [printOptionsStr]; // 返回原始字符串
						}
					}
					return [];
				}
			},



			// 统一文本映射方法
			getTextByType(value, type) {
				const maps = {
					courier: {
						'SF': '顺丰', 'YTO': '圆通', 'ZTO': '中通', 'STO': '申通',
						'EMS': 'EMS', 'JD': '京东', 'YD': '韵达'
					},
					material: {
						'upload': '上传文件',
						'mail': '邮寄材料'
					},
					service: {
						'mail': '邮寄服务',
						'electronic': '电子版',
						'both': '邮寄+电子版'
					},
					status: {
						'pending': '待处理',
						'processing': '处理中',
						'completed': '已完成',
						'cancelled': '已取消',
						'failed': '失败',
						'refund_requested': '申请退款',
						'refunded': '已退款'
					}
				};
				return maps[type]?.[value] || value || '未知';
			},

			// 兼容性方法
			getCourierName(code) { return this.getTextByType(code, 'courier'); },
			getMaterialTypeText(type) { return this.getTextByType(type, 'material'); },
			getServiceTypeText(type) { return this.getTextByType(type, 'service'); },
			getOrderStatusText(status) { return this.getTextByType(status, 'status'); },
			formatDateShort(dateString) { return this.formatDate(dateString, 'short'); },

			// 获取服务类型标签颜色
			getServiceTypeTag(type) {
				const tags = {
					'electronic': 'primary',
					'mail': 'success',
					'both': 'warning'
				};
				return tags[type] || 'info';
			},

			// 获取文件类型图标
			getFileIcon(filename) {
				if (!filename) return 'el-icon-document';

				const ext = filename.toLowerCase().split('.').pop();
				const iconMap = {
					'pdf': 'el-icon-document',
					'doc': 'el-icon-edit-outline',
					'docx': 'el-icon-edit-outline',
					'xls': 'el-icon-s-grid',
					'xlsx': 'el-icon-s-grid',
					'zip': 'el-icon-folder-add',
					'rar': 'el-icon-folder-add',
					'7z': 'el-icon-folder-add',
					'jpg': 'el-icon-picture-outline',
					'jpeg': 'el-icon-picture-outline',
					'png': 'el-icon-picture-outline',
					'gif': 'el-icon-picture-outline'
				};

				return iconMap[ext] || 'el-icon-document';
			},

			// 获取文件大小显示
			getFileSize(size) {
				if (!size) return '';

				const sizeNum = parseInt(size);
				if (sizeNum < 1024) return sizeNum + 'B';
				if (sizeNum < 1024 * 1024) return (sizeNum / 1024).toFixed(1) + 'KB';
				return (sizeNum / (1024 * 1024)).toFixed(1) + 'MB';
			},

			// 处理单个订单数据
			processOrderData(order) {
				return {
					order_id: order.order_id,
					order_no: order.order_no,
					status: order.status,
					created_at: order.created_at,
					uid: order.uid, // 用户ID
					username: order.username, // 用户名（管理员可见）
					user_realname: order.user_realname, // 用户真实姓名（管理员可见）
					customer_name: order.customer_name,
					customer_phone: order.customer_phone,
					customer_email: order.customer_email,
					customer_address: order.customer_address,
					company_name: order.company_name,
					total_price: parseFloat(order.total_price) || 0,
					base_price: parseFloat(order.base_price) || 0,
					print_price: parseFloat(order.print_price) || 0,
					license_price: parseFloat(order.license_price) || 0,
					service_type: order.service_type,
					material_type: order.material_type,
					business_license: order.business_license,
					only_business_license: order.only_business_license,
					print_copies: parseInt(order.print_copies) || 1,
					print_options: this.decodePrintOptions(order.print_options),
					courier_company: order.courier_company,
					tracking_number: order.tracking_number,
					original_filename: order.original_filename,
					file_size: order.file_size,
					remarks: order.admin_notes || '',
					customer_notes: order.special_requirements || '',
					special_requirements: order.special_requirements || '', // 添加特殊要求字段
					uploaded_file: order.uploaded_file,
					order_email_sent: order.order_email_sent === '1',
					completion_email_sent: order.completion_email_sent === '1'
				};
			},



			getStatusType(status) {
				const statusMap = {
					'0': 'info',
					'1': 'success',
					'2': 'danger',
					'3': 'warning',
					'4': '',
					'99': 'warning'
				}
				return statusMap[status] || 'info'
			},
			
			getStatusText(status) {
				const textMap = {
					'0': '待处理',
					'1': '处理成功',
					'2': '处理失败',
					'3': '重复下单',
					'4': '已取消',
					'99': '自营'
				}
				return textMap[status] || '未知状态'
			},
			
			duijie(oid) {
				if (this.getStatusType(oid) === 'info' || this.getStatusType(oid) === 'danger') {
				// 执行对接逻辑
				}
			},
			get: function(page) {
				this.tableLoading = true;
				var apiUrl = "/sxgz/api.php?action=get_orders";

				var params = {
					page: page || 1,
					limit: this.cx.limit || 15
				};

				// 添加搜索条件
				if (this.cx.mh && this.cx.mh.trim()) {
					params.search = this.cx.mh.trim();
					// 当选择"全部"时，cx.search为空字符串，不发送search_field参数，后端会进行全字段搜索
					if (this.cx.search && this.cx.search.trim()) {
						params.search_field = this.cx.search.trim();
					}
					// 如果cx.search为空（选择"全部"），则不添加search_field参数，让后端进行全字段搜索
				}
				if (this.cx.status && this.cx.status.trim()) {
					params.status = this.cx.status.trim();
				}

				this.$http.get(apiUrl, { params: params }).then((response) => {
					this.tableLoading = false;
					if (response.data.success) {
						// 处理订单数据
						const orders = response.data.data.orders || [];
						this.row = {
							data: orders.map(order => this.processOrderData(order)),
							current_page: parseInt(response.data.data.page) || 1,
							total: parseInt(response.data.data.total) || 0,
							last_page: parseInt(response.data.data.pages) || 1
						};
					} else {
						this.$message.error(response.data.message || '获取订单列表失败');
						// 设置空数据
						this.row = {
							data: [],
							current_page: 1,
							total: 0,
							last_page: 1
						};
					}
				}).catch((error) => {
					this.tableLoading = false;
					console.error('获取订单列表失败:', error);
					this.$message.error('获取订单列表失败，请检查网络连接');
					// 设置空数据
					this.row = {
						data: [],
						current_page: 1,
						total: 0,
						last_page: 1
					};
				});
			},

			// 映射订单状态
			mapOrderStatus(status) {
				const statusMap = {
					'pending': '0',
					'processing': '99',
					'completed': '1',
					'cancelled': '4',
					'failed': '2'
				};
				return statusMap[status] || '0';
			},
			handleCurrentChange(val) {
				this.get(val);
			},
			bs: function(oid) {
				layer.confirm('建议漏看或者进度被重置的情况下使用。<br>频繁点击补刷会出现不可预测的结果<br>请问是否补刷所选的任务？', {
					title: '温馨提示',
					icon: 3,
					btn: ['确定补刷', '取消']
				}, function() {
					var load = layer.load(2);
					$.get("/apisub.php?act=bs&oid=" + oid, function(data) {
						layer.close(load);
						if (data.code == 1) {
							vm.get(vm.row.current_page);
							layer.alert(data.msg, {
								icon: 1
							});
						} else {
							layer.msg(data.msg, {
								icon: 2
							});
						}
					});
				});
			},
			up: function(oid) {
				var load = layer.load(2);
				layer.msg("正在努力获取中....", {
					icon: 3
				});
				$.get("/apisub.php?act=uporder&oid=" + oid, function(data) {
					layer.close(load);
					if (data.code == 1) {
						vm.get(vm.row.current_page);
						layer.msg(data.msg, {
							icon: 1
						});
					} else {
						layer.msg(data.msg, {
							icon: 2
						});
					}
				});
			},
			feedback: function(oid) {
				layer.prompt({
					title: '请用简短的一句话描述问题，只需要输入问题！',
					formType: 2,
					placeholder: '请输入问题描述...'
				}, function(feedbackText, index) {
					layer.close(index);
					feedbackText = feedbackText.trim();

					if (feedbackText === '') {
						layer.msg('反馈内容不能为空', {
							icon: 2
						});
						return;
					}
					if (/\d|[a-zA-Z]/.test(feedbackText)) {
						layer.msg('反馈内容不能包含数字和字母', {
							icon: 2
						});
						return;
					}

					var load = layer.load();
					$.get("/gd.php?act=feedback&oid=" + oid, {
						feedback: feedbackText
					}, function(data) {
						layer.close(load);
						if (data.code === 1) {
							layer.msg('反馈成功，请在我的反馈中查看', {
								icon: 1
							});
						} else {
							layer.msg('反馈失败: ' + data.msg, {
								icon: 2
							});
						}
					});
				});
			},
			tips: function(message) {
				// 如果message是对象，则使用message.cid
				let cid = message && message.cid ? message.cid : message;

				for (var i = 0; this.class1.length > i; i++) {
					if (this.class1[i].cid == cid) {
						this.content = this.class1[i].content;
						if (this.class1[i].miaoshua == 1) {
							this.activems = true;
						} else {
							this.activems = false;
						}
						return false;
					}
				}
			},
			tips2: function() {
				layer.tips('开启秒刷将额外收0.05的费用', '#miaoshua');

			},
			// 新增的方法
			showAddDialog() {
				this.dialogVisible = true;
				if(this.class1.length === 0) {
					this.getclass();
				}
				// 重置订单步骤
				this.resetStepForm();
			},
			
			handleClose(done) {
				this.$confirm('确认关闭？')
					.then(_ => {
						done();
						this.resetForm();
						this.resetStepForm();
					})
					.catch(_ => {});
			},
			tuikuan(data) {
				let data2 = data;
				if (data2 == 'wcnm'){
					data2 = this.multipleSelection.map(item => item.oid);
				}else{
					data2 = [data]
				}
				this.$confirm('确认退款？')
					.then(_ => {
						this.$http.post('/apisub.php?act=tuikuan', {
							data: data2
						}).then(res => {
						    if (res.data.code == 1) {
								layer.msg(res.data.msg, {
									icon: 1
								});
								
							} else {
								layer.msg(res.data.msg, {
									icon: 2
								});
								
							}
							this.get(1); // 刷新列表
						}
						)
						}, function(res) {
							
						})
			},
			resetForm() {
				this.cid = null;
				this.userinfo = '';
				this.userinfo2 = '';
				this.userinfo3 = '';
				this.num = 0;
				this.uploadedFile = null;
				this.selectedOptions = [];
				this.businessLicenseEnabled = false;
				this.onlyBusinessLicense = false; // 重置仅需要营业执照选项
				this.originalCompany = null; // 重置原始公司信息
				this.numprice = 0;
				this.jichuprice = 0;
				this.yingyeprice = 0;
				this.remarks = '';
				this.trackingNumber = ''; // 重置快递单号
				this.courierCompany = 'SF'; // 默认选择顺丰
				this.userEmail = ''; // 重置邮箱输入字段
			},
			
			resetStepForm() {
				this.activeStep = 0;
				this.serviceType = null;
				this.materialOption = 'upload'; // 默认选择上传文件
				this.supplementFile = null;
				this.businessLicenseOption = true; // 默认显示营业执照选项
				this.onlyBusinessLicense = false; // 重置仅需要营业执照选项
				this.originalCompany = null; // 重置原始公司信息
				this.companyLoading = false; // 重置公司选择加载状态
				this.trackingNumber = ''; // 重置快递单号
				this.courierCompany = 'SF'; // 默认选择顺丰
			},

			// 步骤导航方法
			nextStep() {
				if (this.activeStep < 5) {
					this.activeStep++;
				}
			},

			prevStep() {
				if (this.activeStep > 0) {
					this.activeStep--;
				}
			},

			// 选择服务类型
			selectServiceType(type) {
				this.serviceType = type;
				// 根据服务类型自动设置材料选项
				if (type === 'electronic') {
					this.materialOption = 'upload';
				}
				// 不自动跳转到下一步，让用户手动点击下一步
			},

			// 处理公司选择变化
			handleCompanyChange(company) {
				this.cid = company;
				// 可以在这里添加额外的逻辑
			},





			// 公司选择后的下一步
			goToNextStepAfterCompany() {
				if (!this.cid) {
					this.$message.warning('请选择公司');
					return;
				}
				this.nextStep();
			},

			// 清除上传的文件
			clearUploadedFile() {
				this.supplementFile = null;
			},



			// 获取打印选项
			getPrintOptions() {
				return this.orderOptions.filter(option =>
					!option.name.includes('工作室寄给客户') &&
					!option.name.includes('电子版')
				);
			},

			// 切换打印选项
			togglePrintOption(optionName) {
				const index = this.selectedOptions.indexOf(optionName);
				if (index > -1) {
					this.selectedOptions.splice(index, 1);
				} else {
					this.selectedOptions.push(optionName);
				}
			},
			
			handleSupplementUploadSuccess(response, file) {
				if(response.code === 1) {
					this.$message.success('文件上传成功');
					this.supplementFile = {
						name: file.name,
						size: file.size,
						file: file.raw, // 保存原始文件对象
						downloadUrl: response.downurl
					};
				} else {
					this.$message.error(response.msg || '上传失败');
				}
			},
			
			handleSupplementUploadError(err, file, fileList) {
				console.error('文件上传错误:', err);
				console.error('错误文件:', file);
				console.error('文件列表:', fileList);

				let errorMsg = '文件上传失败';
				if (err && err.response) {
					try {
						const errorData = JSON.parse(err.response);
						errorMsg = errorData.msg || errorData.message || '文件上传失败';
					} catch (e) {
						errorMsg = '服务器错误: ' + (err.response || '未知错误');
					}
				} else if (err && err.message) {
					errorMsg = err.message;
				}

				this.$message.error(errorMsg);
			},
			
			beforeUpload(file) {
				// 支持的文件类型
				const validTypes = ['doc', 'docx', 'pdf', 'jpg', 'jpeg', 'png', 'zip', 'rar', '7z'];
				const fileExt = file.name.toLowerCase().split('.').pop();

				if (!validTypes.includes(fileExt)) {
					this.$message.error('支持格式：PDF、Word、图片、压缩包');
					return false;
				}

				const isLt10M = file.size / 1024 / 1024 < 10;
				if (!isLt10M) {
					this.$message.error('文件大小不能超过 10MB');
					return false;
				}

				return true;
			},
			

			submitOrder() {
				// 自动去除收件人信息中的空格，避免用户误输入空格
				this.userinfo = (this.userinfo || '').trim();
				this.userinfo2 = (this.userinfo2 || '').trim();
				this.userinfo3 = (this.userinfo3 || '').trim();
				this.userEmail = (this.userEmail || '').trim();
				this.trackingNumber = (this.trackingNumber || '').trim();
				this.remarks = (this.remarks || '').trim();

				// 仅需要营业执照时的简化验证
				if (this.onlyBusinessLicense) {
					// 验证邮箱
					if (!this.userEmail) {
						this.$message.error('请填写邮箱信息');
						return;
					}

					// 验证邮箱格式
					const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
					if (!emailRegex.test(this.userEmail)) {
						this.$message.error('请输入有效的邮箱地址');
						return;
					}

					// 确保jichuprice不为零
					if (!this.jichuprice || isNaN(this.jichuprice) || this.jichuprice <= 0) {
						// 如果价格为零或无效，使用默认价格
						this.jichuprice = 100;
					}

					// 构建仅需要营业执照的订单数据
					const onlyLicenseOrderData = {
						service_type: 'electronic',
						company_id: 25709, // 固定为营业执照专用公司ID
						customer_name: this.userinfo,
						customer_email: this.userEmail,
						only_business_license: true,
						special_requirements: this.remarks || '仅需要营业执照',
						print_copies: 0
					};

					// 提交仅营业执照订单
					const loading = this.$loading({
						lock: true,
						text: '提交中...'
					});

					this.$http.post("/sxgz/api.php?action=create_order", onlyLicenseOrderData, {
						emulateJSON: true
					}).then(response => {
						loading.close();
						if (response.data.success) {
							this.$message.success('订单创建成功');
							this.dialogVisible = false;
							this.resetForm();
							this.resetStepForm();
							this.get(1); // 刷新列表
						} else {
							this.$message.error(response.data.message || '订单创建失败');
						}
					}).catch(() => {
						loading.close();
						this.$message.error('提交失败，请重试');
					});

					return;
				}
				
				// 常规服务类型验证
				if (this.serviceType === 'electronic') {
					// 电子版服务只验证邮箱
					if (!this.userEmail) {
						this.$message.error('请填写邮箱信息');
						return;
					}

					// 验证邮箱格式
					const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
					if (!emailRegex.test(this.userEmail)) {
						this.$message.error('请输入有效的邮箱地址');
						return;
					}
				} else {
					// 其他服务类型验证完整的收货信息
					if (!this.userinfo || !this.userinfo2 || !this.userinfo3) {
						this.$message.error('请填写完整的收件人信息');
						return;
					}

					// 验证手机号格式
					const phoneRegex = /^1[3-9]\d{9}$/;
					if (!phoneRegex.test(this.userinfo2)) {
						this.$message.error('请输入有效的手机号码');
						return;
					}

					// 如果是纸质文件邮寄，还需要验证快递单号
					if (this.materialOption === 'mail' && (!this.trackingNumber || !this.courierCompany)) {
						this.$message.error('请填写快递单号和选择快递公司');
						return;
					}
				}

				// 验证公司选择
				if (!this.cid || !this.cid.cid) {
					this.$message.error('请选择公司');
					return;
				}

				// 验证文件上传（如果需要）
				if ((this.serviceType === 'electronic' || (this.materialOption === 'upload' &&
					(this.serviceType === 'mail' || this.serviceType === 'both'))) &&
					!this.supplementFile) {
					this.$message.error('请上传文件');
					return;
				}

				// 验证打印选项（仅对非电子版服务检查）
				if (this.serviceType !== 'electronic' &&
					!this.selectedOptions.some(op =>
						op !== "需要工作室寄给客户" &&
						op !== "无需工作室寄给客户只要电子版" &&
						op !== "需要工作室寄给客户，同时也需要电子版"
					)
				) {
					this.$message.error('请至少选择一个打印选项');
					return;
				}

				// 验证营业执照选择
				if ((this.businessLicenseEnabled || this.onlyBusinessLicense) && this.selectedLicenseCompanies.length === 0) {
					this.$message.error('请选择营业执照公司');
					return;
				}
				
				// 处理选项
				let finalOptions = [...this.selectedOptions];

				// 添加营业执照选项
				if (this.businessLicenseEnabled && this.selectedLicenseCompanies.length > 0) {
					const licenseCompanyNames = this.selectedLicenseCompanies.map(cid => {
						const company = this.licenseCompanies.find(c => c.cid == cid);
						return company ? company.name : `公司ID:${cid}`;
					});
					finalOptions.push(`需要营业执照: ${licenseCompanyNames.join(', ')}`);
				}

				// 添加打印页数信息（仅对非电子版服务）
				if (this.serviceType !== 'electronic' && Number(this.num) > 0) {
					finalOptions.push(`打印${this.num}页`);
				}

				// 构建订单数据
				const orderData = {
					service_type: this.serviceType,
					company_id: this.cid.cid,
					customer_name: this.userinfo,
					customer_email: this.userEmail || null,
					customer_phone: this.userinfo2 || null,
					customer_address: this.userinfo3 || null,
					material_type: this.materialOption,
					business_license: this.businessLicenseEnabled ? 1 : 0,
					only_business_license: this.onlyBusinessLicense ? 1 : 0,
					selected_license_companies: this.selectedLicenseCompanies || [],
					license_company_details: this.selectedLicenseCompanies.map(cid => {
						const company = this.licenseCompanies.find(c => c.cid == cid);
						return company ? { cid: company.cid, name: company.name, price: company.price } : null;
					}).filter(item => item !== null),
					print_copies: this.serviceType === 'electronic' ? 0 : this.num,
					print_options: finalOptions,
					special_requirements: this.remarks || null,
					courier_company: this.courierCompany || null,
					tracking_number: this.trackingNumber || null
				};

				// 提交订单
				const loading = this.$loading({
					lock: true,
					text: '提交中...'
				});

				this.$http.post("/sxgz/api.php?action=create_order", orderData, {
					emulateJSON: true
				}).then(response => {
					loading.close();
					if (response.data.success) {
						// 如果有文件需要上传
						if (this.supplementFile && this.supplementFile.file) {
							this.uploadOrderFile(response.data.data.order_id);
						} else {
							this.$message.success('订单创建成功');
							this.dialogVisible = false;
							this.resetForm();
							this.resetStepForm();
							this.get(1); // 刷新列表
						}
					} else {
						this.$message.error(response.data.message || '订单创建失败');
					}
				}).catch(() => {
					loading.close();
					this.$message.error('提交失败，请重试');
				});
			},

			// 上传订单文件
			uploadOrderFile(orderId) {
				if (!this.supplementFile || !this.supplementFile.file) {
					this.$message.success('订单创建成功');
					this.dialogVisible = false;
					this.resetForm();
					this.resetStepForm();
					this.get(1);
					return;
				}

				const formData = new FormData();
				formData.append('file', this.supplementFile.file);
				formData.append('order_id', orderId);

				const loading = this.$loading({
					lock: true,
					text: '正在上传文件...'
				});

				this.$http.post("/sxgz/api.php?action=upload_file", formData, {
					headers: {
						'Content-Type': 'multipart/form-data'
					}
				}).then(response => {
					loading.close();

					if (response.data.code === 1 || response.data.success) {
						this.$message.success('订单创建成功，文件上传成功');
					} else {
						console.error('文件上传失败:', response.data);
						this.$message.warning('订单创建成功，文件上传失败：' + (response.data.msg || response.data.message || '请稍后补充上传'));
					}
					this.dialogVisible = false;
					this.resetForm();
					this.resetStepForm();
					this.get(1);
				}).catch(error => {
					loading.close();
					console.error('文件上传请求失败:', error);
					this.$message.success('订单创建成功，文件将在后台处理');
					this.dialogVisible = false;
					this.resetForm();
					this.resetStepForm();
					this.get(1);
				});
			},

			getclass() {
				const loading = this.$loading({
					lock: true,
					text: '加载中...'
				});

				// 优化数据获取方式，添加缓存支持
				if (this.class1Cache && this.class1Cache.length > 0) {
					this.class1 = this.class1Cache;
					loading.close();
					return;
				}

				this.$http.get("/sxgz/api.php?action=get_companies").then(response => {
					loading.close();
					if(response.data.success) {
						const allCompanies = response.data.data.map(company => ({
							cid: company.cid,
							name: company.name,
							price: parseFloat(company.price) || 0,
							content: company.content || ''
						}));

						// 分离营业执照公司和普通公司
						this.licenseCompanies = allCompanies.filter(company =>
							company.name.includes('营业执照') || company.cid == 25709
						);

						// 普通公司列表（排除营业执照公司）
						this.class1 = allCompanies.filter(company =>
							!company.name.includes('营业执照') && company.cid != 25709
						);

						// 如果没有营业执照公司，添加默认的
						if (this.licenseCompanies.length === 0) {
							this.licenseCompanies.push({
								cid: 25709,
								name: "营业执照专用公司",
								price: 100,
								content: "提供营业执照服务"
							});
						}

						// 缓存结果
						this.class1Cache = [...this.class1];
					} else {
						this.$message.error(response.data.message || '获取公司列表失败');
					}
				}).catch(() => {
					loading.close();
					this.$message.error('获取公司列表失败');
				});
			},
			getCookie(name) {
				const cookies = document.cookie.split(';');
				for (let i = 0; i < cookies.length; i++) {
						const cookie = cookies[i].trim();
						if (cookie.startsWith(name + '=')) {
							return cookie.substring(name.length + 1);
					}
				}
				return 1;
			},
			toggleSelection(rows) {
				if (rows) {
				rows.forEach(row => {
					this.$refs.multipleTable.toggleRowSelection(row);
				});
				} else {
				this.$refs.multipleTable.clearSelection();
				}
			},
			handleSelectionChange(val) {
				this.multipleSelection = val;
				this.selectedOrders = val; // 同时更新导出用的选中订单
			},

			// 选择所有订单
			selectAllOrders() {
				this.$nextTick(() => {
					if (this.$refs.multipleTable) {
						this.$refs.multipleTable.toggleAllSelection();
					}
				});
			},

			// 清空选择
			clearSelection() {
				if (this.$refs.multipleTable) {
					this.$refs.multipleTable.clearSelection();
				}
				this.selectedOrders = [];
				this.multipleSelection = [];
			},
			status_text: function(status) {
				if (this.multipleSelection.length === 0) {
					this.$message.warning('请先选择需要修改状态的订单');
					return;
				}
				
				this.$confirm(`确定将选中的 ${this.multipleSelection.length} 条订单状态修改为 "${status}" 吗?`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					const oids = this.multipleSelection.map(item => item.oid);
					const loading = this.$loading({
						lock: true,
						text: '处理中...',
						spinner: 'el-icon-loading',
						background: 'rgba(0, 0, 0, 0.7)'
					});
					
					$.post("/apisub.php?act=status_order&a=" + status, {
						sex: oids,
						type: 1
					}, {
						emulateJSON: true
					}).then(response => {
						loading.close();
						if (response.code == 1) {
							this.$message.success(response.msg || '状态修改成功');
							this.get(this.row.current_page); // 刷新当前页数据
							this.$refs.multipleTable.clearSelection(); // 清除选择
						} else {
							this.$message.error(response.msg || '状态修改失败');
						}
					}).catch(() => {
						loading.close();
						this.$message.error('操作失败，请重试');
					});
				}).catch(() => {
					this.$message.info('已取消操作');
				});
			},
			
			selectAll: function() {
				if (this.multipleSelection.length > 0) {
					// 如果已有选中的，则清除选择
					this.$refs.multipleTable.clearSelection();
				} else {
					// 否则选中所有
					this.row.data.forEach(row => {
						this.$refs.multipleTable.toggleRowSelection(row, true);
					});
				}
			},
			
			processBatchAction: function(actionType) {
				if (this.multipleSelection.length === 0) {
					this.$message.warning('请先选择需要操作的订单');
					return;
				}
				
				let apiUrl = '';
				let confirmMsg = '';
				
				if (actionType === '撤销订单') {
					apiUrl = '/apisub.php?act=qx_order';
					confirmMsg = '确定要撤销选中的订单吗？此操作不可恢复';
				} else if (actionType === '更新订单') {
					apiUrl = '/apisub.php?act=plup&a=待更新';
					confirmMsg = '确定要更新选中的订单吗？';
				}
				
				this.$confirm(confirmMsg, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					const oids = this.multipleSelection.map(item => item.oid);
					const loading = this.$loading({
						lock: true,
						text: '处理中...',
						spinner: 'el-icon-loading',
						background: 'rgba(0, 0, 0, 0.7)'
					});
					
					$.post(apiUrl, {
						sex: oids
					}, {
						emulateJSON: true
					}).then(response => {
						loading.close();
						if (response.code == 1) {
							this.$message.success(response.msg || '操作成功');
							this.get(this.row.current_page); // 刷新当前页数据
							this.$refs.multipleTable.clearSelection(); // 清除选择
						} else {
							this.$message.error(response.msg || '操作失败');
						}
					}).catch(() => {
						loading.close();
						this.$message.error('操作失败，请重试');
					});
				}).catch(() => {
					this.$message.info('已取消操作');
				});
			},
			addfiles(oid){
				// 上传文件  接口：/apisub.php?act=uploadFile
				this.currentOid = oid;
				this.uploadDialogVisible = true;
			},
			handleUploadDialogClose(done) {
				this.$confirm('确认关闭？')
					.then(_ => {
						done();
						this.supplementFile = null;
					})
					.catch(_ => {});
			},
			submitSupplementFile() {
				if(!this.currentOid) {
					this.$message.error('请选择需要上传文件的订单');
					return;
				}
				if(!this.supplementFile) {
					this.$message.error('请先上传文件');
					return;
				}
				
				const loading = this.$loading({
					lock: true,
					text: '提交中...'
				});
				
				this.$http.post("/apisub.php?act=uploadFile", {
					oid: this.currentOid,
					filedownurl: this.supplementFile.downloadUrl
				}, {
					emulateJSON: true
				}).then(response => {
					loading.close();
					if(response.data.code === 1) {
						this.$message.success(response.data.msg || '文件关联成功');
						this.uploadDialogVisible = false;
						this.supplementFile = null;
						this.get(1); // 刷新列表
					} else {
						this.$message.error(response.data.msg || '提交失败');
					}
				}).catch(() => {
					loading.close();
					this.$message.error('提交失败，请重试');
				});
			},
			dowfiles(url){
				// 下载文件
				if (!url) {
					this.$message.error('下载链接不存在');
					return;
				}
				
				// 创建一个临时的a标签用于下载
				const link = document.createElement('a');
				link.href = url;
				link.setAttribute('download', ''); // 指示浏览器下载而非打开
				link.style.display = 'none';
				
				// 添加到页面并触发点击
				document.body.appendChild(link);
				link.click();
				
				// 点击后移除该元素
				setTimeout(() => {
					document.body.removeChild(link);
				}, 100);
			},
			showExportDialog() {
				// 根据是否有选中订单来设置默认导出类型
				if (this.selectedOrders.length > 0) {
					this.exportForm.exportType = 'selected';
				} else {
					this.exportForm.exportType = 'filtered';
				}
				this.exportDialogVisible = true;
			},
			handleExportDialogClose(done) {
				this.$confirm('确认关闭？')
					.then(_ => {
						done();
					})
					.catch(_ => {});
			},
			exportExcel(){
				// 验证导出格式
				if (!this.exportForm.format) {
					this.$message.error('请选择导出格式');
					return;
				}

				// 创建loading状态
				const loading = this.$loading({
					lock: true,
					text: '导出中...',
					spinner: 'el-icon-loading',
					background: 'rgba(0, 0, 0, 0.7)'
				});

				// 准备参数
				const params = new URLSearchParams();
				params.append('format', this.exportForm.format);

				// 验证导出类型
				if (this.exportForm.exportType === 'selected' && this.selectedOrders.length === 0) {
					loading.close();
					this.$message.error('请先选择要导出的订单');
					return;
				}

				// 添加导出类型参数
				params.append('export_type', this.exportForm.exportType);

				// 根据导出类型添加不同的参数
				if (this.exportForm.exportType === 'all') {
					// 导出所有订单，不添加任何筛选条件
				} else if (this.exportForm.exportType === 'filtered') {
					// 导出当前筛选结果
					if (this.exportForm.status) {
						params.append('status', this.exportForm.status);
					}
					if (this.cx.status) {
						params.append('status', this.cx.status);
					}
					if (this.cx.mh && this.cx.mh.trim()) {
						params.append('search', this.cx.mh.trim());
						// 只有当搜索字段不为空时才添加search_field参数
						if (this.cx.search && this.cx.search.trim()) {
							params.append('search_field', this.cx.search.trim());
						}
					}
				} else if (this.exportForm.exportType === 'selected') {
					// 导出选中订单
					const orderIds = this.selectedOrders.map(order => order.order_id).join(',');
					params.append('order_ids', orderIds);
				}

				// 关闭导出对话框
				this.exportDialogVisible = false;

				// 创建下载链接
				const downloadUrl = `/sxgz/api.php?action=export_orders&${params.toString()}`;

				// 创建临时链接进行下载
				const link = document.createElement('a');
				link.href = downloadUrl;
				link.style.display = 'none';
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);

				// 延迟关闭loading，给下载一些时间
				setTimeout(() => {
					loading.close();
					const formatText = this.exportForm.format === 'csv' ? 'CSV' : 'Excel';
					this.$message.success(`${formatText}文件导出成功`);
				}, 1000);
			},
			

			
			validateMaterialAndContinue() {
				// 如果选择了仅需要营业执照，无需验证材料
				if (this.onlyBusinessLicense) {
					this.nextStep();
					return;
				}
				
				// 验证材料上传
				if (this.serviceType === 'electronic' && !this.supplementFile) {
					this.$message.warning('请上传需要盖章的文件');
					return;
				}
				
				if (this.serviceType !== 'electronic') {
					if (!this.materialOption) {
						this.$message.warning('请选择材料提供方式');
						return;
					}
					
					if (this.materialOption === 'upload' && !this.supplementFile) {
						this.$message.warning('请上传需要盖章的文件');
						return;
					}
				}
				
				this.nextStep();
			},
			
			validateAddressAndContinue() {
				// 如果选择了仅需要营业执照，验证必要字段后直接进入下一步
				if (this.onlyBusinessLicense) {
					// 验证客户姓名
					if (!this.userinfo) {
						this.$message.warning('请填写客户姓名');
						return;
					}
					
					// 验证姓名长度和格式
					if (this.userinfo.length < 2) {
						this.$message.warning('客户姓名至少需要2个字符');
						return;
					}
					
					if (this.userinfo.includes(' ')) {
						this.$message.error('客户姓名不能包含空格');
						return;
					}
					
					// 验证邮箱
					if (!this.userEmail) {
						this.$message.warning('请填写邮箱信息以接收电子版营业执照');
						return;
					}
					
					// 验证邮箱格式
					const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
					if (!emailRegex.test(this.userEmail)) {
						this.$message.error('请输入有效的邮箱地址');
						return;
					}
					
					this.nextStep();
					return;
				}
				
				// 验证收货信息
				if (this.serviceType === 'electronic') {
					// 电子版服务需要验证客户姓名和邮箱信息
					if (!this.userinfo) {
						this.$message.warning('请填写客户姓名');
						return;
					}
					
					// 验证姓名长度和格式
					if (this.userinfo.length < 2) {
						this.$message.warning('客户姓名至少需要2个字符');
						return;
					}
					
					if (this.userinfo.includes(' ')) {
						this.$message.error('客户姓名不能包含空格');
						return;
					}
					
					if (!this.userEmail) {
						this.$message.warning('请填写邮箱信息');
						return;
					}
					
					// 验证邮箱格式
					const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
					if (!emailRegex.test(this.userEmail)) {
						this.$message.error('请输入有效的邮箱地址');
						return;
					}
				} else if (this.serviceType === 'both' && this.materialOption === 'upload') {
					// 邮寄+电子版需要验证收件人信息和邮箱
					if (!this.userinfo || !this.userinfo2 || !this.userinfo3 || !this.userEmail) {
						this.$message.warning('请填写完整的收件人信息和邮箱地址');
						return;
					}
					
					// 验证手机号格式
					const phoneRegex = /^1[3-9]\d{9}$/;
					if (!phoneRegex.test(this.userinfo2)) {
						this.$message.error('请输入有效的手机号码');
						return;
					}
					
					// 验证邮箱格式
					const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
					if (!emailRegex.test(this.userEmail)) {
						this.$message.error('请输入有效的邮箱地址');
						return;
					}
					
					if (this.userinfo.includes(' ') || this.userinfo2.includes(' ') || 
						this.userinfo3.includes(' ') || this.userEmail.includes(' ')) {
						this.$message.error('用户信息不能包含空格');
						return;
					}
				} else {
					// 其他服务类型需要验证完整的收货信息
					if (!this.userinfo || !this.userinfo2 || !this.userinfo3) {
						this.$message.warning('请填写完整的收件人信息');
						return;
					}
					
					// 验证手机号格式
					const phoneRegex = /^1[3-9]\d{9}$/;
					if (!phoneRegex.test(this.userinfo2)) {
						this.$message.error('请输入有效的手机号码');
						return;
					}
					
					// 如果是纸质文件邮寄，还需要验证快递单号
					if (this.materialOption === 'mail' && (!this.trackingNumber || !this.courierCompany)) {
						this.$message.warning('请填写快递单号和选择快递公司');
						return;
					}
					
					if (this.userinfo.includes(' ') || this.userinfo2.includes(' ') || this.userinfo3.includes(' ') || 
						(this.materialOption === 'mail' && this.trackingNumber.includes(' '))) {
						this.$message.error('收件人信息不能包含空格');
						return;
					}
				}
				
				this.nextStep();
			},
			
			prevStep() {
				if (this.activeStep > 0) {
					this.activeStep--;
				}
			},
			
			nextStep() {
				this.activeStep++;
			},

			getPrintOptions() {
				// 根据服务类型返回不同的打印选项
				// 基础选项
				const baseOptions = [
					{name: "一式三份"},
					{name: "一式两份"},
					{name: "一式一份"},
					{name: "单面打印"},
					{name: "双面打印"},
					{name: "彩印"},
					{name: "黑白"},
					{name: "骑缝章"},
					{name: "只盖章"}
				];
				
				// 电子版服务不提供额外选项
				if (this.serviceType === 'electronic') {
					return [];
				}
				
				return baseOptions;
			},
			
			togglePrintOption(optionName) {
				// 不允许修改服务类型相关的固定选项
				if (
					optionName === "无需工作室寄给客户只要电子版" || 
					optionName === "需要工作室寄给客户" || 
					optionName === "需要工作室寄给客户，同时也需要电子版"
				) {
					return;
				}
				
				const index = this.selectedOptions.indexOf(optionName);
				if (index > -1) {
					this.selectedOptions.splice(index, 1);
				} else {
					// 处理互斥选项
					if (optionName === "一式三份" || optionName === "一式两份" || optionName === "一式一份") {
						// 移除其他份数选项
						this.selectedOptions = this.selectedOptions.filter(op => 
							op !== "一式三份" && op !== "一式两份" && op !== "一式一份");
					}
					
					if (optionName === "单面打印" || optionName === "双面打印") {
						// 移除其他打印方式
						this.selectedOptions = this.selectedOptions.filter(op => 
							op !== "单面打印" && op !== "双面打印");
					}
					
					if (optionName === "彩印" || optionName === "黑白") {
						// 移除其他颜色选项
						this.selectedOptions = this.selectedOptions.filter(op => 
							op !== "彩印" && op !== "黑白");
					}
					
					this.selectedOptions.push(optionName);
				}
			},
			// 优化公司选择后的处理
			handleCompanyChange(selectedCompany) {
				// 只做必要的数据更新，不执行耗时操作
				this.tips(selectedCompany);
				// 更新基础价格
				if (selectedCompany && selectedCompany.price) {
					this.jichuprice = selectedCompany.price;
				}
				// 预加载下一步可能需要的数据
				this.preloadNextStepData();
			},
			// 预加载下一步可能需要的数据
			preloadNextStepData() {
				// 这个方法可以用来提前加载一些下一步可能需要的数据
				// 但注意不要执行太多耗时操作
			},
			// 点击下一步按钮时的处理
			goToNextStepAfterCompany() {
				// 验证选择
				if (this.onlyBusinessLicense) {
					if (this.selectedLicenseCompanies.length === 0) {
						this.$message.warning('请选择营业执照公司');
						return;
					}
				} else {
					if (!this.cid || !this.cid.cid) {
						this.$message.warning('请选择公司');
						return;
					}
				}

				this.companyLoading = true;

				// 使用setTimeout将耗时操作放入下一个事件循环，避免阻塞UI
				setTimeout(() => {
					try {
						// 更新营业执照费用
						this.updateLicensePrice();

						// 进入下一步
						this.nextStep();
					} catch(err) {
						console.error('处理公司选择时出错:', err);
						this.$message.error('处理失败，请重试');
					} finally {
						this.companyLoading = false;
					}
				}, 10);
			},
			// 窗口大小变化处理函数
			handleResize() {
				this.windowWidth = window.innerWidth;
			}
		},
		computed: {
			filteredOrderOptions() {
				return this.orderOptions;
			},
			// 判断是否可以进入下一步
			canGoToNextStep() {
				if (this.onlyBusinessLicense) {
					// 仅需要营业执照：必须选择营业执照公司
					return this.selectedLicenseCompanies.length > 0;
				} else {
					// 普通模式：必须选择普通公司
					return this.cid && this.cid.cid;
				}
			},
			calculatedTotalPrice() {
				// 获取用户费率
				let userRate = parseFloat(this.userrow.addprice) || 1.0;
				if (userRate <= 0) {
					userRate = 1.0; // 默认费率
				}

				// 基础价格需要乘以用户费率
				let basePrice = (parseFloat(this.jichuprice) || 0) * userRate;

				// 打印费用不受用户费率影响
				let printPrice = parseFloat(this.numprice) || 0;

				// 营业执照费用计算
				let licensePrice = 0;
				if (this.onlyBusinessLicense) {
					// 仅需要营业执照模式：营业执照费用就是基础费用，不额外收费
					licensePrice = 0;
					// 基础费用已经在jichuprice中设置了营业执照费用
					// 如果没有选择营业执照公司，使用默认价格
					if (this.selectedLicenseCompanies.length === 0) {
						basePrice = 100 * userRate;
					}
				} else if (this.businessLicenseEnabled) {
					// 额外营业执照费用：营业执照费用受用户费率影响
					licensePrice = this.totalLicensePrice * userRate;
				}

				// 计算总价并确保有两位小数
				let total = basePrice + printPrice + licensePrice;
				return total.toFixed(2);
			}
		},
		mounted() {
			this.get(1);
			window.addEventListener('resize', this.handleResize);
			this.handleResize();
		},
		
		beforeDestroy() {
			// 移除窗口大小变化的监听器，避免内存泄漏
			window.removeEventListener('resize', this.handleResize);
		}

	});
</script>
