<?php
/**
 * 实习盖章管理员界面
 */
$title = '实习盖章管理';
require_once('head.php');

// 验证管理员权限
if ($userrow['uid'] != 1) {
    exit("<script>alert('权限不足');history.back();</script>");
}
?>

<link rel="stylesheet" href="/sxgz/style.css">
<link rel="stylesheet" href="/sxgz/element/element.css">

<style>
/* ==================== 简约色彩方案 ==================== */
:root {
    --primary-color: #2c3e50;      /* 深蓝灰 - 主色 */
    --secondary-color: #34495e;    /* 中蓝灰 - 辅助色 */
    --accent-color: #3498db;       /* 蓝色 - 强调色 */
    --success-color: #27ae60;      /* 绿色 - 成功 */
    --warning-color: #f39c12;      /* 橙色 - 警告 */
    --danger-color: #e74c3c;       /* 红色 - 危险 */
    --text-primary: #2c3e50;       /* 主要文字 */
    --text-secondary: #7f8c8d;     /* 次要文字 */
    --text-muted: #95a5a6;         /* 弱化文字 */
    --border-color: #ecf0f1;       /* 边框色 */
    --bg-light: #f8f9fa;           /* 浅背景 */
    --bg-white: #ffffff;           /* 白色背景 */
}

/* ==================== 表格容器样式 ==================== */
.table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* 宽屏幕优化 */
@media (min-width: 1400px) {
    .el-table {
        min-width: 1400px;
    }
}

@media (min-width: 1600px) {
    .el-table {
        min-width: 1600px;
    }
}

/* 统计卡片样式 */
.stats-container {
    margin-bottom: 10px; /* 统计卡片间距 */
}

.stats-card {
    border-radius: 12px; /* 圆角 */
    border: none; /* 去除边框 */   
    overflow: hidden; /* 隐藏溢出内容 */
    transition: all 0.3s ease; /* 添加过渡动画 */
}

.stats-card:hover {
    transform: translateY(-2px); /* 鼠标悬停时轻微上移 */
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important; /* 增加阴影 */
}

.stats-content {
    display: flex; /* 使内容水平排列 */
    align-items: center; /* 垂直居中 */
    padding: 5px; /* 内边距 */
}

.stats-icon {
    width: 50px; /* 图标宽度 */
    height: 50px; /* 图标高度 */
    border-radius: 12px; /* 圆角 */
    display: flex; /* 图标居中 */
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
    margin-right: 15px; /* 图标与文字间距 */
    flex-shrink: 0; /* 不压缩宽度 */
}

.stats-icon i {
    font-size: 24px; /* 图标大小 */
    color: white; /* 图标颜色 */
}

.stats-info {
    flex: 1; /* 占满剩余空间 */
    min-width: 0; /* 防止内容被挤压 */
}

.stats-number {
    font-size: 24px; /* 数字大小 */
    font-weight: bold; /* 加粗 */
    color: #303133; 
    line-height: 1; 
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.stats-label {
    font-size: 13px;
    color: #909399;
    line-height: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .stats-number {
        font-size: 20px;
    }
    .stats-icon {
        width: 45px;
        height: 45px;
        margin-right: 12px;
    }
    .stats-icon i {
        font-size: 20px;
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    /* 统计卡片移动端优化 */
    .stats-container .el-col {
        margin-bottom: 12px;
    }
    .stats-number {
        font-size: 18px;
    }
    .stats-label {
        font-size: 12px;
    }
    .stats-icon {
        width: 40px;
        height: 40px;
        margin-right: 10px;
    }
    .stats-icon i {
        font-size: 18px;
    }

    /* 页面标题区域移动端优化 */
    .panel-heading {
        padding: 10px 15px !important;
    }

    .panel-heading h4 {
        font-size: 16px !important;
        margin: 0 !important;
    }

    /* 搜索区域移动端优化 */
    .search-wrapper {
        padding: 8px !important;
        margin-bottom: 8px !important;
    }

    .search-wrapper .el-row {
        margin: 0 !important;
    }

    .search-wrapper .el-col {
        margin-bottom: 8px;
        padding: 0 4px !important;
    }

    /* 批量操作区域移动端优化 */
    .batch-actions {
        margin: 8px 0 !important;
        padding: 6px 8px !important;
    }

    .batch-actions > div {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 8px;
    }

    .batch-actions .el-button {
        font-size: 12px !important;
        padding: 5px 8px !important;
    }

    /* 表格移动端优化 */
    .el-table {
        font-size: 12px !important;
        min-width: 800px !important;
    }

    .el-table th {
        padding: 8px 4px !important;
        font-size: 11px !important;
    }

    .el-table td {
        padding: 6px 4px !important;
        font-size: 11px !important;
    }



    /* 按钮移动端优化 */
    .el-button--mini {
        padding: 4px 6px !important;
        font-size: 10px !important;
    }

    .el-button--small {
        padding: 6px 10px !important;
        font-size: 11px !important;
    }

    /* 分页移动端优化 */
    .el-pagination {
        text-align: center !important;
        padding: 10px 0 !important;
    }

    .el-pagination .el-pager li {
        min-width: 28px !important;
        height: 28px !important;
        line-height: 28px !important;
        font-size: 12px !important;
    }

    .el-pagination .btn-prev,
    .el-pagination .btn-next {
        min-width: 28px !important;
        height: 28px !important;
        line-height: 28px !important;
    }
}



/* 表格滚动容器样式 */
.table-container {
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-container::-webkit-scrollbar {
    height: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 操作按钮容器样式 */
.action-buttons-container {
    display: flex;
    flex-direction: column;
    gap: 6px;
    align-items: center;
    min-height: 60px;
    justify-content: center;
}

.primary-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    justify-content: center;
    align-items: center;
}

.more-actions {
    margin-top: 2px;
}

/* 电脑端优化 */
@media (min-width: 1024px) {
    .action-buttons-container {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 4px;
        min-height: auto;
        justify-content: center;
    }

    .primary-actions {
        flex: 1;
        justify-content: flex-start;
    }

    .more-actions {
        margin-top: 0;
        margin-left: 4px;
    }
}

/* 按钮样式优化 */
.el-table .el-button--small {
    padding: 5px 8px !important;
    font-size: 12px !important;
    border-radius: 3px !important;
    font-weight: 500 !important;
}

.el-table .el-button--small .el-icon {
    margin-right: 2px !important;
}

/* 状态按钮颜色优化 */
.el-button--success {
    background-color: #67c23a !important;
    border-color: #67c23a !important;
}

.el-button--warning {
    background-color: #e6a23c !important;
    border-color: #e6a23c !important;
}

.el-button--info {
    background-color: #909399 !important;
    border-color: #909399 !important;
}

/* 按钮悬停效果 */
.el-table .el-button:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.2s ease !important;
}

.el-table .el-button:active {
    transform: translateY(0) !important;
    transition: all 0.1s ease !important;
}

/* 危险操作按钮特殊样式 */
.el-dropdown-menu__item[style*="color: #f56c6c"] {
    background-color: #fef0f0 !important;
}

.el-dropdown-menu__item[style*="color: #f56c6c"]:hover {
    background-color: #f56c6c !important;
    color: white !important;
}

/* 按钮组间距优化 */
.primary-actions .el-button + .el-button {
    margin-left: 0 !important;
}

/* 下拉菜单样式优化 */
.el-dropdown-menu {
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #e4e7ed !important;
}

.el-dropdown-menu__item {
    padding: 8px 16px !important;
    font-size: 13px !important;
    transition: all 0.2s ease !important;
}

.el-dropdown-menu__item:hover {
    background-color: #f5f7fa !important;
}

.el-dropdown-menu__item i {
    margin-right: 6px !important;
    width: 14px !important;
}

/* 批量操作区域样式 - 简约设计 */
.batch-actions {
    margin: 15px 0;
    padding: 12px 16px;
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.batch-actions-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
}

.batch-info {
    display: flex;
    align-items: center;
    color: #409EFF;
    font-size: 14px;
    font-weight: 500;
}

.batch-info i {
    margin-right: 8px;
    font-size: 16px;
}

.batch-info strong {
    color: #1890ff;
    margin: 0 2px;
}

.batch-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.batch-dropdown .el-button {
    border-radius: 4px !important;
}

.batch-delete-btn {
    background-color: #ff4d4f !important;
    border-color: #ff4d4f !important;
    color: white !important;
}

.batch-delete-btn:hover {
    background-color: #ff7875 !important;
    border-color: #ff7875 !important;
    color: white !important;
}

/* 批量操作移动端优化 */
@media (max-width: 768px) {
    .batch-actions {
        margin: 10px 0;
        padding: 10px 12px;
    }

    .batch-actions-content {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .batch-info {
        justify-content: center;
        font-size: 13px;
    }

    .batch-buttons {
        flex-direction: column !important;
        gap: 8px !important;
        align-items: stretch !important;
    }

    .batch-buttons .batch-dropdown,
    .batch-buttons .el-button {
        width: 100% !important;
        margin-right: 0 !important;
        margin-bottom: 0 !important;
        min-height: 36px !important;
    }

    .batch-buttons .batch-dropdown .el-button {
        width: 100% !important;
        justify-content: center !important;
        text-align: center !important;
        padding: 8px 15px !important;
        font-size: 13px !important;
    }

    .batch-buttons .el-button {
        padding: 8px 15px !important;
        font-size: 13px !important;
        text-align: center !important;
    }

    /* 移动端下拉箭头优化 */
    .batch-buttons .batch-dropdown .el-icon--right {
        margin-left: 8px !important;
    }
}

/* 批量操作按钮样式优化 */
.batch-buttons .batch-dropdown {
    margin-right: 8px;
}

.batch-buttons .batch-dropdown .el-button {
    border-radius: 4px !important;
    font-weight: 500 !important;
    min-width: 100px !important;
    color: white !important;
}

.batch-buttons .batch-dropdown .el-button:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.2s ease !important;
    color: white !important;
}

/* 确保所有批量操作按钮文字为白色 */
.batch-buttons .el-button {
    color: white !important;
}

.batch-buttons .el-button:hover,
.batch-buttons .el-button:focus,
.batch-buttons .el-button:active {
    color: white !important;
}

/* 批量操作下拉菜单按钮文字颜色 */
.batch-dropdown .el-button span {
    color: white !important;
}

.batch-dropdown .el-button:hover span,
.batch-dropdown .el-button:focus span,
.batch-dropdown .el-button:active span {
    color: white !important;
}
    opacity: 1 !important;
    visibility: visible !important;
}

.batch-buttons .el-button span {
    opacity: 1 !important;
    visibility: visible !important;
    display: inline !important;
}

/* 批量操作下拉菜单样式 */
.batch-dropdown .el-dropdown-menu {
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #e4e7ed !important;
    padding: 4px 0 !important;
}

.batch-dropdown .el-dropdown-menu__item {
    padding: 8px 16px !important;
    font-size: 13px !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
}

.batch-dropdown .el-dropdown-menu__item:hover {
    background-color: #f5f7fa !important;
}

.batch-dropdown .el-dropdown-menu__item i {
    margin-right: 8px !important;
    width: 16px !important;
    text-align: center !important;
}

/* 危险操作样式 */
.batch-dropdown .el-dropdown-menu__item[command="fail"],
.batch-dropdown .el-dropdown-menu__item[command="reject"] {
    color: #f56c6c !important;
}

.batch-dropdown .el-dropdown-menu__item[command="fail"]:hover,
.batch-dropdown .el-dropdown-menu__item[command="reject"]:hover {
    background-color: #fef0f0 !important;
}

/* 成功操作样式 */
.batch-dropdown .el-dropdown-menu__item[command="complete"],
.batch-dropdown .el-dropdown-menu__item[command="approve"] {
    color: #67c23a !important;
}

.batch-dropdown .el-dropdown-menu__item[command="complete"]:hover,
.batch-dropdown .el-dropdown-menu__item[command="approve"]:hover {
    background-color: #f0f9ff !important;
}

/* 表格行悬停效果优化 */
.el-table tbody tr:hover {
    background-color: #f8f9fa !important;
}

/* 状态标签样式优化 */
.el-tag {
    border-radius: 12px !important;
    padding: 0 8px !important;
    font-size: 11px !important;
    font-weight: 500 !important;
}

/* 操作列固定优化 */
.el-table__fixed-right {
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1) !important;
}

/* 加载状态优化 */
.el-loading-mask {
    background-color: rgba(255, 255, 255, 0.8) !important;
}

.el-loading-spinner .circular {
    width: 32px !important;
    height: 32px !important;
}

/* 分页样式优化 */
.el-pagination {
    margin-top: 20px !important;
    text-align: center !important;
}

.el-pagination .el-pager li {
    border-radius: 4px !important;
    margin: 0 2px !important;
}

.el-pagination .el-pager li.active {
    background-color: #409EFF !important;
    color: white !important;
}

/* 搜索区域按钮优化 */
.search-wrapper .el-button {
    border-radius: 4px !important;
    font-weight: 500 !important;
}

.search-wrapper .el-button--primary {
    background-color: #409EFF !important;
    border-color: #409EFF !important;
}

.search-wrapper .el-button--success {
    background-color: #67c23a !important;
    border-color: #67c23a !important;
}

/* 公司信息列样式 */
.company-info-cell {
    line-height: 1.4;
}

.company-info-cell .main-company {
    margin-bottom: 6px;
    padding-bottom: 4px;
    border-bottom: 1px solid #f0f0f0;
}

.company-info-cell .license-companies {
    background: #fff7e6;
    padding: 6px 8px;
    border-radius: 4px;
    border-left: 3px solid #E6A23C;
    margin-top: 4px;
}

.company-info-cell .el-tag {
    margin-right: 4px;
    margin-bottom: 2px;
    font-size: 10px !important;
    padding: 1px 4px !important;
    border-radius: 2px !important;
}

/* 移动端公司信息优化 */
@media (max-width: 768px) {
    .company-info-cell .main-company {
        font-size: 11px !important;
    }

    .company-info-cell .license-companies {
        padding: 4px 6px !important;
        margin-top: 3px !important;
    }

    .company-info-cell .el-tag {
        font-size: 9px !important;
        padding: 0 3px !important;
    }
}

/* 客户信息列样式 */
.customer-info-cell {
    line-height: 1.4;
}

.customer-info-cell .customer-name {
    margin-bottom: 4px;
    padding-bottom: 3px;
    border-bottom: 1px solid #f0f0f0;
}

/* 服务详情列样式 */
.service-info-cell {
    line-height: 1.4;
}

.service-info-cell .el-tag {
    margin-bottom: 3px;
    font-size: 10px !important;
    padding: 1px 4px !important;
}

/* 移动端客户信息和服务详情优化 */
@media (max-width: 768px) {
    .customer-info-cell,
    .service-info-cell {
        font-size: 11px !important;
    }

    .customer-info-cell .customer-name {
        font-size: 12px !important;
        margin-bottom: 3px !important;
    }

    .service-info-cell .el-tag {
        font-size: 9px !important;
        padding: 1px 3px !important;
    }
}

/* 备注信息列样式 */
.notes-info-cell {
    line-height: 1.4;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    white-space: normal;
}

.notes-info-cell .el-tag {
    font-size: 10px !important;
    padding: 1px 4px !important;
    margin-bottom: 2px !important;
}

/* 备注文本自动换行 */
.notes-text {
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    line-height: 1.4 !important;
    max-width: 100% !important;
}

/* 订单详情弹窗样式 */
.order-overview-card .info-section {
    margin-bottom: 15px;
}

.order-overview-card .info-section h5 {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
}

.order-overview-card .info-section h5 i {
    margin-right: 6px;
}

.file-section {
    margin-bottom: 15px;
}

.file-section h6 {
    display: flex;
    align-items: center;
    font-size: 13px;
    font-weight: 600;
}

.file-section h6 i {
    margin-right: 6px;
}

/* 详情弹窗布局优化 */
.notes-card {
    height: 100%;
    transition: all 0.2s ease;
}

.notes-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.file-management-card {
    transition: all 0.2s ease;
}

.file-management-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 文件项悬停效果 */
.file-section .el-upload-dragger {
    border: 2px dashed var(--border-color) !important;
    border-radius: 6px !important;
    background-color: var(--bg-white) !important;
    transition: all 0.2s ease !important;
}

.file-section .el-upload-dragger:hover {
    border-color: var(--secondary-color) !important;
    background-color: var(--bg-light) !important;
}

/* 文件列表项悬停效果 */
.file-section > div > div[style*="background: var(--bg-light)"]:hover {
    background: #f0f2f5 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 隐藏ElementUI自动生成的上传文件按钮 */
.el-upload__input {
    display: none !important;
}

.el-upload-list {
    display: none !important;
}

.el-upload-list__item {
    display: none !important;
}

/* 移动端表格滚动提示 */
@media (max-width: 768px) {
    .table-container::before {
        content: "← 左右滑动查看更多 →";
        display: block;
        text-align: center;
        font-size: 12px;
        color: #999;
        padding: 5px 0;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    /* 移动端操作按钮优化 */
    .action-buttons-container {
        flex-direction: column !important;
        gap: 3px !important;
        min-height: 80px !important;
    }

    .primary-actions {
        flex-direction: column !important;
        gap: 3px !important;
        width: 100% !important;
    }

    .primary-actions .el-button {
        width: 100% !important;
        margin: 0 !important;
    }

    .more-actions {
        width: 100% !important;
        margin-top: 3px !important;
        margin-left: 0 !important;
    }

    .more-actions .el-button {
        width: 100% !important;
    }

    /* 对话框移动端优化 */
    .el-dialog {
        width: 95% !important;
        margin: 0 auto !important;
        margin-top: 5vh !important;
        max-height: 90vh !important;
    }

    .el-dialog__header {
        padding: 15px 15px 10px !important;
    }

    .el-dialog__body {
        padding: 10px 15px !important;
        max-height: 70vh !important;
        overflow-y: auto !important;
    }

    .el-dialog__footer {
        padding: 10px 15px 15px !important;
    }

    /* 表单移动端优化 */
    .el-form-item {
        margin-bottom: 15px !important;
    }

    .el-form-item__label {
        font-size: 13px !important;
        line-height: 1.4 !important;
    }

    .el-input__inner,
    .el-textarea__inner,
    .el-select .el-input__inner {
        font-size: 14px !important;
    }

    /* 标签页移动端优化 */
    .el-tabs__header {
        margin: 0 0 10px !important;
    }

    .el-tabs__nav-wrap {
        padding: 0 10px !important;
    }

    .el-tabs__item {
        padding: 0 15px !important;
        font-size: 13px !important;
        height: 35px !important;
        line-height: 35px !important;
    }

    /* 消息提示移动端优化 */
    .el-message {
        min-width: 280px !important;
        max-width: 90% !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }
}
</style>

<div class="app-content-body" id="sxgzAdmin">
    <div class="wrapper-md">
        <div class="panel panel-default">
            <div class="panel-heading font-bold bg-white">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h4 style="margin: 0; color: #303133;">
                            <i class="mdi mdi-stamp"></i> 实习盖章管理
                        </h4>
                        <p style="margin: 5px 0 0 0; color: #909399; font-size: 14px;">
                            实习盖章订单处理和公司管理
                        </p>
                    </div>
                </div>
            </div>

            <div class="panel-body">
                <!-- 标签页 -->
                <el-tabs v-model="activeTab" @tab-click="handleTabClick">
                    <!-- 订单管理 -->
                    <el-tab-pane label="订单管理" name="orders">
                        <!-- 统计数据卡片 -->
                        <div class="stats-container" style="margin-bottom: 12px;">
                            <el-row :gutter="10">
                                <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                    <el-card class="stats-card" shadow="hover">
                                        <div class="stats-content">
                                            <div class="stats-icon" style="background: var(--primary-color);">
                                                <i class="el-icon-document"></i>
                                            </div>
                                            <div class="stats-info">
                                                <div class="stats-number">{{ statistics.total_orders || 0 }}</div>
                                                <div class="stats-label">总订单数</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                    <el-card class="stats-card" shadow="hover">
                                        <div class="stats-content">
                                            <div class="stats-icon" style="background: var(--accent-color);">
                                                <i class="el-icon-loading"></i>
                                            </div>
                                            <div class="stats-info">
                                                <div class="stats-number">{{ statistics.processing_orders || 0 }}</div>
                                                <div class="stats-label">处理中</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                    <el-card class="stats-card" shadow="hover">
                                        <div class="stats-content">
                                            <div class="stats-icon" style="background: var(--warning-color);">
                                                <i class="el-icon-warning"></i>
                                            </div>
                                            <div class="stats-info">
                                                <div class="stats-number">{{ statistics.pending_orders || 0 }}</div>
                                                <div class="stats-label">待处理</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                    <el-card class="stats-card" shadow="hover">
                                        <div class="stats-content">
                                            <div class="stats-icon" style="background: var(--success-color);">
                                                <i class="el-icon-money"></i>
                                            </div>
                                            <div class="stats-info">
                                                <div class="stats-number">¥{{ (statistics.total_revenue || 0).toFixed(2) }}</div>
                                                <div class="stats-label">总收入</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>

                            <!-- 第二行统计 -->
                            <el-row :gutter="10" style="margin-top: 12px;">
                                <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                    <el-card class="stats-card" shadow="hover">
                                        <div class="stats-content">
                                            <div class="stats-icon" style="background: var(--danger-color);">
                                                <i class="el-icon-warning-outline"></i>
                                            </div>
                                            <div class="stats-info">
                                                <div class="stats-number">{{ statistics.refund_requests || 0 }}</div>
                                                <div class="stats-label">申请退款</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                    <el-card class="stats-card" shadow="hover">
                                        <div class="stats-content">
                                            <div class="stats-icon" style="background: var(--success-color);">
                                                <i class="el-icon-check"></i>
                                            </div>
                                            <div class="stats-info">
                                                <div class="stats-number">{{ statistics.completed_orders || 0 }}</div>
                                                <div class="stats-label">已完成</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                    <el-card class="stats-card" shadow="hover">
                                        <div class="stats-content">
                                            <div class="stats-icon" style="background: var(--secondary-color);">
                                                <i class="el-icon-date"></i>
                                            </div>
                                            <div class="stats-info">
                                                <div class="stats-number">{{ statistics.today_orders || 0 }}</div>
                                                <div class="stats-label">今日订单</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                    <el-card class="stats-card" shadow="hover">
                                        <div class="stats-content">
                                            <div class="stats-icon" style="background: var(--text-secondary);">
                                                <i class="el-icon-user"></i>
                                            </div>
                                            <div class="stats-info">
                                                <div class="stats-number">{{ statistics.active_users || 0 }}</div>
                                                <div class="stats-label">活跃用户</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>
                        </div>

                        <!-- 搜索筛选 -->
                        <div class="search-wrapper" style="background: #f8f9fa; padding: 10px; border-radius: 8px; margin-bottom: 10px;">
                            <el-row :gutter="20">
                                <el-col :xs="12" :sm="6" :md="3" :lg="3">
                                    <el-select v-model="searchForm.status" placeholder="订单状态" clearable @change="searchOrders" size="small" style="width: 100%;">
                                        <el-option label="全部状态" value=""></el-option>
                                        <el-option label="待处理" value="pending"></el-option>
                                        <el-option label="处理中" value="processing"></el-option>
                                        <el-option label="已完成" value="completed"></el-option>
                                        <el-option label="已取消" value="cancelled"></el-option>
                                        <el-option label="失败" value="failed"></el-option>
                                        <el-option label="申请退款" value="refund_requested"></el-option>
                                        <el-option label="已退款" value="refunded"></el-option>
                                    </el-select>
                                </el-col>
                                <el-col :xs="12" :sm="6" :md="3" :lg="3">
                                    <el-select v-model="searchForm.service_type" placeholder="服务类型" clearable @change="searchOrders" size="small" style="width: 100%;">
                                        <el-option label="全部类型" value=""></el-option>
                                        <el-option label="电子版" value="electronic"></el-option>
                                        <el-option label="邮寄服务" value="mail"></el-option>
                                        <el-option label="邮寄+电子版" value="both"></el-option>
                                    </el-select>
                                </el-col>
                                <el-col :xs="24" :sm="12" :md="8" :lg="8">
                                    <el-date-picker
                                        v-model="searchForm.dateRange"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        size="small"
                                        style="width: 100%;"
                                        @change="searchOrders">
                                    </el-date-picker>
                                </el-col>
                                <el-col :xs="24" :sm="12" :md="5" :lg="5">
                                    <el-input
                                        v-model="searchForm.search"
                                        placeholder="搜索订单号/客户姓名/邮箱"
                                        clearable
                                        size="small"
                                        @keyup.enter.native="searchOrders">
                                        <el-button slot="append" icon="el-icon-search" @click="searchOrders"></el-button>
                                    </el-input>
                                </el-col>
                                <el-col :xs="24" :sm="24" :md="6" :lg="6">
                                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                        <el-button @click="resetSearch" type="primary" size="small" icon="el-icon-refresh">重置</el-button>
                                        <el-dropdown @command="handleExport">
                                            <el-button type="success" size="small">
                                                <i class="el-icon-arrow-down el-icon--right"></i>导出数据
                                            </el-button>
                                            <el-dropdown-menu slot="dropdown">
                                                <el-dropdown-item command="csv">导出CSV</el-dropdown-item>
                                                <el-dropdown-item command="excel">导出Excel</el-dropdown-item>
                                            </el-dropdown-menu>
                                        </el-dropdown>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>

                        <!-- 批量操作区域 -->
                        <div v-if="selectedOrders.length > 0" class="batch-actions">
                            <div class="batch-actions-content">
                                <div class="batch-info">
                                    <i class="el-icon-info"></i>
                                    <span>已选中 <strong>{{ selectedOrders.length }}</strong> 个订单</span>
                                    <div style="margin-top: 4px; font-size: 11px; color: #909399;">
                                        {{ getBatchOperationHint() }}
                                    </div>
                                </div>
                                <div class="batch-buttons">
                                    <!-- 批量状态操作 -->
                                    <el-dropdown @command="handleBatchStatusUpdate" class="batch-dropdown">
                                        <el-button type="success" size="small" icon="el-icon-s-tools">
                                            批量处理<i class="el-icon-arrow-down el-icon--right"></i>
                                        </el-button>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item command="process">
                                                <i class="el-icon-s-tools"></i> 批量开始处理
                                            </el-dropdown-item>
                                            <el-dropdown-item command="complete">
                                                <i class="el-icon-check"></i> 批量完成
                                            </el-dropdown-item>
                                            <el-dropdown-item command="fail" divided>
                                                <i class="el-icon-close"></i> 批量标记失败
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>

                                    <!-- 批量退款操作 -->
                                    <el-dropdown @command="handleBatchRefund" class="batch-dropdown">
                                        <el-button type="warning" size="small" icon="el-icon-money">
                                            退款操作<i class="el-icon-arrow-down el-icon--right"></i>
                                        </el-button>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item command="approve">
                                                <i class="el-icon-check"></i> 批准退款
                                            </el-dropdown-item>
                                            <el-dropdown-item command="reject">
                                                <i class="el-icon-close"></i> 拒绝退款
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>

                                    <!-- 导出操作 -->
                                    <el-dropdown @command="handleBatchExport" class="batch-dropdown">
                                        <el-button type="primary" size="small" icon="el-icon-download">
                                            导出选中<i class="el-icon-arrow-down el-icon--right"></i>
                                        </el-button>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item command="csv">
                                                <i class="el-icon-document"></i> 导出CSV
                                            </el-dropdown-item>
                                            <el-dropdown-item command="excel">
                                                <i class="el-icon-s-grid"></i> 导出Excel
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>

                                    <!-- 危险操作 -->
                                    <el-button size="small" type="danger" @click="batchDeleteOrders"
                                        icon="el-icon-delete" class="batch-delete-btn">
                                        批量删除
                                    </el-button>

                                    <!-- 快速选择 -->
                                    <el-dropdown @command="handleQuickSelect" class="batch-dropdown">
                                        <el-button size="small" type="info" icon="el-icon-s-check">
                                            快速选择<i class="el-icon-arrow-down el-icon--right"></i>
                                        </el-button>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item command="pending">
                                                <i class="el-icon-time"></i> 选择待处理
                                            </el-dropdown-item>
                                            <el-dropdown-item command="processing">
                                                <i class="el-icon-loading"></i> 选择处理中
                                            </el-dropdown-item>
                                            <el-dropdown-item command="refund_requested">
                                                <i class="el-icon-money"></i> 选择申请退款
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>

                                    <!-- 清空选择 -->
                                    <el-button size="small" @click="clearSelection" icon="el-icon-close"
                                        type="info" plain>
                                        清空选择
                                    </el-button>
                                </div>
                            </div>
                        </div>

                        <!-- 订单列表 -->
                        <div class="table-container" style="overflow-x: auto; -webkit-overflow-scrolling: touch;">
                            <el-table
                                ref="orderTable"
                                :data="orders"
                                v-loading="loading"
                                stripe
                                border
                                style="width: 100%; min-width: 1000px;"
                                :header-cell-style="{background: '#f8f9fa', fontWeight: '600', textAlign: 'center'}"
                                :cell-style="{textAlign: 'center', verticalAlign: 'middle'}"
                                @selection-change="handleSelectionChange">

                            <el-table-column type="selection" width="55"></el-table-column>
                            <el-table-column prop="uid" label="UID" width="50"></el-table-column>
                            <el-table-column prop="order_id" label="订单ID" width="50"></el-table-column>
                            <el-table-column prop="order_no" label="订单号" width="150"></el-table-column>
                            <el-table-column label="客户信息" width="200" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <div class="customer-info-cell">
                                        <!-- 用户信息（管理员可见） -->
                                        <div v-if="scope.row.uid" style="background: #f0f9ff; padding: 2px 4px; margin-bottom: 3px; border-radius: 3px; border-left: 3px solid #409eff;">
                                            <div style="font-size: 10px; color: #409eff; font-weight: 600;">
                                                <i class="el-icon-user-solid" style="margin-right: 2px;"></i>
                                                用户ID: {{ scope.row.uid }}
                                                <span v-if="scope.row.username" style="margin-left: 4px;">{{ scope.row.username }}</span>
                                                <span v-if="scope.row.user_realname" style="margin-left: 4px;">({{ scope.row.user_realname }})</span>
                                            </div>
                                        </div>

                                        <!-- 客户姓名 -->
                                        <div class="customer-name">
                                            <i class="el-icon-user" style="color: #409EFF; margin-right: 4px; font-size: 12px;"></i>
                                            <span style="font-weight: 600; color: #303133;">{{ scope.row.customer_name }}</span>
                                        </div>

                                        <!-- 联系方式 -->
                                        <div v-if="scope.row.customer_phone || scope.row.customer_email"
                                             style="margin-top: 3px; font-size: 11px; color: #606266;">
                                            <div v-if="scope.row.customer_phone" style="margin-bottom: 1px;">
                                                <i class="el-icon-phone" style="margin-right: 3px; color: #67C23A;"></i>
                                                {{ scope.row.customer_phone }}
                                            </div>
                                            <div v-if="scope.row.customer_email" style="margin-bottom: 1px;">
                                                <i class="el-icon-message" style="margin-right: 3px; color: #E6A23C;"></i>
                                                {{ scope.row.customer_email }}
                                            </div>
                                        </div>

                                        <!-- 收货地址 -->
                                        <div v-if="scope.row.customer_address"
                                             style="margin-top: 4px; padding: 3px 6px; background: #f8f9fa; border-radius: 3px; border-left: 2px solid #909399;">
                                            <div style="font-size: 10px; color: #909399; margin-bottom: 1px;">
                                                <i class="el-icon-location" style="margin-right: 2px;"></i>收货地址
                                            </div>
                                            <div style="font-size: 10px; color: #606266; line-height: 1.3; word-break: break-all;">
                                                {{ scope.row.customer_address }}
                                            </div>
                                        </div>

                                        <!-- 快递信息 -->
                                        <div v-if="scope.row.courier_company || scope.row.tracking_number"
                                             style="margin-top: 3px; font-size: 10px; color: #E6A23C;">
                                            <i class="el-icon-truck" style="margin-right: 3px;"></i>
                                            <span v-if="scope.row.courier_company">{{ scope.row.courier_company }}</span>
                                            <span v-if="scope.row.tracking_number" style="margin-left: 4px;">{{ scope.row.tracking_number }}</span>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="公司信息" width="220" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <div class="company-info-cell">
                                        <!-- 主要公司 -->
                                        <div class="main-company">
                                            <i class="el-icon-office-building" style="color: #409EFF; margin-right: 4px;"></i>
                                            <span style="font-weight: 600; color: #303133;">{{ scope.row.company_name }}</span>
                                        </div>

                                        <!-- 仅营业执照模式 -->
                                        <div v-if="scope.row.only_business_license == '1'"
                                             style="margin-top: 4px;">
                                            <div style="display: flex; align-items: center;">
                                                <i class="el-icon-document" style="color: #F56C6C; margin-right: 4px; font-size: 12px;"></i>
                                                <el-tag size="mini" type="danger" style="font-size: 10px; font-weight: 600;">
                                                    仅营业执照服务
                                                </el-tag>
                                            </div>
                                            <!-- 显示营业执照公司详情 -->
                                            <div v-if="getLicenseCompanies(scope.row).length > 0"
                                                 style="margin-top: 3px; padding-left: 16px;">
                                                <div v-for="(company, index) in getLicenseCompanies(scope.row)"
                                                     :key="index"
                                                     style="font-size: 10px; color: #606266; line-height: 1.3;">
                                                    <span style="color: #303133; font-weight: 500;">{{ company.name }}</span>
                                                    <span style="color: #F56C6C; font-weight: 600; margin-left: 4px;">¥{{ company.price }}</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 普通公司+营业执照模式 -->
                                        <div v-else-if="scope.row.business_license == '1' && getLicenseCompanies(scope.row)"
                                             class="license-companies">
                                            <div style="display: flex; align-items: center; margin-top: 4px;">
                                                <i class="el-icon-document" style="color: #E6A23C; margin-right: 4px; font-size: 12px;"></i>
                                                <span style="font-size: 11px; color: #E6A23C; font-weight: 600;">附加营业执照:</span>
                                            </div>
                                            <div style="margin-top: 2px; padding-left: 16px;">
                                                <div v-for="(company, index) in getLicenseCompanies(scope.row)"
                                                     :key="index"
                                                     style="font-size: 11px; color: #606266; line-height: 1.3;">
                                                    <el-tag size="mini" type="warning" style="margin-right: 4px; margin-bottom: 2px;">
                                                        {{ company.name }}
                                                        <span style="color: #F56C6C; font-weight: 600;">¥{{ company.price }}</span>
                                                    </el-tag>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            
                            <el-table-column label="服务详情" width="160" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <div class="service-info-cell">
                                        <!-- 服务类型 -->
                                        <div style="margin-bottom: 4px;">
                                            <el-tag :type="getServiceTypeTag(scope.row.service_type)" size="mini">
                                                {{ getServiceTypeText(scope.row.service_type) }}
                                            </el-tag>
                                        </div>

                                        <!-- 材料方式 -->
                                        <div v-if="scope.row.material_type" style="margin-bottom: 3px; font-size: 10px; color: #606266;">
                                            <i class="el-icon-folder" style="margin-right: 3px; color: #909399;"></i>
                                            <span>{{ getMaterialTypeText(scope.row.material_type) }}</span>
                                        </div>

                                        <!-- 文件信息 -->
                                        <div v-if="scope.row.original_filename" style="margin-bottom: 3px; font-size: 10px; color: #606266;">
                                            <i class="el-icon-document" style="margin-right: 3px; color: #409EFF;"></i>
                                            <span style="word-break: break-all;">{{ scope.row.original_filename }}</span>
                                            <span v-if="scope.row.file_size" style="color: #909399; margin-left: 4px;">
                                                ({{ formatFileSize(scope.row.file_size) }})
                                            </span>
                                        </div>

                                        <!-- 打印信息 -->
                                        <div v-if="scope.row.print_copies > 0" style="margin-bottom: 2px; font-size: 10px; color: #E6A23C;">
                                            <i class="el-icon-printer" style="margin-right: 3px;"></i>
                                            <span>{{ scope.row.print_copies }}份</span>
                                        </div>

                                        <!-- 特殊要求 -->
                                        <div v-if="scope.row.special_requirements && !scope.row.special_requirements.includes('营业执照公司:')"
                                             style="margin-top: 3px; padding: 2px 4px; background: #fff7e6; border-radius: 2px; font-size: 9px; color: #E6A23C;">
                                            <i class="el-icon-warning" style="margin-right: 2px;"></i>
                                            {{ getCleanSpecialRequirements(scope.row.special_requirements) }}
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>

                            <el-table-column prop="status" label="状态" width="100">
                                <template slot-scope="scope">
                                    <el-tag :type="getStatusTag(scope.row.status)">
                                        {{ getStatusText(scope.row.status) }}
                                    </el-tag>
                                </template>
                            </el-table-column>

                            <el-table-column prop="total_price" label="金额" width="90">
                                <template slot-scope="scope">
                                    ¥{{ scope.row.total_price }}
                                </template>
                            </el-table-column>

                            <el-table-column prop="created_at" label="创建时间" width="160">
                                <template slot-scope="scope">
                                    {{ formatDate(scope.row.created_at) }}
                                </template>
                            </el-table-column>

                            <el-table-column label="备注信息" width="180" show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <div class="notes-info-cell">
                                        <!-- 管理员备注 -->
                                        <div v-if="scope.row.admin_notes" style="margin-bottom: 3px;">
                                            <div style="font-size: 10px; color: var(--danger-color); margin-bottom: 1px;">
                                                <i class="el-icon-edit" style="margin-right: 2px;"></i>管理员备注
                                            </div>
                                            <div style="font-size: 11px; color: var(--text-secondary); line-height: 1.4; padding: 4px 6px; background: var(--bg-light); border-radius: 3px; border-left: 2px solid var(--danger-color); white-space: pre-wrap; word-wrap: break-word; word-break: break-word; max-width: 100%; overflow-wrap: break-word;">
                                                {{ scope.row.admin_notes }}
                                            </div>
                                        </div>

                                        <!-- 客户备注 -->
                                        <div v-if="getCleanSpecialRequirements(scope.row.special_requirements)" style="margin-bottom: 3px;">
                                            <div style="font-size: 10px; color: var(--warning-color); margin-bottom: 1px;">
                                                <i class="el-icon-chat-line-square" style="margin-right: 2px;"></i>客户备注
                                            </div>
                                            <div style="font-size: 11px; color: var(--text-secondary); line-height: 1.4; padding: 4px 6px; background: var(--bg-light); border-radius: 3px; border-left: 2px solid var(--warning-color); white-space: pre-wrap; word-wrap: break-word; word-break: break-word; max-width: 100%; overflow-wrap: break-word;">
                                                {{ getCleanSpecialRequirements(scope.row.special_requirements) }}
                                            </div>
                                        </div>

                                        <!-- 无备注提示 -->
                                        <div v-if="!scope.row.admin_notes && !getCleanSpecialRequirements(scope.row.special_requirements)"
                                             style="font-size: 11px; color: #C0C4CC; text-align: center; padding: 8px 0;">
                                            <i class="el-icon-document" style="margin-right: 4px;"></i>暂无备注
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>

                            <el-table-column label="操作" width="150" fixed="right">
                                <template slot-scope="scope">
                                    <div class="action-buttons-container">
                                        <!-- 主要操作按钮 -->
                                        <div class="primary-actions">
                                            <el-button size="small" @click="viewOrder(scope.row)" icon="el-icon-view">
                                                详情
                                            </el-button>

                                            <!-- 待处理状态 -->
                                            <el-button size="small" type="primary" @click="processOrder(scope.row)"
                                                v-if="scope.row.status === 'pending'" icon="el-icon-s-tools">
                                                处理
                                            </el-button>

                                            <!-- 处理中状态 -->
                                            <template v-if="scope.row.status === 'processing'">
                                                <el-button size="small" type="success" @click="completeOrder(scope.row)" icon="el-icon-check">
                                                    完成
                                                </el-button>
                                                <el-button size="small" type="warning" @click="failOrder(scope.row)" icon="el-icon-close">
                                                    失败
                                                </el-button>
                                            </template>

                                            <!-- 退款申请状态 -->
                                            <template v-if="scope.row.status === 'refund_requested'">
                                                <el-button size="small" type="success" @click="approveRefund(scope.row)" icon="el-icon-check">
                                                    批准退款
                                                </el-button>
                                                <el-button size="small" type="warning" @click="rejectRefund(scope.row)" icon="el-icon-close">
                                                    拒绝退款
                                                </el-button>
                                            </template>
                                        </div>

                                        <!-- 更多操作下拉菜单 -->
                                        <el-dropdown @command="handleOrderAction" trigger="click" class="more-actions">
                                            <el-button size="small" type="info">
                                                更多<i class="el-icon-arrow-down el-icon--right"></i>
                                            </el-button>
                                            <el-dropdown-menu slot="dropdown">
                                                <el-dropdown-item :command="{action: 'delete', order: scope.row}"
                                                    style="color: #f56c6c;">
                                                    <i class="el-icon-delete"></i> 删除订单
                                                </el-dropdown-item>
                                                <el-dropdown-item :command="{action: 'export', order: scope.row}">
                                                    <i class="el-icon-download"></i> 导出数据
                                                </el-dropdown-item>
                                            </el-dropdown-menu>
                                        </el-dropdown>
                                    </div>
                                </template>
                            </el-table-column>
                            </el-table>
                        </div>

                        <!-- 分页 -->
                        <div class="mt-3 text-center">
                            <el-pagination
                                @current-change="handlePageChange"
                                :current-page="currentPage"
                                :page-size="pageSize"
                                :total="total"
                                layout="total, prev, pager, next, jumper">
                            </el-pagination>
                        </div>
                    </el-tab-pane>





                    <!-- 公司管理 -->
                    <el-tab-pane label="公司管理" name="companies">
                        <div class="company-management">

                            <!-- 搜索筛选 -->
                            <div class="search-wrapper" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                                <el-row :gutter="15">
                                    <el-col :span="16">
                                        <el-input
                                            v-model="companySearchForm.search"
                                            placeholder="搜索公司名称或内容"
                                            clearable
                                            @keyup.enter.native="searchCompanies">
                                            <el-button slot="append" icon="el-icon-search" @click="searchCompanies"></el-button>
                                        </el-input>
                                    </el-col>
                                    <el-col :span="12" style="text-align: right;">
                                        <el-button type="primary" size="small" @click="showAddCompanyDialog" icon="el-icon-plus">添加公司</el-button>
                                        <el-button type="success" size="small" @click="loadCompanies" icon="el-icon-refresh">刷新</el-button>
                                    </el-col>
                                </el-row>
                            </div>

                            <!-- 公司列表 -->
                            <div class="table-container" style="overflow-x: auto; -webkit-overflow-scrolling: touch;">
                                <el-table
                                    :data="companies"
                                    stripe
                                    border
                                    v-loading="companyLoading"
                                    style="width: 100%; min-width: 800px;">

                                <el-table-column prop="cid" label="ID" width="80"></el-table-column>
                                <el-table-column prop="name" label="公司名称" width="200"></el-table-column>
                                <el-table-column prop="price" label="价格" width="100">
                                    <template slot-scope="scope">
                                        ¥{{ scope.row.price }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="content" label="描述" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="status" label="状态" width="80">
                                    <template slot-scope="scope">
                                        <el-tag :type="scope.row.status == 1 ? 'success' : 'danger'">
                                            {{ scope.row.status == 1 ? '启用' : '禁用' }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="addtime" label="添加时间" width="150"></el-table-column>
                                <el-table-column label="操作" width="200" fixed="right">
                                    <template slot-scope="scope">
                                        <el-button size="mini" type="primary" @click="editCompany(scope.row)">编辑</el-button>
                                        <el-button size="mini" type="danger" @click="deleteCompany(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                                </el-table>
                            </div>

                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;">
                                <el-pagination
                                    @current-change="handleCompanyPageChange"
                                    :current-page="companyCurrentPage"
                                    :page-size="companyPageSize"
                                    layout="total, prev, pager, next, jumper"
                                    :total="companyTotal">
                                </el-pagination>
                            </div>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </div>

    <!-- 订单详情对话框 -->
    <el-dialog
        title="订单详情"
        :visible.sync="detailDialogVisible"
        width="80%"
        :before-close="handleDetailClose">

        <div v-if="currentOrder">
            <!-- 订单概览卡片 -->
            <el-card class="order-overview-card" style="margin-bottom: 20px;">
                <div slot="header" class="clearfix">
                    <span style="font-weight: 600; color: var(--text-primary);">
                        <i class="el-icon-document" style="margin-right: 8px; color: var(--primary-color);"></i>
                        订单概览 - {{ currentOrder.order_no }}
                    </span>
                    <el-tag :type="getStatusTag(currentOrder.status)" style="float: right;">
                        {{ getStatusText(currentOrder.status) }}
                    </el-tag>
                </div>

                <el-row :gutter="20">
                    <!-- 用户信息 -->
                    <el-col :span="8">
                        <div class="info-section">
                            <h5 style="margin: 0 0 10px 0; color: var(--primary-color); border-bottom: 1px solid var(--border-color); padding-bottom: 5px;">
                                <i class="el-icon-user-solid"></i> 用户信息
                            </h5>
                            <div v-if="currentOrder.uid" style="margin-bottom: 8px; padding: 6px; background: var(--bg-light); border-radius: 4px; border-left: 3px solid var(--primary-color);">
                                <div style="font-size: 12px; color: var(--primary-color); font-weight: 600;">
                                    用户ID: {{ currentOrder.uid }}
                                </div>
                                <div v-if="currentOrder.username" style="font-size: 11px; color: var(--text-secondary);">
                                    用户名: {{ currentOrder.username }}
                                </div>
                                <div v-if="currentOrder.user_realname" style="font-size: 11px; color: var(--text-secondary);">
                                    真实姓名: {{ currentOrder.user_realname }}
                                </div>
                            </div>
                            <div style="font-size: 13px; line-height: 1.6;">
                                <div><strong>客户姓名:</strong> {{ currentOrder.customer_name }}</div>
                                <div><strong>联系电话:</strong> {{ currentOrder.customer_phone || '无' }}</div>
                                <div><strong>邮箱地址:</strong> {{ currentOrder.customer_email || '无' }}</div>
                            </div>
                        </div>
                    </el-col>

                    <!-- 订单信息 -->
                    <el-col :span="8">
                        <div class="info-section">
                            <h5 style="margin: 0 0 10px 0; color: var(--secondary-color); border-bottom: 1px solid var(--border-color); padding-bottom: 5px;">
                                <i class="el-icon-s-order"></i> 订单信息
                            </h5>
                            <div style="font-size: 13px; line-height: 1.6;">
                                <div><strong>服务类型:</strong> {{ getServiceTypeText(currentOrder.service_type) }}</div>
                                <div><strong>材料方式:</strong> {{ getMaterialTypeText(currentOrder.material_type) }}</div>
                                <div><strong>公司名称:</strong> {{ currentOrder.company_name }}</div>
                                <div v-if="currentOrder.business_license == '1'">
                                    <strong>营业执照:</strong>
                                    <el-tag size="mini" :type="currentOrder.only_business_license == '1' ? 'danger' : 'warning'">
                                        {{ currentOrder.only_business_license == '1' ? '仅营业执照' : '附加营业执照' }}
                                    </el-tag>
                                </div>
                                <div v-if="currentOrder.print_copies > 0">
                                    <strong>打印份数:</strong> {{ currentOrder.print_copies }}份
                                </div>
                                <div><strong>创建时间:</strong> {{ formatDate(currentOrder.created_at) }}</div>
                            </div>
                        </div>
                    </el-col>

                    <!-- 价格信息 -->
                    <el-col :span="8">
                        <div class="info-section">
                            <h5 style="margin: 0 0 10px 0; color: var(--success-color); border-bottom: 1px solid var(--border-color); padding-bottom: 5px;">
                                <i class="el-icon-money"></i> 价格明细
                            </h5>
                            <div style="font-size: 13px; line-height: 1.6;">
                                <div><strong>基础价格:</strong> ¥{{ currentOrder.base_price || 0 }}</div>
                                <div v-if="currentOrder.print_price > 0"><strong>打印费用:</strong> ¥{{ currentOrder.print_price }}</div>
                                <div v-if="currentOrder.license_price > 0"><strong>营业执照费用:</strong> ¥{{ currentOrder.license_price }}</div>
                                <div style="border-top: 1px solid #DCDFE6; margin-top: 8px; padding-top: 8px;">
                                    <strong style="color: #F56C6C; font-size: 16px;">总金额: ¥{{ currentOrder.total_price }}</strong>
                                </div>
                            </div>
                        </div>
                    </el-col>
                </el-row>

                <!-- 收货地址 -->
                <div v-if="currentOrder.customer_address" style="margin-top: 15px; padding: 10px; background: var(--bg-light); border-radius: 4px; border-left: 3px solid var(--text-secondary);">
                    <h6 style="margin: 0 0 5px 0; color: var(--text-secondary);">
                        <i class="el-icon-location"></i> 收货地址
                    </h6>
                    <div style="font-size: 13px; color: var(--text-secondary);">{{ currentOrder.customer_address }}</div>
                </div>

                <!-- 快递信息 -->
                <div v-if="currentOrder.courier_company || currentOrder.tracking_number"
                     style="margin-top: 10px; padding: 10px; background: var(--bg-light); border-radius: 4px; border-left: 3px solid var(--accent-color);">
                    <h6 style="margin: 0 0 5px 0; color: var(--accent-color);">
                        <i class="el-icon-truck"></i> 快递信息
                    </h6>
                    <div style="font-size: 13px; color: var(--text-secondary);">
                        <span v-if="currentOrder.courier_company">快递公司: {{ currentOrder.courier_company }}</span>
                        <span v-if="currentOrder.tracking_number" style="margin-left: 15px;">运单号: {{ currentOrder.tracking_number }}</span>
                    </div>
                </div>

                <!-- 客户备注 -->
                <div v-if="currentOrder.special_requirements"
                     style="margin-top: 10px; padding: 10px; background: var(--bg-light); border-radius: 4px; border-left: 3px solid var(--warning-color);">
                    <h6 style="margin: 0 0 5px 0; color: var(--warning-color);">
                        <i class="el-icon-chat-line-square"></i> 客户备注
                    </h6>
                    <div style="font-size: 13px; color: var(--text-secondary); white-space: pre-wrap; word-wrap: break-word; word-break: break-word; overflow-wrap: break-word;">
                        {{ currentOrder.special_requirements }}
                    </div>
                </div>
            </el-card>

            <!-- 管理员备注区域 -->
            <el-row style="margin-bottom: 20px;">
                <el-col :span="24">
                    <el-card class="notes-card">
                        <div slot="header" class="clearfix">
                            <span style="font-weight: 600; color: var(--danger-color);">
                                <i class="el-icon-edit"></i> 管理员备注
                            </span>
                            <span style="float: right; font-size: 12px; color: var(--text-muted);">
                                最后更新: {{ currentOrder.updated_at ? formatDate(currentOrder.updated_at) : '未更新' }}
                            </span>
                        </div>

                        <div>
                            <!-- 显示现有备注 -->
                            <div v-if="currentOrder.admin_notes && currentOrder.admin_notes !== adminNotes"
                                style="margin-bottom: 15px; padding: 10px; background: var(--bg-light); border-radius: 4px; border-left: 3px solid var(--danger-color);">
                                <h6 style="margin: 0 0 5px 0; color: var(--danger-color);">当前备注:</h6>
                                <div style="font-size: 13px; color: var(--text-secondary); white-space: pre-wrap; word-wrap: break-word; word-break: break-word; overflow-wrap: break-word;">{{ currentOrder.admin_notes }}</div>
                            </div>

                            <!-- 编辑备注 -->
                            <el-input
                                type="textarea"
                                v-model="adminNotes"
                                placeholder="请输入处理备注..."
                                :rows="3"
                                show-word-limit
                                maxlength="500">
                            </el-input>

                            <div style="margin-top: 10px; font-size: 12px; color: var(--text-muted);">
                                <i class="el-icon-info"></i>
                                备注将记录在订单历史中，客户可能会看到相关信息
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>

            <!-- 文件管理区域 -->
            <el-row>
                <el-col :span="24">
                    <el-card class="file-management-card">
                        <div slot="header" class="clearfix">
                            <span style="font-weight: 600; color: var(--primary-color);">
                                <i class="el-icon-folder-opened"></i> 文件管理
                            </span>
                        </div>

                        <div v-if="orderFiles">
                            <el-row :gutter="20">
                                <!-- 客户上传文件 -->
                                <el-col :span="8">
                                    <div class="file-section">
                                        <h6 style="margin: 0 0 15px 0; color: var(--success-color); border-bottom: 2px solid var(--success-color); padding-bottom: 8px; font-size: 14px;">
                                            <i class="el-icon-upload2"></i> 客户上传文件
                                        </h6>
                                        <div v-if="orderFiles.uploads && orderFiles.uploads.length > 0">
                                            <div v-for="file in orderFiles.uploads" :key="file.name"
                                                 style="display: flex; align-items: center; padding: 10px; margin-bottom: 10px; background: var(--bg-light); border-radius: 6px; border-left: 4px solid var(--success-color); transition: all 0.2s ease;">
                                                <i class="el-icon-document" style="color: var(--success-color); margin-right: 10px; font-size: 18px;"></i>
                                                <div style="flex: 1; min-width: 0;">
                                                    <div style="font-weight: 600; font-size: 13px; color: var(--text-primary); margin-bottom: 4px; word-break: break-all;">
                                                        {{ file.original_name || file.name }}
                                                    </div>
                                                    <div style="font-size: 11px; color: var(--text-muted); line-height: 1.4;">
                                                        {{ formatFileSize(file.size) }}
                                                        <span v-if="file.file_type"> | {{ file.file_type }}</span>
                                                        <div v-if="file.is_database || file.is_legacy" style="margin-top: 2px;">
                                                            <span v-if="file.is_database" style="color: var(--accent-color); font-size: 10px;">数据库记录</span>
                                                            <span v-if="file.is_legacy" style="color: var(--warning-color); font-size: 10px;">旧格式</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div style="display: flex; flex-direction: column; gap: 4px; margin-left: 8px;">
                                                    <el-button v-if="file.can_preview" size="mini" type="text" @click="previewFile(file.preview_url)" icon="el-icon-view" style="padding: 2px 4px; font-size: 11px;">预览</el-button>
                                                    <el-button size="mini" type="text" @click="downloadFile(file.download_url)" icon="el-icon-download" style="padding: 2px 4px; font-size: 11px;">下载</el-button>
                                                    <el-button size="mini" type="text" style="color: var(--danger-color); padding: 2px 4px; font-size: 11px;" @click="deleteFile(file.name, 'upload')" icon="el-icon-delete">删除</el-button>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else style="text-align: center; color: var(--text-muted); padding: 30px 10px;">
                                            <i class="el-icon-document" style="font-size: 32px; margin-bottom: 10px; display: block;"></i>
                                            <div style="font-size: 13px;">暂无上传文件</div>
                                        </div>
                                    </div>
                                </el-col>

                                <!-- 已处理文件 -->
                                <el-col :span="8">
                                    <div class="file-section">
                                        <h6 style="margin: 0 0 15px 0; color: var(--accent-color); border-bottom: 2px solid var(--accent-color); padding-bottom: 8px; font-size: 14px;">
                                            <i class="el-icon-check"></i> 已处理文件
                                        </h6>
                                        <div v-if="orderFiles.processed && orderFiles.processed.length > 0">
                                            <div v-for="file in orderFiles.processed" :key="file.name"
                                                 style="display: flex; align-items: center; padding: 10px; margin-bottom: 10px; background: var(--bg-light); border-radius: 6px; border-left: 4px solid var(--accent-color); transition: all 0.2s ease;">
                                                <i class="el-icon-document-checked" style="color: var(--accent-color); margin-right: 10px; font-size: 18px;"></i>
                                                <div style="flex: 1; min-width: 0;">
                                                    <div style="font-weight: 600; font-size: 13px; color: var(--text-primary); margin-bottom: 4px; word-break: break-all;">
                                                        {{ file.original_name || file.name }}
                                                    </div>
                                                    <div style="font-size: 11px; color: var(--text-muted); line-height: 1.4;">
                                                        {{ formatFileSize(file.size) }}
                                                        <span v-if="file.file_type"> | {{ file.file_type }}</span>
                                                        <div v-if="file.is_legacy" style="margin-top: 2px;">
                                                            <span style="color: var(--warning-color); font-size: 10px;">旧格式</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div style="display: flex; flex-direction: column; gap: 4px; margin-left: 8px;">
                                                    <el-button v-if="file.can_preview" size="mini" type="text" @click="previewFile(file.preview_url)" icon="el-icon-view" style="padding: 2px 4px; font-size: 11px;">预览</el-button>
                                                    <el-button size="mini" type="text" @click="downloadFile(file.download_url)" icon="el-icon-download" style="padding: 2px 4px; font-size: 11px;">下载</el-button>
                                                    <el-button size="mini" type="text" style="color: var(--danger-color); padding: 2px 4px; font-size: 11px;" @click="deleteFile(file.name, 'processed')" icon="el-icon-delete">删除</el-button>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else style="text-align: center; color: var(--text-muted); padding: 30px 10px;">
                                            <i class="el-icon-document-checked" style="font-size: 32px; margin-bottom: 10px; display: block;"></i>
                                            <div style="font-size: 13px;">暂无已处理文件</div>
                                        </div>
                                    </div>
                                </el-col>

                                <!-- 上传处理后文件 -->
                                <el-col :span="8">
                                    <div class="file-section">
                                        <h6 style="margin: 0 0 15px 0; color: var(--secondary-color); border-bottom: 2px solid var(--secondary-color); padding-bottom: 8px; font-size: 14px;">
                                            <i class="el-icon-upload"></i> 上传处理后文件
                                        </h6>
                                        <el-upload
                                            :action="'/sxgz/upload_processed.php'"
                                            :data="{ order_id: currentOrder.order_id }"
                                            :on-success="handleProcessedUploadSuccess"
                                            :on-error="handleProcessedUploadError"
                                            :show-file-list="true"
                                            drag
                                            multiple
                                            style="width: 100%;">
                                            <i class="el-icon-upload" style="font-size: 32px; color: var(--text-muted);"></i>
                                            <div class="el-upload__text" style="color: var(--text-secondary); font-size: 13px; margin-top: 8px;">
                                                将文件拖到此处<br><em>或点击上传</em>
                                            </div>
                                            <div slot="tip" class="el-upload__tip" style="color: var(--text-muted); font-size: 11px; margin-top: 8px;">
                                                支持多文件上传，上传处理后的文件供客户下载
                                            </div>
                                        </el-upload>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </el-card>
                </el-col>
            </el-row>


        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="detailDialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="updateOrderStatus" v-if="currentOrder && currentOrder.status !== 'completed'">
                保存备注
            </el-button>
            <el-button type="success" @click="completeOrder(currentOrder)" v-if="currentOrder && currentOrder.status !== 'completed'" icon="el-icon-check">
                完成订单
            </el-button>
        </div>
    </el-dialog>

    <!-- 公司编辑对话框 -->
    <el-dialog
        :title="companyDialogTitle"
        :visible.sync="companyDialogVisible"
        width="600px"
        :before-close="handleCompanyDialogClose">

        <el-form :model="companyForm" :rules="companyRules" ref="companyForm" label-width="100px">
            <el-form-item label="公司名称" prop="name">
                <el-input v-model="companyForm.name" placeholder="请输入公司名称"></el-input>
            </el-form-item>
            <el-form-item label="价格" prop="price">
                <el-input-number v-model="companyForm.price" :min="0" :precision="2" placeholder="请输入价格"></el-input-number>
            </el-form-item>
            <el-form-item label="描述" prop="content">
                <el-input type="textarea" v-model="companyForm.content" :rows="3" placeholder="请输入公司描述"></el-input>
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-radio-group v-model="companyForm.status">
                    <el-radio :label="1">启用</el-radio>
                    <el-radio :label="0">禁用</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button @click="companyDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="saveCompany" :loading="companySaving">保存</el-button>
        </div>
    </el-dialog>
</div>

<script src="/sxgz/element/vue.min.js"></script>
<script src="/sxgz/element/vue-resource.min.js"></script>
<script src="/sxgz/element/element.js"></script>
<script src="/sxgz/element/jquery.min.js"></script>

<script>
var sxgzAdmin = new Vue({
    el: '#sxgzAdmin',
    data: {
        activeTab: 'orders',
        loading: false,

        // 统计数据
        statistics: {
            total_orders: 0,
            processing_orders: 0,
            pending_orders: 0,
            completed_orders: 0,
            refund_requests: 0,
            today_orders: 0,
            total_revenue: 0,
            active_users: 0
        },

        // 订单相关
        orders: [],
        selectedOrders: [], // 选中的订单
        currentPage: 1,
        pageSize: 15,
        total: 0,
        searchForm: {
            search: '',
            status: '',
            service_type: '',
            dateRange: []
        },







        // 订单详情
        detailDialogVisible: false,
        currentOrder: null,
        orderFiles: null,
        adminNotes: '',

        // 公司管理
        companies: [],
        companyLoading: false,
        companyCurrentPage: 1,
        companyPageSize: 20,
        companyTotal: 0,
        companySearchForm: {
            search: ''
        },

        // 公司编辑对话框
        companyDialogVisible: false,
        companyDialogTitle: '添加公司',
        companyForm: {
            name: '',
            price: 0,
            content: '',
            fenlei: 25709,
            status: 1
        },
        companyRules: {
            name: [
                { required: true, message: '请输入公司名称', trigger: 'blur' }
            ],
            price: [
                { required: true, message: '请输入价格', trigger: 'blur' },
                { type: 'number', min: 0, message: '价格必须大于等于0', trigger: 'blur' }
            ]
        },
        companySaving: false
    },
    
    mounted() {
        this.loadOrders();
        this.loadStatistics();
    },

    methods: {
        // 标签页切换
        handleTabClick(tab) {
            if (tab.name === 'orders') {
                this.loadOrders();
                this.loadStatistics(); // 切换到订单管理时刷新统计
            } else if (tab.name === 'companies') {
                this.loadCompanies();
            }
        },


        // ==================== 订单管理方法 ====================

        // 加载订单列表
        loadOrders() {
            this.loading = true;

            let params = {
                page: this.currentPage,
                limit: this.pageSize
            };

            // 添加搜索条件
            if (this.searchForm.search) {
                params.search = this.searchForm.search;
            }
            if (this.searchForm.status) {
                params.status = this.searchForm.status;
            }
            if (this.searchForm.service_type) {
                params.service_type = this.searchForm.service_type;
            }
            if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
                params.date_from = this.searchForm.dateRange[0];
                params.date_to = this.searchForm.dateRange[1];
            }

            this.$http.get('/sxgz/api.php?action=admin_orders', { params: params })
                .then(response => {
                    this.loading = false;
                    if (response.data.success) {
                        this.orders = response.data.data.orders;
                        this.total = parseInt(response.data.data.total);
                        this.currentPage = parseInt(response.data.data.page);
                    } else {
                        this.$message.error(response.data.message || '加载订单失败');
                    }
                })
                .catch(() => {
                    this.loading = false;
                    this.$message.error('加载订单失败');
                });
        },

        // 搜索订单
        searchOrders() {
            this.currentPage = 1;
            this.loadOrders();
        },

        // 重置搜索
        resetSearch() {
            this.searchForm = {
                search: '',
                status: '',
                service_type: '',
                dateRange: []
            };
            this.currentPage = 1;
            this.loadOrders();
        },

        // 分页处理
        handlePageChange(page) {
            this.currentPage = page;
            this.loadOrders();
        },

        // 查看订单详情
        viewOrder(order) {
            this.currentOrder = order;
            this.adminNotes = order.admin_notes || '';
            this.detailDialogVisible = true;
            this.loadOrderDetails(order.order_id);
        },

        // 处理订单
        processOrder(order) {
            this.$confirm('确认将订单状态更改为"处理中"？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.updateOrderStatusApi(order.order_id, 'processing');
            });
        },

        // 完成订单
        completeOrder(order) {
            this.$confirm('确认完成此订单？完成后将自动发送邮件通知客户。', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.updateOrderStatusApi(order.order_id, 'completed');
                // 如果是在详情弹窗中完成订单，刷新详情页面
                if (this.detailDialogVisible && this.currentOrder && this.currentOrder.order_id === order.order_id) {
                    setTimeout(() => {
                        this.loadOrderDetails(order.order_id);
                    }, 1000);
                }
            });
        },

        // 处理失败并自动退款
        failOrder(order) {
            this.$confirm('确认将此订单标记为处理失败？系统将自动退款并通知客户。', '处理失败确认', {
                confirmButtonText: '确认失败',
                cancelButtonText: '取消',
                type: 'error',
                dangerouslyUseHTMLString: true,
                message: `<div>
                    <p><strong>订单信息：</strong></p>
                    <p>订单号：${order.order_no}</p>
                    <p>客户：${order.customer_name}</p>
                    <p>金额：¥${order.total_price}</p>
                    <br>
                    <p style="color: #E6A23C;"><strong>注意：此操作将：</strong></p>
                    <p>1. 将订单状态更新为"处理失败"</p>
                    <p>2. 自动退款到客户余额</p>
                    <p>3. 添加系统备注说明</p>
                </div>`
            }).then(() => {
                this.processFailedOrder(order);
            });
        },

        // 执行处理失败操作
        processFailedOrder(order) {
            const loading = this.$loading({
                lock: true,
                text: '处理中...'
            });

            this.$http.post('/sxgz/api.php?action=process_failed_order', {
                order_id: order.order_id
            }).then(response => {
                loading.close();
                if (response.data.success) {
                    this.$message.success('订单已标记为处理失败，退款已完成');
                    this.loadOrders();
                    this.loadStatistics(); // 刷新统计数据
                } else {
                    this.$message.error(response.data.message || '操作失败');
                }
            }).catch(() => {
                loading.close();
                this.$message.error('操作失败');
            });
        },

        // 批准退款
        approveRefund(order) {
            this.$prompt('请输入处理备注（可选）', '批准退款', {
                confirmButtonText: '批准退款',
                cancelButtonText: '取消',
                inputType: 'textarea',
                inputPlaceholder: '请输入处理备注...'
            }).then(({ value }) => {
                this.processRefundApi(order.order_id, 'approve', value || '');
            });
        },

        // 拒绝退款
        rejectRefund(order) {
            this.$prompt('请输入拒绝原因', '拒绝退款', {
                confirmButtonText: '拒绝退款',
                cancelButtonText: '取消',
                inputType: 'textarea',
                inputPlaceholder: '请输入拒绝原因...',
                inputValidator: (value) => {
                    if (!value || value.trim().length < 5) {
                        return '拒绝原因至少需要5个字符';
                    }
                    return true;
                }
            }).then(({ value }) => {
                this.processRefundApi(order.order_id, 'reject', value.trim());
            });
        },

        // 处理退款API
        processRefundApi(orderId, action, notes) {
            const loading = this.$loading({
                lock: true,
                text: action === 'approve' ? '处理退款中...' : '拒绝退款中...'
            });

            this.$http.post('/sxgz/api.php?action=process_refund', {
                order_id: orderId,
                action: action,
                admin_notes: notes
            }).then(response => {
                loading.close();
                if (response.data.success) {
                    this.$message.success(response.data.message);
                    this.loadOrders(); // 刷新列表
                    this.loadStatistics(); // 刷新统计数据
                } else {
                    this.$message.error(response.data.message || '处理失败');
                }
            }).catch(error => {
                loading.close();
                console.error('处理退款失败:', error);
                this.$message.error('处理失败，请检查网络连接');
            });
        },

        // 删除订单
        deleteOrder(order) {
            this.$confirm(`确认删除订单 ${order.order_no}？此操作将永久删除订单及其相关文件，不可恢复。`, '危险操作', {
                confirmButtonText: '确认删除',
                cancelButtonText: '取消',
                type: 'error',
                dangerouslyUseHTMLString: false
            }).then(() => {
                this.deleteOrderApi(order.order_id);
            });
        },

        // 批量删除订单
        batchDeleteOrders() {
            if (this.selectedOrders.length === 0) {
                this.$message.warning('请先选择要删除的订单');
                return;
            }

            const orderCount = this.selectedOrders.length;
            const orderNos = this.selectedOrders.map(order => order.order_no).join('、');

            this.$confirm(`确认删除选中的 ${orderCount} 个订单？\n订单号：${orderNos}\n\n此操作将永久删除订单及其相关文件，不可恢复。`, '批量删除确认', {
                confirmButtonText: '确认删除',
                cancelButtonText: '取消',
                type: 'error',
                dangerouslyUseHTMLString: false
            }).then(() => {
                this.batchDeleteOrdersApi();
            });
        },

        // 删除订单API
        deleteOrderApi(orderId) {
            const loading = this.$loading({
                lock: true,
                text: '删除中...'
            });

            this.$http.post('/sxgz/api.php?action=delete_order', {
                order_id: orderId
            }).then(response => {
                loading.close();
                if (response.data.success) {
                    this.$message.success('订单删除成功');
                    this.loadOrders(); // 刷新列表
                    this.loadStatistics(); // 刷新统计数据
                } else {
                    this.$message.error(response.data.message || '删除失败');
                }
            }).catch(error => {
                loading.close();
                console.error('删除订单失败:', error);
                this.$message.error('删除失败，请检查网络连接');
            });
        },

        // 批量删除订单API
        batchDeleteOrdersApi() {
            const orderIds = this.selectedOrders.map(order => order.order_id);
            const loading = this.$loading({
                lock: true,
                text: `删除中... (0/${orderIds.length})`
            });

            // 使用Promise.all并发删除，但限制并发数量
            const batchSize = 3; // 每批处理3个
            const batches = [];

            for (let i = 0; i < orderIds.length; i += batchSize) {
                batches.push(orderIds.slice(i, i + batchSize));
            }

            let completedCount = 0;
            let failedCount = 0;
            const failedOrders = [];

            const processBatch = async (batch, batchIndex) => {
                const promises = batch.map(orderId =>
                    this.$http.post('/sxgz/api.php?action=delete_order', {
                        order_id: orderId
                    }).then(response => {
                        completedCount++;
                        loading.setText(`删除中... (${completedCount}/${orderIds.length})`);
                        if (!response.data.success) {
                            failedCount++;
                            failedOrders.push(orderId);
                        }
                        return response;
                    }).catch(error => {
                        completedCount++;
                        failedCount++;
                        failedOrders.push(orderId);
                        loading.setText(`删除中... (${completedCount}/${orderIds.length})`);
                        return { data: { success: false } };
                    })
                );

                return Promise.all(promises);
            };

            // 顺序处理每个批次
            const processAllBatches = async () => {
                for (let i = 0; i < batches.length; i++) {
                    await processBatch(batches[i], i);
                }
            };

            processAllBatches().then(() => {
                loading.close();

                const successCount = orderIds.length - failedCount;
                if (failedCount === 0) {
                    this.$message.success(`成功删除 ${successCount} 个订单`);
                } else if (successCount === 0) {
                    this.$message.error(`删除失败，共 ${failedCount} 个订单删除失败`);
                } else {
                    this.$message.warning(`部分删除成功：成功 ${successCount} 个，失败 ${failedCount} 个`);
                }

                this.selectedOrders = []; // 清空选择
                this.loadOrders(); // 刷新列表
                this.loadStatistics(); // 刷新统计数据
            });
        },

        // 处理批量状态更新
        handleBatchStatusUpdate(command) {
            if (this.selectedOrders.length === 0) {
                this.$message.warning('请先选择要操作的订单');
                return;
            }

            const orderCount = this.selectedOrders.length;
            let actionText = '';
            let confirmText = '';
            let statusValue = '';

            switch (command) {
                case 'process':
                    actionText = '开始处理';
                    confirmText = `确认将选中的 ${orderCount} 个订单状态更改为"处理中"？`;
                    statusValue = 'processing';
                    break;
                case 'complete':
                    actionText = '完成';
                    confirmText = `确认将选中的 ${orderCount} 个订单标记为"已完成"？`;
                    statusValue = 'completed';
                    break;
                case 'fail':
                    actionText = '标记失败';
                    confirmText = `确认将选中的 ${orderCount} 个订单标记为"处理失败"？`;
                    statusValue = 'failed';
                    break;
                default:
                    this.$message.error('未知的操作类型');
                    return;
            }

            this.$confirm(confirmText, `批量${actionText}确认`, {
                confirmButtonText: `确认${actionText}`,
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.batchUpdateStatusApi(statusValue, actionText);
            });
        },

        // 处理批量退款操作
        handleBatchRefund(command) {
            if (this.selectedOrders.length === 0) {
                this.$message.warning('请先选择要操作的订单');
                return;
            }

            // 过滤出符合条件的订单
            let validOrders = [];
            if (command === 'approve') {
                validOrders = this.selectedOrders.filter(order => order.status === 'refund_requested');
                if (validOrders.length === 0) {
                    this.$message.warning('选中的订单中没有申请退款的订单');
                    return;
                }
            } else if (command === 'reject') {
                validOrders = this.selectedOrders.filter(order => order.status === 'refund_requested');
                if (validOrders.length === 0) {
                    this.$message.warning('选中的订单中没有申请退款的订单');
                    return;
                }
            }

            const actionText = command === 'approve' ? '批准退款' : '拒绝退款';
            const confirmText = `确认${actionText} ${validOrders.length} 个订单？`;

            this.$confirm(confirmText, `${actionText}确认`, {
                confirmButtonText: `确认${actionText}`,
                cancelButtonText: '取消',
                type: command === 'approve' ? 'success' : 'warning'
            }).then(() => {
                this.batchRefundApi(validOrders, command);
            });
        },

        // 批量更新状态API
        batchUpdateStatusApi(status, actionText) {
            const orderIds = this.selectedOrders.map(order => order.order_id);

            // 创建进度提示
            const progressMessage = this.$message({
                message: `开始${actionText}，共 ${orderIds.length} 个订单...`,
                type: 'info',
                duration: 0,
                showClose: true
            });

            const loading = this.$loading({
                lock: true,
                text: `${actionText}中... (0/${orderIds.length})`,
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });

            let completedCount = 0;
            let failedCount = 0;
            const failedOrders = [];

            // 使用Promise.all并发处理，但限制并发数量
            const batchSize = 3;
            const batches = [];

            for (let i = 0; i < orderIds.length; i += batchSize) {
                batches.push(orderIds.slice(i, i + batchSize));
            }

            const processBatch = async (batch) => {
                const promises = batch.map(orderId =>
                    this.$http.post('/sxgz/api.php?action=update_order_status', {
                        order_id: orderId,
                        status: status
                        // 不再发送notes，操作记录通过wlog处理
                    }).then(response => {
                        completedCount++;
                        loading.setText(`${actionText}中... (${completedCount}/${orderIds.length})`);
                        console.log(`订单 ${orderId} 更新响应:`, response.data);
                        if (!response.data || !response.data.success) {
                            failedCount++;
                            failedOrders.push(orderId);
                            console.error(`订单 ${orderId} 更新失败:`, response.data?.message || '未知错误');
                        }
                        return response;
                    }).catch(error => {
                        completedCount++;
                        failedCount++;
                        failedOrders.push(orderId);
                        loading.setText(`${actionText}中... (${completedCount}/${orderIds.length})`);
                        console.error(`订单 ${orderId} 请求失败:`, error);
                        return { data: { success: false, message: error.message || '网络错误' } };
                    })
                );

                return Promise.all(promises);
            };

            // 顺序处理每个批次
            const processAllBatches = async () => {
                for (let i = 0; i < batches.length; i++) {
                    await processBatch(batches[i]);
                }
            };

            processAllBatches().then(() => {
                loading.close();
                progressMessage.close();

                const successCount = orderIds.length - failedCount;

                // 显示详细的操作结果
                if (failedCount === 0) {
                    this.$notify({
                        title: `${actionText}完成`,
                        message: `成功${actionText} ${successCount} 个订单`,
                        type: 'success',
                        duration: 3000
                    });
                } else if (successCount === 0) {
                    this.$notify({
                        title: `${actionText}失败`,
                        message: `所有订单${actionText}失败，请检查订单状态或网络连接`,
                        type: 'error',
                        duration: 5000
                    });
                } else {
                    this.$notify({
                        title: `${actionText}部分完成`,
                        message: `成功 ${successCount} 个，失败 ${failedCount} 个订单`,
                        type: 'warning',
                        duration: 4000
                    });
                }

                this.selectedOrders = []; // 清空选择
                this.loadOrders(); // 刷新列表
                this.loadStatistics(); // 刷新统计数据
            }).catch(error => {
                loading.close();
                progressMessage.close();
                console.error(`批量${actionText}错误:`, error);
                this.$notify({
                    title: `${actionText}异常`,
                    message: `批量${actionText}过程中发生异常，请重试`,
                    type: 'error',
                    duration: 5000
                });
            });
        },

        // 批量退款API
        batchRefundApi(orders, action) {
            const orderIds = orders.map(order => order.order_id);
            const actionText = action === 'approve' ? '批准退款' : '拒绝退款';
            const loading = this.$loading({
                lock: true,
                text: `${actionText}中... (0/${orderIds.length})`
            });

            let completedCount = 0;
            let failedCount = 0;
            const failedOrders = [];

            const processBatch = async (batch) => {
                const promises = batch.map(orderId =>
                    this.$http.post('/sxgz/api.php?action=handle_refund', {
                        order_id: orderId,
                        action: action
                        // 不再发送notes，操作记录通过wlog处理
                    }).then(response => {
                        completedCount++;
                        loading.setText(`${actionText}中... (${completedCount}/${orderIds.length})`);
                        console.log(`订单 ${orderId} 退款处理响应:`, response.data);
                        if (!response.data || !response.data.success) {
                            failedCount++;
                            failedOrders.push(orderId);
                            console.error(`订单 ${orderId} 退款处理失败:`, response.data?.message || '未知错误');
                        }
                        return response;
                    }).catch(error => {
                        completedCount++;
                        failedCount++;
                        failedOrders.push(orderId);
                        loading.setText(`${actionText}中... (${completedCount}/${orderIds.length})`);
                        console.error(`订单 ${orderId} 退款请求失败:`, error);
                        return { data: { success: false, message: error.message || '网络错误' } };
                    })
                );

                return Promise.all(promises);
            };

            // 分批处理
            const batchSize = 3;
            const batches = [];
            for (let i = 0; i < orderIds.length; i += batchSize) {
                batches.push(orderIds.slice(i, i + batchSize));
            }

            const processAllBatches = async () => {
                for (let i = 0; i < batches.length; i++) {
                    await processBatch(batches[i]);
                }
            };

            processAllBatches().then(() => {
                loading.close();

                const successCount = orderIds.length - failedCount;
                if (failedCount === 0) {
                    this.$message.success(`成功${actionText} ${successCount} 个订单`);
                } else if (successCount === 0) {
                    this.$message.error(`${actionText}失败，共 ${failedCount} 个订单${actionText}失败`);
                } else {
                    this.$message.warning(`部分${actionText}成功：成功 ${successCount} 个，失败 ${failedCount} 个`);
                }

                this.selectedOrders = []; // 清空选择
                this.loadOrders(); // 刷新列表
                this.loadStatistics(); // 刷新统计数据
            });
        },

        // 更新订单状态API
        updateOrderStatusApi(orderId, status, notes = '') {
            const loading = this.$loading({
                lock: true,
                text: '更新中...'
            });

            this.$http.post('/sxgz/api.php?action=admin_update', {
                order_id: orderId,
                status: status,
                admin_notes: notes
            }).then(response => {
                loading.close();
                if (response.data.success) {
                    this.$message.success('订单状态更新成功');
                    this.loadOrders();
                    this.loadStatistics(); // 刷新统计数据
                    if (this.detailDialogVisible) {
                        this.loadOrderDetails(orderId);
                    }
                } else {
                    this.$message.error(response.data.message || '更新失败');
                }
            }).catch(() => {
                loading.close();
                this.$message.error('更新失败');
            });
        },

        // ==================== 统计分析方法 ====================

        // 加载统计数据
        loadStatistics() {
            // 加载基础统计
            this.$http.get('/sxgz/api.php?action=get_statistics')
                .then(response => {
                    if (response.data && response.data.success) {
                        // 合并统计数据，确保所有字段都有默认值
                        this.statistics = {
                            total_orders: response.data.data.total_orders || 0,
                            processing_orders: response.data.data.processing_orders || 0,
                            pending_orders: response.data.data.pending_orders || 0,
                            completed_orders: response.data.data.completed_orders || 0,
                            refund_requests: response.data.data.refund_requests || 0,
                            today_orders: response.data.data.today_orders || 0,
                            total_revenue: response.data.data.total_revenue || 0,
                            active_users: response.data.data.active_users || 0
                        };
                    } else {
                        console.error('基础统计失败:', response.data);
                        // 静默失败，使用默认值
                    }
                })
                .catch(error => {
                    console.error('基础统计请求失败:', error);
                    // 静默失败，使用默认值
                });


        },

        // 刷新统计数据
        refreshStats() {
            this.loadStatistics();
            this.$message.success('统计数据已刷新');
        },



        // ==================== 工具方法 ====================

        // 获取服务类型标签
        getServiceTypeTag(type) {
            const tagMap = {
                'electronic': 'info',
                'mail': 'warning',
                'both': 'success'
            };
            return tagMap[type] || 'info';
        },

        // 获取服务类型文本
        getServiceTypeText(type) {
            const textMap = {
                'electronic': '电子版',
                'mail': '邮寄服务',
                'both': '邮寄+电子版'
            };
            return textMap[type] || type;
        },

        // 获取状态标签
        getStatusTag(status) {
            const tagMap = {
                'pending': '',           // 默认灰色，优先级较低
                'processing': 'danger',  // 红色，最高优先级，需要立即处理
                'completed': 'success',  // 绿色，已完成
                'cancelled': 'info',     // 蓝色，已取消
                'failed': 'danger',      // 红色，失败
                'refund_requested': 'warning', // 橙色，第二优先级
                'refunded': 'info'       // 蓝色，已退款
            };
            return tagMap[status] || 'info';
        },

        // 获取状态文本
        getStatusText(status) {
            const textMap = {
                'pending': '待处理',
                'processing': '处理中',
                'completed': '已完成',
                'cancelled': '已取消',
                'failed': '失败',
                'refund_requested': '申请退款',
                'refunded': '已退款'
            };
            return textMap[status] || status;
        },

        // 格式化日期
        formatDate(dateStr) {
            if (!dateStr) return '';
            const date = new Date(dateStr);
            return date.toLocaleString('zh-CN');
        },

        // 加载订单详情
        loadOrderDetails(orderId) {
            this.$http.get(`/sxgz/api.php?action=get_order&order_id=${orderId}`)
                .then(response => {
                    if (response.data.success) {
                        this.currentOrder = response.data.data.order;
                        this.orderFiles = response.data.data.files;
                    } else {
                        this.$message.error(response.data.message || '加载订单详情失败');
                    }
                })
                .catch(() => {
                    this.$message.error('加载订单详情失败');
                });
        },

        // 更新订单状态（从详情页）
        updateOrderStatus() {
            if (!this.currentOrder) return;
            this.updateOrderStatusApi(this.currentOrder.order_id, this.currentOrder.status, this.adminNotes);
        },

        // 通用文件下载方法
        downloadFile(url) {
            if (!url) {
                this.$message.error('下载链接不存在');
                return;
            }

            // 检查URL格式 - 支持插件文件的完整URL
            if (!url.startsWith('/sxgz/download.php') && !url.startsWith('http')) {
                this.$message.warning('下载链接格式可能有误，尝试直接访问');
            }

            // 如果是完整URL（插件文件），直接打开；否则在新窗口打开
            if (url.startsWith('http')) {
                // 插件文件，直接下载
                const link = document.createElement('a');
                link.href = url;
                link.target = '_blank';
                link.download = '';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                // 本地文件，在新窗口打开
                window.open(url, '_blank');
            }
        },

        // 处理文件上传
        handleProcessedUploadSuccess(response, file) {
            if (response.success) {
                this.$message.success('文件上传成功');
                this.loadOrderDetails(this.currentOrder.order_id);
            } else {
                this.$message.error(response.message || '上传失败');
            }
        },

        handleProcessedUploadError() {
            this.$message.error('文件上传失败');
        },

        // 删除文件
        deleteFile(filename, fileType) {
            this.$confirm('确定要删除这个文件吗？', '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.post('/sxgz/api.php?action=delete_file', {
                    order_id: this.currentOrder.order_id,
                    filename: filename,
                    file_type: fileType
                }).then(response => {
                    if (response.data.success) {
                        this.$message.success('文件删除成功');
                        this.loadOrderDetails(this.currentOrder.order_id);
                    } else {
                        this.$message.error(response.data.message || '删除失败');
                    }
                }).catch(error => {
                    console.error('删除文件失败:', error);
                    this.$message.error('删除文件失败');
                });
            }).catch(() => {
                // 用户取消删除
            });
        },

        // 文件预览方法
        previewFile(url) {
            if (!url) {
                this.$message.error('预览链接不存在');
                return;
            }
            window.open(url, '_blank');
        },

        // 格式化文件大小
        formatFileSize(size) {
            if (!size) return '未知';

            const sizeNum = parseInt(size);
            if (sizeNum < 1024) return sizeNum + 'B';
            if (sizeNum < 1024 * 1024) return (sizeNum / 1024).toFixed(1) + 'KB';
            return (sizeNum / (1024 * 1024)).toFixed(1) + 'MB';
        },

        // 关闭详情对话框
        handleDetailClose(done) {
            this.currentOrder = null;
            this.orderFiles = null;
            this.adminNotes = '';
            done();
        },

        // ==================== 公司管理方法 ====================

        // 加载公司列表
        loadCompanies() {
            this.companyLoading = true;

            let params = {
                page: this.companyCurrentPage,
                limit: this.companyPageSize,
                admin: 1
            };

            // 添加搜索条件
            if (this.companySearchForm.search) {
                params.search = this.companySearchForm.search;
            }

            this.$http.get('/sxgz/api.php?action=get_companies', { params: params })
                .then(response => {
                    this.companyLoading = false;
                    if (response.data.success) {
                        // 检查数据结构
                        if (response.data.data.companies) {
                            // 管理员接口返回的结构
                            this.companies = response.data.data.companies;
                            this.companyTotal = response.data.data.total;
                            this.companyCurrentPage = response.data.data.page;
                        } else {
                            // 普通接口返回的结构
                            this.companies = response.data.data;
                            this.companyTotal = response.data.data.length;
                            this.companyCurrentPage = 1;
                        }
                    } else {
                        this.$message.error(response.data.message || '加载公司列表失败');
                    }
                })
                .catch(error => {
                    this.companyLoading = false;
                    console.error('加载公司列表失败:', error);
                    this.$message.error('加载公司列表失败');
                });
        },

        // 搜索公司
        searchCompanies() {
            this.companyCurrentPage = 1;
            this.loadCompanies();
        },


        // 公司分页处理
        handleCompanyPageChange(page) {
            this.companyCurrentPage = page;
            this.loadCompanies();
        },

        // 显示添加公司对话框
        showAddCompanyDialog() {
            this.companyDialogTitle = '添加公司';

            this.companyForm = {
                name: '',
                price: 0,
                content: '',
                status: 1
            };
            this.companyDialogVisible = true;
        },

        // 编辑公司
        editCompany(company) {
            this.companyDialogTitle = '编辑公司';
            this.companyForm = {
                cid: company.cid,
                name: company.name,
                price: parseFloat(company.price),
                content: company.content,
                status: parseInt(company.status)
            };
            this.companyDialogVisible = true;
        },

        // 保存公司
        saveCompany() {
            this.$refs.companyForm.validate((valid) => {
                if (valid) {
                    this.companySaving = true;

                    const action = this.companyForm.cid > 0 ? 'update_company' : 'add_company';

                    this.$http.post(`/sxgz/api.php?action=${action}`, this.companyForm)
                        .then(response => {
                            this.companySaving = false;
                            if (response.data.success) {
                                this.$message.success(response.data.message || '保存成功');
                                this.companyDialogVisible = false;
                                this.loadCompanies();
                            } else {
                                this.$message.error(response.data.message || '保存失败');
                            }
                        })
                        .catch(() => {
                            this.companySaving = false;
                            this.$message.error('保存失败');
                        });
                } else {
                    return false;
                }
            });
        },

        // 删除公司
        deleteCompany(company) {
            this.$confirm(`确认删除公司"${company.name}"？此操作不可恢复。`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.post('/sxgz/api.php?action=delete_company', {
                    cid: company.cid
                }).then(response => {
                    if (response.data.success) {
                        this.$message.success('删除成功');
                        this.loadCompanies();
                    } else {
                        this.$message.error(response.data.message || '删除失败');
                    }
                }).catch(() => {
                    this.$message.error('删除失败');
                });
            });
        },

        // 关闭公司对话框
        handleCompanyDialogClose() {
            this.companyDialogVisible = false;
            this.$refs.companyForm.resetFields();
        },

        // 处理导出命令
        handleExport(command) {
            this.exportOrders(command);
        },

        // 处理订单操作下拉菜单
        handleOrderAction(command) {
            const { action, order } = command;

            switch (action) {
                case 'delete':
                    this.deleteOrder(order);
                    break;
                case 'export':
                    this.exportSingleOrder(order);
                    break;
                default:
                    console.warn('未知的操作:', action);
            }
        },

        // 导出单个订单
        exportSingleOrder(order) {
            const loading = this.$loading({
                lock: true,
                text: '正在导出订单数据...'
            });

            // 构建导出参数 - 指定单个订单ID
            const params = new URLSearchParams();
            params.append('format', 'excel');
            params.append('export_type', 'single');
            params.append('order_id', order.order_id);

            // 创建下载链接
            const downloadUrl = `/sxgz/api.php?action=export_orders&${params.toString()}`;

            // 创建临时链接进行下载
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            loading.close();

            // 显示成功消息
            setTimeout(() => {
                this.$message.success(`订单 ${order.order_no} 导出成功`);
            }, 500);
        },

        // 解析营业执照公司信息
        getLicenseCompanies(order) {
            if (order.business_license != '1' || !order.special_requirements) {
                return [];
            }

            // 从special_requirements中提取营业执照公司信息
            const requirements = order.special_requirements;
            const licenseMatch = requirements.match(/营业执照公司:\s*([^|]+)/);

            if (!licenseMatch) {
                return [];
            }

            const licenseText = licenseMatch[1].trim();
            const companies = [];

            // 解析格式：公司A(¥100), 公司B(¥200)
            const companyMatches = licenseText.match(/([^(,]+)\(¥([^)]+)\)/g);

            if (companyMatches) {
                companyMatches.forEach(match => {
                    const parts = match.match(/([^(]+)\(¥([^)]+)\)/);
                    if (parts) {
                        companies.push({
                            name: parts[1].trim(),
                            price: parts[2].trim()
                        });
                    }
                });
            }

            return companies;
        },

        // 获取材料类型文本
        getMaterialTypeText(type) {
            const types = {
                'upload': '在线上传',
                'mail': '邮寄纸质'
            };
            return types[type] || type;
        },

        // 清理特殊要求文本（移除营业执照公司信息）
        getCleanSpecialRequirements(requirements) {
            if (!requirements) return '';

            // 移除营业执照公司信息部分
            let cleaned = requirements.replace(/\s*\|\s*营业执照公司:[^|]*$/g, '');
            cleaned = cleaned.replace(/^营业执照公司:[^|]*\s*\|\s*/g, '');

            // 限制长度
            if (cleaned.length > 20) {
                cleaned = cleaned.substring(0, 20) + '...';
            }

            return cleaned.trim();
        },

        // 获取批量操作提示
        getBatchOperationHint() {
            if (this.selectedOrders.length === 0) return '';

            const statusCounts = {};
            this.selectedOrders.forEach(order => {
                statusCounts[order.status] = (statusCounts[order.status] || 0) + 1;
            });

            const hints = [];

            // 统计各状态数量
            if (statusCounts.pending) {
                hints.push(`${statusCounts.pending}个待处理`);
            }
            if (statusCounts.processing) {
                hints.push(`${statusCounts.processing}个处理中`);
            }
            if (statusCounts.completed) {
                hints.push(`${statusCounts.completed}个已完成`);
            }
            if (statusCounts.failed) {
                hints.push(`${statusCounts.failed}个已失败`);
            }
            if (statusCounts.refund_requested) {
                hints.push(`${statusCounts.refund_requested}个申请退款`);
            }
            if (statusCounts.refunded) {
                hints.push(`${statusCounts.refunded}个已退款`);
            }

            let hintText = hints.join('、');

            // 添加操作建议
            if (statusCounts.pending > 0) {
                hintText += ' | 可批量开始处理';
            }
            if (statusCounts.processing > 0) {
                hintText += ' | 可批量完成或标记失败';
            }
            if (statusCounts.refund_requested > 0) {
                hintText += ' | 可批量处理退款';
            }

            return hintText;
        },

        // 处理快速选择
        handleQuickSelect(command) {
            switch (command) {
                case 'pending':
                    this.selectedOrders = this.orders.filter(order => order.status === 'pending');
                    this.$message.success(`已选择 ${this.selectedOrders.length} 个待处理订单`);
                    break;
                case 'processing':
                    this.selectedOrders = this.orders.filter(order => order.status === 'processing');
                    this.$message.success(`已选择 ${this.selectedOrders.length} 个处理中订单`);
                    break;
                case 'refund_requested':
                    this.selectedOrders = this.orders.filter(order => order.status === 'refund_requested');
                    this.$message.success(`已选择 ${this.selectedOrders.length} 个申请退款订单`);
                    break;
                default:
                    this.$message.error('未知的选择类型');
            }
        },

        // 处理选中订单变化
        handleSelectionChange(selection) {
            this.selectedOrders = selection;
        },

        // 清空选择
        clearSelection() {
            this.$refs.orderTable.clearSelection();
            this.selectedOrders = [];
        },

        // 处理批量导出
        handleBatchExport(format) {
            if (this.selectedOrders.length === 0) {
                this.$message.warning('请先选择要导出的订单');
                return;
            }
            this.exportSelectedOrders(format);
        },

        // 导出选中订单
        exportSelectedOrders(format) {
            const loading = this.$loading({
                lock: true,
                text: '正在导出选中订单...'
            });

            // 构建导出参数
            const params = new URLSearchParams();
            params.append('format', format);
            params.append('export_type', 'selected');

            // 添加选中的订单ID
            const orderIds = this.selectedOrders.map(order => order.order_id).join(',');
            params.append('order_ids', orderIds);

            // 创建隐藏的下载链接
            const downloadUrl = `/sxgz/api.php?action=export_orders&${params.toString()}`;

            // 创建临时链接进行下载
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 延迟关闭loading，给下载一些时间
            setTimeout(() => {
                loading.close();
                this.$message.success(`${format.toUpperCase()}文件导出成功，共${this.selectedOrders.length}个订单`);
            }, 1000);
        },

        // 导出订单数据（筛选结果）
        exportOrders(format) {
            const loading = this.$loading({
                lock: true,
                text: '正在导出数据...'
            });

            // 构建导出参数
            const params = new URLSearchParams();
            params.append('format', format);
            params.append('export_type', 'filtered');

            // 添加搜索条件
            if (this.searchForm.status) {
                params.append('status', this.searchForm.status);
            }
            if (this.searchForm.service_type) {
                params.append('service_type', this.searchForm.service_type);
            }
            if (this.searchForm.search) {
                params.append('search', this.searchForm.search);
            }
            if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
                params.append('start_date', this.searchForm.dateRange[0]);
                params.append('end_date', this.searchForm.dateRange[1]);
            }

            // 创建隐藏的下载链接
            const downloadUrl = `/sxgz/api.php?action=export_orders&${params.toString()}`;

            // 创建临时链接进行下载
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 延迟关闭loading，给下载一些时间
            setTimeout(() => {
                loading.close();
                this.$message.success(`${format.toUpperCase()}文件导出成功`);
            }, 1000);
        }
    }
});
</script>
