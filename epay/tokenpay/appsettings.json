{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.Hosting.Lifetime": "Information"
      }
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DB": "Data Source=TokenPay.db;"
  },
  "TRON-PRO-API-KEY": "dff4acf6-1a00-4266-8669-04c76ae68a3a", // 避免接口请求频繁被限制，此处申请 https://www.trongrid.io/dashboard/keys
  "BaseCurrency": "CNY", //默认货币，支持 CNY、USD、EUR、GBP、AUD、HKD、TWD、SGD
  "Rate": { //汇率 设置0将使用自动汇率
    "USDT": 0,
    "TRX": 0,
    "ETH": 0,
    "USDC": 0
  },
  "ExpireTime": 1800, //单位秒
  "UseDynamicAddress": false, //是否使用动态地址，设为false时，与EPUSDT表现类似；设为true时，为每个下单用户分配单独的收款地址
  "Address": { // UseDynamicAddress设为false时在此配置TRON收款地址，EVM可以替代所有ETH系列的收款地址，支持单独配置某条链的收款地址
    "TRON": [ "TLNLC1BT5qBXCw9VCHgX5dotgJgEApiY1j" ],
    "EVM": [ "******************************************" ]
  },
  "OnlyConfirmed": false, //默认仅查询已确认的数据，如果想要回调更快，可以设置为false
  "NotifyTimeOut": 3, //异步通知超时时间
  "ApiToken": "pqo24uw3ja765skfhi42ah4654", //异步通知密钥，请务必修改此密钥为随机字符串，脸滚键盘即可
  "WebSiteUrl": "https://tokenpay.freedomp.icu", //配置服务器外网域名
  "Collection": { //需要 UseDynamicAddress 为 true 才有使用归集功能的意义，静态地址收款TokenPay无法归集
    "Enable": false, //是否启用归集功能，false 表示关闭，true 表示启用
    "UseEnergy": true, //是否租用能量归集，降低归集成本，false 表示直接燃烧trx转账
    "ForceCheckAllAddress": false, //强制检查所有地址的余额
    "RetainUSDT": true, //归集USDT时是否保留0.000001，用于降低用户下次支付的成本
    "CheckTime": 1, //归集任务运行间隔，默认1小时运行一次，单位：小时
    "MinUSDT": 0.1, //只归集USDT余额大于此金额的地址
    "NeedEnergy": 65000, //归集USDT所需能量，此配置项通常不需要修改，发生变化时会在GitHub更新
    "EnergyPrice": 210, //波场当前能量单价，此配置项通常不需要修改，发生变化时会在GitHub更新
    "Address": "TKGTx4pCKiKQbk8evXHTborfZn754TGViP" //归集收款地址，配置你自己的收款地址
  },
  "Telegram": {
    "AdminUserId": 5184072914, // 你的TG账号ID，如不知道ID，可给 https://t.me/QueryCoinBot 发送 /tgid 获取ID
    "BotToken": "8391291051:AAHAYLEUZGDK-c6VUh_EeAvGRRIDcB8hVkc" //从https://t.me/BotFather 创建机器人时，会给你BotToken
  },
  "RateMove": { //汇率微调，支持设置正负数，仅支持两位小数
    "TRX_CNY": 0,
    "USDT_CNY": 0
  },
  "DynamicAddressConfig": {
    "AmountMove": true, //使用动态地址收款时启用动态金额，支持非准确金额支付，用于优化中心化钱包或交易所提币扣除手续费后金额不匹配的情况，可自行决定是否开启，默认false表示不启用
    "TRX": [ 0, 2 ], //表示下浮0，上浮2，如果实际支付金额为100TRX，根据此配置，用户支付金额在100-102TRX订单都会成功
    "USDT": [ 1, 2 ], //表示下浮1，上浮2，如果实际支付金额为100USDT，根据此配置，用户支付金额在99-102USDT订单都会成功
    "ETH": [ 0.1, 0.15 ] //表示下浮0.1，上浮0.1，如果实际支付金额为0.5ETH，根据此配置，用户支付金额在0.4-0.65ETH订单都会成功
  }
}
