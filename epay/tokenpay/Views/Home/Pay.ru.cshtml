﻿@{
    ViewData["Title"] = "支付页";
    var Now = DateTime.Now.ToUniversalTime();
    var ExpireTime = ViewData.ContainsKey("ExpireTime") ? Convert.ToDateTime(ViewData["ExpireTime"]).ToUniversalTime() : Now;
}
@using TokenPay.Domains;
@using TokenPay.Extensions
@using TokenPay.Models.EthModel;
@model TokenPay.Domains.TokenOrders
@inject List<EVMChain> chain
@if (Model == null)
{
    <div class="row align-items-center h-100">
        <div class="text-center">
            <h1 class="display-4">Приказы не существуют！</h1>
        </div>
    </div>

}
else
{
    <style>
        .time {
            font-family: Consolas,Menlo,Courier;
        }

        .address {
        @if (@Model.ToAddress.StartsWith("T"))
        {
            @Html.Raw("font-size:14px;")
        }
        else
        {
            @Html.Raw("font-size:12px;")
        }
        }
    </style>
    <div class="text-center pt-2">
        <h4 class="display-6">Валюта, в которой вы платите <span class="text-danger">@Model.Currency.ToCurrency(chain,true)</span> для <span class="text-danger">@Model.Currency.ToBlockchainEnglishName(chain)</span> </h4>

        <div class="d-flex mb-2">
            <div class="input-group m-auto" style="width:410px;">
                <input class="form-control form-control-sm" type="text" id="Token" value="@Model.ToAddress" readonly>
                <button class="btn btn-primary btn-sm" data-clipboard-target="#Token">копировать</button>
            </div>
        </div>
        <div class="mb-2 fw-bold display-6 invisible time">
            осталось времени：<span class="text-danger"><span id="day_show"></span><span id="hour_show"></span><span id="minute_show"></span><span id="second_show"></span></span>
        </div>
        <div class="text-danger fw-bold mb-2">Пожалуйста, внимательно проверьте блокчейн и валюту, чтобы избежать потери активов или сбоя платежа ! ! !</div>
        <div class="mb-2">
            <div class="text-danger fw-bold" style="position:relative;bottom:-6px;height:0;">блокчейн：@Model.Currency.ToBlockchainEnglishName(chain) валюта：@Model.Currency.ToCurrency(chain)</div>
            <img src="data:image/png;base64,@ViewData["QrCode"]" class="border border-gray-300" alt="address">
            <div class="text-danger fw-semibold address" style="position:relative;top:-24px;height:0;">@Model.ToAddress</div>
        </div>
        <div class="text-start m-auto" style="width:300px;">
            <div class="mb-2 fw-bold">
                Сумма к оплате：<span class="text-danger">@Model.Amount @Model.Currency.ToCurrency(chain)</span><button class="btn btn-primary btn-sm ms-2" data-clipboard-text="@Model.Amount">копировать</button>
            </div>
            <div class="mb-2">
                Номер заказа：<span class="text-danger">@Model.OutOrderId</span>
            </div>
            <div class="mb-2">
                Истечение срока：<span class="text-danger">@ExpireTime.ToString("yyyy-MM-dd HH:mm:ss \"GMT\"zzz")</span>
            </div>
            <div class="mt-4 text-start">
                <div>Купить валюту блокчейна на этих сайтах</div>
                <div>1. Купить <span class="text-danger">@Model.Currency.ToCurrency(chain)</span> на <a href="https://www.okx.com/buy-crypto#sourceBase=@Model.Currency.ToCurrency(chain)" target="_blank">OKX</a></div>
                <div>2. Купить <span class="text-danger">@Model.Currency.ToCurrency(chain)</span> на <a href="https://p2p.binance.com/express/buy/@Model.Currency.ToCurrency(chain)" target="_blank">Binance</a></div>
            </div>
        </div>
    </div>

    @section Scripts{
    <script>
        let Time;
        var EndTime = new Date('@ExpireTime.ToUniversalTime().ToString("yyyy/MM/dd HH:mm:ss")');
        function timer() {
            window.setInterval(function () {
                var intDiff = (EndTime - new Date(new Date().toISOString().replace('T',' ').replace('Z',''))) / 1000
                if (intDiff <= 0) return;
                $(".time").removeClass("invisible")
                var day = 0,
                    hour = 0,
                    minute = 0,
                    second = 0;
                if (intDiff > 0) {
                    day = Math.floor(intDiff / (60 * 60 * 24));
                    hour = Math.floor(intDiff / (60 * 60)) - (day * 24);
                    minute = Math.floor(intDiff / 60) - (day * 24 * 60) - (hour * 60);
                    second = Math.floor(intDiff) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60);
                }
                if (minute <= 9) minute = '0' + minute;
                if (second <= 9) second = '0' + second;
                if (day)
                    $('#day_show').html(day + " дней ");
                if (hour)
                    $('#hour_show').html('<s id="h"></s>' + hour + ' часов ');
                if (minute)
                    $('#minute_show').html('<s></s>' + minute + ' минут ');
                $('#second_show').html('<s></s>' + second + ' секунд ');
                intDiff--;
            }, 1000);
        }
        $(() => {
            timer();
            Time = setInterval(Check, 1000);
        })
        function Check() {
            var RedirectUrl = "@(Model?.RedirectUrl)";
                $.get("/Check/@(Model?.Id)")
                    .then(x => {
                        if (x === 'Pending') {
                            console.log('待支付')
                        } else if (x === 'Expired') {
                            clearInterval(Time)
                            console.log('订单过期')
                            location.reload();
                        } else if (x === 'Paid') {
                            clearInterval(Time)
                            console.log('已支付')
                            setTimeout(() => {
                                if (RedirectUrl) {
                                    location = RedirectUrl
                                } else {
                                    alert("Paid")
                                }
                            }, 0)
                        }
                    })
            }
        </script>
    }
}
