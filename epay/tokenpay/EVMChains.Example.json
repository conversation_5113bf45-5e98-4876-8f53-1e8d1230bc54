{
  "EVMChains": [
    {
      "Enable": false, // false 表示不启用此区块链， true 表示启用
      "ChainName": "以太坊",
      "ChainNameEN": "ETH",
      "BaseCoin": "ETH",
      "Decimals": 18,
      "ScanHost": "https://etherscan.io",
      "ChainId": 1, //各个ETH系的ChainId可在此页面检索 https://docs.etherscan.io/etherscan-v2/supported-chains
      "ApiKey": "必填项", // 使用ETH V2 Api，申请教程 https://docs.etherscan.io/etherscan-v2/getting-an-api-key
      "ERC20Name": "ERC20",
      "ERC20": [
        {
          "Name": "USDT",
          "ContractAddress": "******************************************"
        },
        {
          "Name": "USDC",
          "ContractAddress": "******************************************"
        }
      ]
    },
    {
      "Enable": false, // false 表示不启用此区块链， true 表示启用
      "ChainName": "币安智能链",
      "ChainNameEN": "BSC",
      "BaseCoin": "BNB",
      "Decimals": 18,
      "ScanHost": "https://www.bscscan.com",
      "ChainId": 56, //各个ETH系的ChainId可在此页面检索 https://docs.etherscan.io/etherscan-v2/supported-chains
      "ApiKey": "必填项", // 使用ETH V2 Api，申请教程 https://docs.etherscan.io/etherscan-v2/getting-an-api-key
      "ERC20Name": "BEP20",
      "ERC20": [
        {
          "Name": "USDT",
          "ContractAddress": "******************************************"
        },
        {
          "Name": "USDC",
          "ContractAddress": "******************************************"
        }
      ]
    },
    {
      "Enable": false, // false 表示不启用此区块链， true 表示启用
      "ChainName": "Polygon",
      "ChainNameEN": "Polygon",
      "BaseCoin": "POL",
      "Decimals": 18,
      "ScanHost": "https://polygonscan.com",
      "ChainId": 137, //各个ETH系的ChainId可在此页面检索 https://docs.etherscan.io/etherscan-v2/supported-chains
      "ApiKey": "必填项", // 使用ETH V2 Api，申请教程 https://docs.etherscan.io/etherscan-v2/getting-an-api-key
      "ERC20Name": "ERC20",
      "ERC20": [
        {
          "Name": "USDT",
          "ContractAddress": "******************************************"
        },
        {
          "Name": "USDC",
          "ContractAddress": "******************************************"
        }
      ]
    }
  ]
}
