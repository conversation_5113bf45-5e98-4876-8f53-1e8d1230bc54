2025-08-10 00:17:24.876 +08:00 [INF] -------------TokenPay Info-------------
2025-08-10 00:17:25.311 +08:00 [INF] File Version: *******
2025-08-10 00:17:25.311 +08:00 [INF] Product Version: 1.1.0+f5f6f70ae02862a6a42fa0328dcacdd6c929f21e
2025-08-10 00:17:25.312 +08:00 [INF] -------------System Info-------------
2025-08-10 00:17:25.312 +08:00 [INF] Platform: Linux
2025-08-10 00:17:25.312 +08:00 [INF] Architecture: "X64"
2025-08-10 00:17:25.336 +08:00 [INF] Description: Ubuntu 22.04.5 LTS
2025-08-10 00:17:25.336 +08:00 [INF] ProcessArchitecture: "X64"
2025-08-10 00:17:25.336 +08:00 [INF] X64: Yes
2025-08-10 00:17:25.337 +08:00 [INF] CPU CORE: 4
2025-08-10 00:17:25.339 +08:00 [INF] HostName: C20240314128253
2025-08-10 00:17:25.340 +08:00 [INF] OSVersion: Unix **********
2025-08-10 00:17:25.344 +08:00 [INF] IsServerGC: false
2025-08-10 00:17:25.345 +08:00 [INF] IsConcurrent: false
2025-08-10 00:17:25.346 +08:00 [INF] LatencyMode: "Interactive"
2025-08-10 00:17:25.598 +08:00 [INF] -------------AppSettings-------------
2025-08-10 00:17:25.599 +08:00 [INF] 支持的币种: ["TRX","USDT_TRC20"]
2025-08-10 00:17:25.604 +08:00 [INF] 币种小数点位数: 
2025-08-10 00:17:25.606 +08:00 [INF] 	TRX=2
2025-08-10 00:17:25.606 +08:00 [INF] 	USDT_TRC20=4
2025-08-10 00:17:25.606 +08:00 [INF] 启用动态地址: false
2025-08-10 00:17:25.606 +08:00 [INF] 启用动态金额: false
2025-08-10 00:17:25.606 +08:00 [INF] 动态金额生效状态: false
2025-08-10 00:17:25.606 +08:00 [INF] 启用波场自动归集: false
2025-08-10 00:17:25.606 +08:00 [INF] -------------End-------------
2025-08-10 00:17:26.891 +08:00 [INF] Starting web host
2025-08-10 00:17:28.313 +08:00 [ERR] 机器人连接失败！
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
2025-08-10 00:17:28.389 +08:00 [FTL] Host terminated unexpectedly
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitIEnumerable(IEnumerableCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey, Func`2)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider, Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost)
   at Microsoft.AspNetCore.Builder.WebApplication.Run(String )
   at Program.<Main>$(String[] args)
2025-08-10 02:15:16.167 +08:00 [INF] -------------TokenPay Info-------------
2025-08-10 02:15:16.609 +08:00 [INF] File Version: *******
2025-08-10 02:15:16.609 +08:00 [INF] Product Version: 1.1.0+f5f6f70ae02862a6a42fa0328dcacdd6c929f21e
2025-08-10 02:15:16.610 +08:00 [INF] -------------System Info-------------
2025-08-10 02:15:16.610 +08:00 [INF] Platform: Linux
2025-08-10 02:15:16.611 +08:00 [INF] Architecture: "X64"
2025-08-10 02:15:16.636 +08:00 [INF] Description: Ubuntu 22.04.5 LTS
2025-08-10 02:15:16.636 +08:00 [INF] ProcessArchitecture: "X64"
2025-08-10 02:15:16.636 +08:00 [INF] X64: Yes
2025-08-10 02:15:16.637 +08:00 [INF] CPU CORE: 4
2025-08-10 02:15:16.638 +08:00 [INF] HostName: C20240314128253
2025-08-10 02:15:16.640 +08:00 [INF] OSVersion: Unix **********
2025-08-10 02:15:16.644 +08:00 [INF] IsServerGC: false
2025-08-10 02:15:16.645 +08:00 [INF] IsConcurrent: false
2025-08-10 02:15:16.646 +08:00 [INF] LatencyMode: "Interactive"
2025-08-10 02:15:16.906 +08:00 [INF] -------------AppSettings-------------
2025-08-10 02:15:16.907 +08:00 [INF] 支持的币种: ["TRX","USDT_TRC20"]
2025-08-10 02:15:16.913 +08:00 [INF] 币种小数点位数: 
2025-08-10 02:15:16.914 +08:00 [INF] 	TRX=2
2025-08-10 02:15:16.914 +08:00 [INF] 	USDT_TRC20=4
2025-08-10 02:15:16.914 +08:00 [INF] 启用动态地址: false
2025-08-10 02:15:16.914 +08:00 [INF] 启用动态金额: false
2025-08-10 02:15:16.914 +08:00 [INF] 动态金额生效状态: false
2025-08-10 02:15:16.914 +08:00 [INF] 启用波场自动归集: false
2025-08-10 02:15:16.915 +08:00 [INF] -------------End-------------
2025-08-10 02:15:17.989 +08:00 [INF] Starting web host
2025-08-10 02:15:23.813 +08:00 [INF] 机器人启动成功！我是FreeDomQA。
2025-08-10 02:15:24.480 +08:00 [INF] 机器人消息发送结果：true
2025-08-10 02:15:24.628 +08:00 [WRN] No XML encryptor configured. Key {1f8269a3-2938-4c84-a72e-9632b4e5b39d} may be persisted to storage in unencrypted form.
2025-08-10 02:15:24.813 +08:00 [INF] Service 订单过期 is starting.
2025-08-10 02:15:24.818 +08:00 [INF] Service 更新汇率 is starting.
2025-08-10 02:15:24.819 +08:00 [INF] Service 订单通知 is starting.
2025-08-10 02:15:24.819 +08:00 [INF] Background Service 发送订单通知 is starting.
2025-08-10 02:15:24.828 +08:00 [INF] Service TRC20订单检测 is starting.
2025-08-10 02:15:24.828 +08:00 [INF] Service TRX订单检测 is starting.
2025-08-10 02:15:24.829 +08:00 [INF] Service EVM基本币订单检测 is starting.
2025-08-10 02:15:24.829 +08:00 [INF] Service EVM代币订单检测 is starting.
2025-08-10 02:15:24.830 +08:00 [INF] Service TRON归集任务 is starting.
2025-08-10 02:15:25.006 +08:00 [INF] Now listening on: http://localhost:5000
2025-08-10 02:15:25.009 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 02:15:25.010 +08:00 [INF] Hosting environment: Production
2025-08-10 02:15:25.011 +08:00 [INF] Content root path: /www/wwwroot/**************/epay/tokenpay
2025-08-10 02:15:27.850 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 02:15:28.246 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.41
2025-08-10 02:15:28.584 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 02:15:28.589 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 03:15:27.821 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 03:15:28.251 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.42
2025-08-10 03:15:28.385 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 03:15:28.392 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 03:17:48.512 +08:00 [INF] Application is shutting down...
2025-08-10 03:17:48.537 +08:00 [INF] Service TRON归集任务 is stopping.
2025-08-10 03:17:48.540 +08:00 [INF] Service EVM代币订单检测 is stopping.
2025-08-10 03:17:48.541 +08:00 [INF] Service EVM基本币订单检测 is stopping.
2025-08-10 03:17:48.541 +08:00 [INF] Service TRX订单检测 is stopping.
2025-08-10 03:17:48.541 +08:00 [INF] Service TRC20订单检测 is stopping.
2025-08-10 03:17:48.541 +08:00 [INF] Background Service 发送订单通知 is stopping.
2025-08-10 03:17:48.542 +08:00 [INF] Service 订单通知 is stopping.
2025-08-10 03:17:48.542 +08:00 [INF] Service 更新汇率 is stopping.
2025-08-10 03:17:48.542 +08:00 [INF] Service 订单过期 is stopping.
"TRX","USDT_TRC20"]
2025-08-10 02:31:47.766 +08:00 [INF] 币种小数点位数: 
2025-08-10 02:31:47.768 +08:00 [INF] 	TRX=2
2025-08-10 02:31:47.768 +08:00 [INF] 	USDT_TRC20=4
2025-08-10 02:31:47.768 +08:00 [INF] 启用动态地址: false
2025-08-10 02:31:47.768 +08:00 [INF] 启用动态金额: false
2025-08-10 02:31:47.768 +08:00 [INF] 动态金额生效状态: false
2025-08-10 02:31:47.768 +08:00 [INF] 启用波场自动归集: false
2025-08-10 02:31:47.768 +08:00 [INF] -------------End-------------
2025-08-10 02:31:49.056 +08:00 [INF] Starting web host
2025-08-10 02:31:50.867 +08:00 [INF] 机器人启动成功！我是FreeDomQA。
2025-08-10 02:31:51.348 +08:00 [INF] 机器人消息发送结果：true
2025-08-10 02:31:51.690 +08:00 [INF] Service 订单过期 is starting.
2025-08-10 02:31:51.699 +08:00 [INF] Service 更新汇率 is starting.
2025-08-10 02:31:51.700 +08:00 [INF] Service 订单通知 is starting.
2025-08-10 02:31:51.701 +08:00 [INF] Background Service 发送订单通知 is starting.
2025-08-10 02:31:51.711 +08:00 [INF] Service TRC20订单检测 is starting.
2025-08-10 02:31:51.712 +08:00 [INF] Service TRX订单检测 is starting.
2025-08-10 02:31:51.712 +08:00 [INF] Service EVM基本币订单检测 is starting.
2025-08-10 02:31:51.713 +08:00 [INF] Service EVM代币订单检测 is starting.
2025-08-10 02:31:51.713 +08:00 [INF] Service TRON归集任务 is starting.
2025-08-10 02:31:51.882 +08:00 [INF] Now listening on: http://127.0.0.1:5080
2025-08-10 02:31:51.884 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 02:31:51.884 +08:00 [INF] Hosting environment: Production
2025-08-10 02:31:51.884 +08:00 [INF] Content root path: /www/wwwroot/**************/epay/tokenpay
2025-08-10 02:31:54.758 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 02:31:55.650 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.41
2025-08-10 02:31:55.849 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 02:31:55.854 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 03:25:06.718 +08:00 [INF] Application is shutting down...
2025-08-10 03:25:06.756 +08:00 [INF] Service TRON归集任务 is stopping.
2025-08-10 03:25:06.760 +08:00 [INF] Service EVM代币订单检测 is stopping.
2025-08-10 03:25:06.761 +08:00 [INF] Service EVM基本币订单检测 is stopping.
2025-08-10 03:25:06.761 +08:00 [INF] Service TRX订单检测 is stopping.
2025-08-10 03:25:06.761 +08:00 [INF] Service TRC20订单检测 is stopping.
2025-08-10 03:25:06.973 +08:00 [INF] Background Service 发送订单通知 is stopping.
2025-08-10 03:25:06.974 +08:00 [INF] Service 订单通知 is stopping.
2025-08-10 03:25:06.974 +08:00 [INF] Service 更新汇率 is stopping.
2025-08-10 03:25:06.975 +08:00 [INF] Service 订单过期 is stopping.
n: Unix **********
2025-08-10 03:17:51.050 +08:00 [INF] IsServerGC: false
2025-08-10 03:17:51.051 +08:00 [INF] IsConcurrent: false
2025-08-10 03:17:51.052 +08:00 [INF] LatencyMode: "Interactive"
2025-08-10 03:17:51.310 +08:00 [INF] -------------AppSettings-------------
2025-08-10 03:17:51.311 +08:00 [INF] 支持的币种: ["TRX","USDT_TRC20"]
2025-08-10 03:17:51.317 +08:00 [INF] 币种小数点位数: 
2025-08-10 03:17:51.318 +08:00 [INF] 	TRX=2
2025-08-10 03:17:51.318 +08:00 [INF] 	USDT_TRC20=4
2025-08-10 03:17:51.318 +08:00 [INF] 启用动态地址: false
2025-08-10 03:17:51.319 +08:00 [INF] 启用动态金额: true
2025-08-10 03:17:51.319 +08:00 [INF] 动态金额生效状态: false
2025-08-10 03:17:51.319 +08:00 [INF] 启用波场自动归集: false
2025-08-10 03:17:51.319 +08:00 [INF] -------------End-------------
2025-08-10 03:17:52.424 +08:00 [INF] Starting web host
2025-08-10 03:17:54.719 +08:00 [INF] 机器人启动成功！我是FreeDomQA。
2025-08-10 03:17:55.199 +08:00 [INF] 机器人消息发送结果：true
2025-08-10 03:17:55.589 +08:00 [INF] Service 订单过期 is starting.
2025-08-10 03:17:55.600 +08:00 [INF] Service 更新汇率 is starting.
2025-08-10 03:17:55.601 +08:00 [INF] Service 订单通知 is starting.
2025-08-10 03:17:55.602 +08:00 [INF] Background Service 发送订单通知 is starting.
2025-08-10 03:17:55.613 +08:00 [INF] Service TRC20订单检测 is starting.
2025-08-10 03:17:55.614 +08:00 [INF] Service TRX订单检测 is starting.
2025-08-10 03:17:55.614 +08:00 [INF] Service EVM基本币订单检测 is starting.
2025-08-10 03:17:55.615 +08:00 [INF] Service EVM代币订单检测 is starting.
2025-08-10 03:17:55.616 +08:00 [INF] Service TRON归集任务 is starting.
2025-08-10 03:17:55.807 +08:00 [INF] Now listening on: http://localhost:5000
2025-08-10 03:17:55.810 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-10 03:17:55.810 +08:00 [INF] Hosting environment: Production
2025-08-10 03:17:55.810 +08:00 [INF] Content root path: /www/wwwroot/**************/epay/tokenpay
2025-08-10 03:17:58.648 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 03:17:58.891 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.42
2025-08-10 03:17:59.549 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 03:17:59.555 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 04:17:58.600 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 04:17:59.036 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.41
2025-08-10 04:17:59.044 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 04:17:59.049 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 05:17:58.604 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 05:17:59.065 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.41
2025-08-10 05:17:59.073 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 05:17:59.080 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 06:17:58.602 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 06:17:59.046 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.41
2025-08-10 06:17:59.055 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 06:17:59.064 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 07:17:58.605 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 07:17:59.006 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.41
2025-08-10 07:17:59.015 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 07:17:59.022 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 08:17:58.606 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 08:17:59.052 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.41
2025-08-10 08:17:59.059 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 08:17:59.066 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 09:17:58.601 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 09:17:59.081 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.42
2025-08-10 09:17:59.088 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 09:17:59.093 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 10:17:58.602 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 10:17:59.225 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.43
2025-08-10 10:17:59.233 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 10:17:59.239 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 11:17:58.604 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 11:17:59.149 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.44
2025-08-10 11:17:59.158 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 11:17:59.162 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 12:17:58.606 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 12:17:59.030 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.44
2025-08-10 12:17:59.036 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 12:17:59.040 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 13:17:58.604 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 13:17:59.094 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.45
2025-08-10 13:17:59.102 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 13:17:59.110 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 14:17:58.603 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 14:17:59.025 +08:00 [WRN] TRX 汇率获取失败！错误信息：; Call failed with status code 403 (Forbidden): GET https://www.okx.com/v3/c2c/otc-ticker/quotedPrice?side=buy&quoteCurrency=CNY&baseCurrency=TRX
2025-08-10 14:17:59.053 +08:00 [WRN] USDT 汇率获取失败！错误信息：; Call failed with status code 403 (Forbidden): GET https://www.okx.com/v3/c2c/otc-ticker/quotedPrice?side=buy&quoteCurrency=CNY&baseCurrency=USDT
2025-08-10 14:17:59.054 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 15:17:58.603 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 15:17:59.093 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.44
2025-08-10 15:17:59.101 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 15:17:59.110 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 16:17:58.600 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 16:17:59.043 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.43
2025-08-10 16:17:59.050 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 16:17:59.058 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 17:17:58.602 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 17:17:59.027 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.44
2025-08-10 17:17:59.033 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 17:17:59.040 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 18:17:58.604 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 18:17:59.112 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.45
2025-08-10 18:17:59.120 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 18:17:59.127 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 19:17:58.601 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 19:17:59.078 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.44
2025-08-10 19:17:59.089 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 19:17:59.096 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 20:17:58.605 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 20:17:59.102 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.44
2025-08-10 20:17:59.109 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 20:17:59.115 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 21:17:58.603 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 21:17:59.158 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.46
2025-08-10 21:17:59.166 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 21:17:59.173 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 22:17:58.601 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 22:17:59.106 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.45
2025-08-10 22:17:59.169 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 22:17:59.177 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 23:17:58.604 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 23:17:59.089 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.45
2025-08-10 23:17:59.097 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 23:17:59.102 +08:00 [INF] ------------------结束更新汇率------------------
2:25:18.066 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 12:25:18.284 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.44
2025-08-10 12:25:18.292 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 12:25:18.298 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 13:23:58.065 +08:00 [INF] 订单["68989660-c5ee-b4c5-0055-248637c1e64c"]过期了！
2025-08-10 13:25:18.063 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 13:25:18.272 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.44
2025-08-10 13:25:18.280 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 13:25:18.284 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 13:30:08.065 +08:00 [INF] 订单["689897ce-c5ee-b4c5-0055-24873e60343e"]过期了！
2025-08-10 14:19:58.067 +08:00 [INF] 订单["6898a385-c5ee-b4c5-0055-24886786703d"]过期了！
2025-08-10 14:25:18.063 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 14:25:18.254 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.44
2025-08-10 14:25:18.262 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 14:25:18.269 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 15:25:18.068 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 15:25:18.290 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.44
2025-08-10 15:25:18.298 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 15:25:18.305 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 16:08:48.066 +08:00 [INF] 订单["6898bd01-c5ee-b4c5-0055-2489056db43a"]过期了！
2025-08-10 16:13:38.068 +08:00 [INF] 订单["6898be26-c5ee-b4c5-0055-248a7b379324"]过期了！
2025-08-10 16:25:18.062 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 16:25:18.256 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.44
2025-08-10 16:25:18.262 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 16:25:18.268 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 17:25:18.063 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 17:25:18.252 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.44
2025-08-10 17:25:18.260 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 17:25:18.272 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 17:35:48.064 +08:00 [INF] 订单["6898d16b-c5ee-b4c5-0055-248b38fe64bc"]过期了！
2025-08-10 18:25:18.066 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 18:25:18.300 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.45
2025-08-10 18:25:18.320 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 18:25:18.329 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 18:27:38.067 +08:00 [INF] 订单["6898dd8f-c5ee-b4c5-0055-248c1f6416d4"]过期了！
2025-08-10 18:27:58.066 +08:00 [INF] 订单["6898dd9d-c5ee-b4c5-0055-248d43dfff36"]过期了！
2025-08-10 19:25:18.065 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 19:25:18.263 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.45
2025-08-10 19:25:18.276 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 19:25:18.281 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 20:25:18.065 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 20:25:18.284 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.45
2025-08-10 20:25:18.293 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 20:25:18.300 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 21:25:18.067 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 21:25:18.277 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.46
2025-08-10 21:25:18.286 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 21:25:18.293 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 22:25:18.065 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 22:25:18.275 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.45
2025-08-10 22:25:18.281 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 22:25:18.287 +08:00 [INF] ------------------结束更新汇率------------------
2025-08-10 23:25:18.068 +08:00 [INF] ------------------开始更新汇率------------------
2025-08-10 23:25:18.277 +08:00 [INF] 更新汇率，TRX=>"CNY" = 2.45
2025-08-10 23:25:18.285 +08:00 [INF] 更新汇率，USDT=>"CNY" = 7.18
2025-08-10 23:25:18.290 +08:00 [INF] ------------------结束更新汇率------------------
