2025-08-09 23:08:09.325 +08:00 [INF] -------------TokenPay Info-------------
2025-08-09 23:08:09.879 +08:00 [INF] File Version: *******
2025-08-09 23:08:09.880 +08:00 [INF] Product Version: 1.1.0+f5f6f70ae02862a6a42fa0328dcacdd6c929f21e
2025-08-09 23:08:09.880 +08:00 [INF] -------------System Info-------------
2025-08-09 23:08:09.880 +08:00 [INF] Platform: Linux
2025-08-09 23:08:09.881 +08:00 [INF] Architecture: "X64"
2025-08-09 23:08:09.912 +08:00 [INF] Description: Ubuntu 22.04.5 LTS
2025-08-09 23:08:09.912 +08:00 [INF] ProcessArchitecture: "X64"
2025-08-09 23:08:09.913 +08:00 [INF] X64: Yes
2025-08-09 23:08:09.914 +08:00 [INF] CPU CORE: 4
2025-08-09 23:08:09.916 +08:00 [INF] HostName: C20240314128253
2025-08-09 23:08:09.918 +08:00 [INF] OSVersion: Unix **********
2025-08-09 23:08:09.923 +08:00 [INF] IsServerGC: false
2025-08-09 23:08:09.924 +08:00 [INF] IsConcurrent: false
2025-08-09 23:08:09.925 +08:00 [INF] LatencyMode: "Interactive"
2025-08-09 23:08:10.200 +08:00 [INF] -------------AppSettings-------------
2025-08-09 23:08:10.201 +08:00 [INF] 支持的币种: ["TRX","USDT_TRC20"]
2025-08-09 23:08:10.207 +08:00 [INF] 币种小数点位数: 
2025-08-09 23:08:10.209 +08:00 [INF] 	TRX=2
2025-08-09 23:08:10.209 +08:00 [INF] 	USDT_TRC20=4
2025-08-09 23:08:10.209 +08:00 [INF] 启用动态地址: false
2025-08-09 23:08:10.209 +08:00 [INF] 启用动态金额: false
2025-08-09 23:08:10.209 +08:00 [INF] 动态金额生效状态: false
2025-08-09 23:08:10.209 +08:00 [INF] 启用波场自动归集: false
2025-08-09 23:08:10.210 +08:00 [INF] -------------End-------------
2025-08-09 23:08:11.294 +08:00 [INF] Starting web host
2025-08-09 23:08:13.121 +08:00 [ERR] 机器人连接失败！
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
2025-08-09 23:08:13.200 +08:00 [FTL] Host terminated unexpectedly
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitIEnumerable(IEnumerableCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey, Func`2)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider, Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost)
   at Microsoft.AspNetCore.Builder.WebApplication.Run(String )
   at Program.<Main>$(String[] args)
2025-08-09 23:08:31.951 +08:00 [INF] -------------TokenPay Info-------------
2025-08-09 23:08:32.346 +08:00 [INF] File Version: *******
2025-08-09 23:08:32.346 +08:00 [INF] Product Version: 1.1.0+f5f6f70ae02862a6a42fa0328dcacdd6c929f21e
2025-08-09 23:08:32.347 +08:00 [INF] -------------System Info-------------
2025-08-09 23:08:32.347 +08:00 [INF] Platform: Linux
2025-08-09 23:08:32.348 +08:00 [INF] Architecture: "X64"
2025-08-09 23:08:32.372 +08:00 [INF] Description: Ubuntu 22.04.5 LTS
2025-08-09 23:08:32.372 +08:00 [INF] ProcessArchitecture: "X64"
2025-08-09 23:08:32.372 +08:00 [INF] X64: Yes
2025-08-09 23:08:32.373 +08:00 [INF] CPU CORE: 4
2025-08-09 23:08:32.374 +08:00 [INF] HostName: C20240314128253
2025-08-09 23:08:32.376 +08:00 [INF] OSVersion: Unix **********
2025-08-09 23:08:32.380 +08:00 [INF] IsServerGC: false
2025-08-09 23:08:32.381 +08:00 [INF] IsConcurrent: false
2025-08-09 23:08:32.381 +08:00 [INF] LatencyMode: "Interactive"
2025-08-09 23:08:32.631 +08:00 [INF] -------------AppSettings-------------
2025-08-09 23:08:32.632 +08:00 [INF] 支持的币种: ["TRX","USDT_TRC20"]
2025-08-09 23:08:32.638 +08:00 [INF] 币种小数点位数: 
2025-08-09 23:08:32.639 +08:00 [INF] 	TRX=2
2025-08-09 23:08:32.639 +08:00 [INF] 	USDT_TRC20=4
2025-08-09 23:08:32.639 +08:00 [INF] 启用动态地址: false
2025-08-09 23:08:32.639 +08:00 [INF] 启用动态金额: false
2025-08-09 23:08:32.639 +08:00 [INF] 动态金额生效状态: false
2025-08-09 23:08:32.640 +08:00 [INF] 启用波场自动归集: false
2025-08-09 23:08:32.640 +08:00 [INF] -------------End-------------
2025-08-09 23:08:33.574 +08:00 [INF] Starting web host
2025-08-09 23:08:35.271 +08:00 [ERR] 机器人连接失败！
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
2025-08-09 23:08:35.326 +08:00 [FTL] Host terminated unexpectedly
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitIEnumerable(IEnumerableCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey, Func`2)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider, Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost)
   at Microsoft.AspNetCore.Builder.WebApplication.Run(String )
   at Program.<Main>$(String[] args)
2025-08-09 23:30:29.525 +08:00 [INF] -------------TokenPay Info-------------
2025-08-09 23:30:29.943 +08:00 [INF] File Version: *******
2025-08-09 23:30:29.943 +08:00 [INF] Product Version: 1.1.0+f5f6f70ae02862a6a42fa0328dcacdd6c929f21e
2025-08-09 23:30:29.944 +08:00 [INF] -------------System Info-------------
2025-08-09 23:30:29.944 +08:00 [INF] Platform: Linux
2025-08-09 23:30:29.944 +08:00 [INF] Architecture: "X64"
2025-08-09 23:30:29.970 +08:00 [INF] Description: Ubuntu 22.04.5 LTS
2025-08-09 23:30:29.970 +08:00 [INF] ProcessArchitecture: "X64"
2025-08-09 23:30:29.970 +08:00 [INF] X64: Yes
2025-08-09 23:30:29.971 +08:00 [INF] CPU CORE: 4
2025-08-09 23:30:29.972 +08:00 [INF] HostName: C20240314128253
2025-08-09 23:30:29.974 +08:00 [INF] OSVersion: Unix **********
2025-08-09 23:30:29.978 +08:00 [INF] IsServerGC: false
2025-08-09 23:30:29.979 +08:00 [INF] IsConcurrent: false
2025-08-09 23:30:29.980 +08:00 [INF] LatencyMode: "Interactive"
2025-08-09 23:30:30.221 +08:00 [INF] -------------AppSettings-------------
2025-08-09 23:30:30.222 +08:00 [INF] 支持的币种: ["TRX","USDT_TRC20"]
2025-08-09 23:30:30.227 +08:00 [INF] 币种小数点位数: 
2025-08-09 23:30:30.229 +08:00 [INF] 	TRX=2
2025-08-09 23:30:30.229 +08:00 [INF] 	USDT_TRC20=4
2025-08-09 23:30:30.229 +08:00 [INF] 启用动态地址: false
2025-08-09 23:30:30.229 +08:00 [INF] 启用动态金额: false
2025-08-09 23:30:30.229 +08:00 [INF] 动态金额生效状态: false
2025-08-09 23:30:30.230 +08:00 [INF] 启用波场自动归集: false
2025-08-09 23:30:30.230 +08:00 [INF] -------------End-------------
2025-08-09 23:30:31.309 +08:00 [INF] Starting web host
2025-08-09 23:30:32.738 +08:00 [ERR] 机器人连接失败！
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
2025-08-09 23:30:32.799 +08:00 [FTL] Host terminated unexpectedly
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitIEnumerable(IEnumerableCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey, Func`2)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider, Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost)
   at Microsoft.AspNetCore.Builder.WebApplication.Run(String )
   at Program.<Main>$(String[] args)
2025-08-09 23:42:50.709 +08:00 [INF] -------------TokenPay Info-------------
2025-08-09 23:42:51.140 +08:00 [INF] File Version: *******
2025-08-09 23:42:51.141 +08:00 [INF] Product Version: 1.1.0+f5f6f70ae02862a6a42fa0328dcacdd6c929f21e
2025-08-09 23:42:51.142 +08:00 [INF] -------------System Info-------------
2025-08-09 23:42:51.142 +08:00 [INF] Platform: Linux
2025-08-09 23:42:51.143 +08:00 [INF] Architecture: "X64"
2025-08-09 23:42:51.172 +08:00 [INF] Description: Ubuntu 22.04.5 LTS
2025-08-09 23:42:51.172 +08:00 [INF] ProcessArchitecture: "X64"
2025-08-09 23:42:51.172 +08:00 [INF] X64: Yes
2025-08-09 23:42:51.173 +08:00 [INF] CPU CORE: 4
2025-08-09 23:42:51.175 +08:00 [INF] HostName: C20240314128253
2025-08-09 23:42:51.177 +08:00 [INF] OSVersion: Unix **********
2025-08-09 23:42:51.181 +08:00 [INF] IsServerGC: false
2025-08-09 23:42:51.183 +08:00 [INF] IsConcurrent: false
2025-08-09 23:42:51.184 +08:00 [INF] LatencyMode: "Interactive"
2025-08-09 23:42:51.488 +08:00 [INF] -------------AppSettings-------------
2025-08-09 23:42:51.488 +08:00 [INF] 支持的币种: ["TRX","USDT_TRC20"]
2025-08-09 23:42:51.494 +08:00 [INF] 币种小数点位数: 
2025-08-09 23:42:51.495 +08:00 [INF] 	TRX=2
2025-08-09 23:42:51.495 +08:00 [INF] 	USDT_TRC20=4
2025-08-09 23:42:51.495 +08:00 [INF] 启用动态地址: false
2025-08-09 23:42:51.496 +08:00 [INF] 启用动态金额: false
2025-08-09 23:42:51.496 +08:00 [INF] 动态金额生效状态: false
2025-08-09 23:42:51.496 +08:00 [INF] 启用波场自动归集: false
2025-08-09 23:42:51.496 +08:00 [INF] -------------End-------------
2025-08-09 23:42:52.619 +08:00 [INF] Starting web host
2025-08-09 23:42:54.340 +08:00 [ERR] 机器人连接失败！
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
2025-08-09 23:42:54.411 +08:00 [FTL] Host terminated unexpectedly
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitIEnumerable(IEnumerableCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey, Func`2)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider, Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost)
   at Microsoft.AspNetCore.Builder.WebApplication.Run(String )
   at Program.<Main>$(String[] args)
2025-08-09 23:42:54.467 +08:00 [INF] Starting web host
2025-08-09 23:42:56.196 +08:00 [ERR] 机器人连接失败！
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
2025-08-09 23:42:56.267 +08:00 [FTL] Host terminated unexpectedly
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitIEnumerable(IEnumerableCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey, Func`2)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider, Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost)
   at Microsoft.AspNetCore.Builder.WebApplication.Run(String )
   at Program.<Main>$(String[] args)
2025-08-09 23:42:56.497 +08:00 [INF] Starting web host
2025-08-09 23:42:58.117 +08:00 [ERR] 机器人连接失败！
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
2025-08-09 23:42:58.180 +08:00 [FTL] Host terminated unexpectedly
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitIEnumerable(IEnumerableCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey, Func`2)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider, Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost)
   at Microsoft.AspNetCore.Builder.WebApplication.Run(String )
   at Program.<Main>$(String[] args)
2025-08-09 23:42:57.969 +08:00 [INF] -------------TokenPay Info-------------
2025-08-09 23:42:58.381 +08:00 [INF] File Version: *******
2025-08-09 23:42:58.381 +08:00 [INF] Product Version: 1.1.0+f5f6f70ae02862a6a42fa0328dcacdd6c929f21e
2025-08-09 23:42:58.382 +08:00 [INF] -------------System Info-------------
2025-08-09 23:42:58.382 +08:00 [INF] Platform: Linux
2025-08-09 23:42:58.383 +08:00 [INF] Architecture: "X64"
2025-08-09 23:42:58.419 +08:00 [INF] Description: Ubuntu 22.04.5 LTS
2025-08-09 23:42:58.419 +08:00 [INF] ProcessArchitecture: "X64"
2025-08-09 23:42:58.419 +08:00 [INF] X64: Yes
2025-08-09 23:42:58.420 +08:00 [INF] CPU CORE: 4
2025-08-09 23:42:58.422 +08:00 [INF] HostName: C20240314128253
2025-08-09 23:42:58.425 +08:00 [INF] OSVersion: Unix **********
2025-08-09 23:42:58.431 +08:00 [INF] IsServerGC: false
2025-08-09 23:42:58.432 +08:00 [INF] IsConcurrent: false
2025-08-09 23:42:58.433 +08:00 [INF] LatencyMode: "Interactive"
2025-08-09 23:42:58.713 +08:00 [INF] -------------AppSettings-------------
2025-08-09 23:42:58.714 +08:00 [INF] 支持的币种: ["TRX","USDT_TRC20"]
2025-08-09 23:42:58.720 +08:00 [INF] 币种小数点位数: 
2025-08-09 23:42:58.722 +08:00 [INF] 	TRX=2
2025-08-09 23:42:58.722 +08:00 [INF] 	USDT_TRC20=4
2025-08-09 23:42:58.722 +08:00 [INF] 启用动态地址: false
2025-08-09 23:42:58.722 +08:00 [INF] 启用动态金额: false
2025-08-09 23:42:58.722 +08:00 [INF] 动态金额生效状态: false
2025-08-09 23:42:58.722 +08:00 [INF] 启用波场自动归集: false
2025-08-09 23:42:58.722 +08:00 [INF] -------------End-------------
2025-08-09 23:42:59.929 +08:00 [INF] Starting web host
2025-08-09 23:43:01.740 +08:00 [ERR] 机器人连接失败！
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
2025-08-09 23:43:01.833 +08:00 [FTL] Host terminated unexpectedly
Flurl.Http.FlurlHttpException: Call failed with status code 401 (Unauthorized): GET https://api.telegram.org/bot1234567890:AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/getMe
   at Flurl.Http.FlurlClient.HandleExceptionAsync(FlurlCall call, Exception ex, CancellationToken token)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.FlurlClient.SendAsync(IFlurlRequest request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at Flurl.Http.ResponseExtensions.ReceiveJson[T](Task`1 response)
   at TokenPay.Helper.TelegramBot.GetMeAsync(String TelegramApiHost)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__4(IServiceProvider s)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitIEnumerable(IEnumerableCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite, TArgument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite, RuntimeResolverContext)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey, Func`2)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider, Type)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.Extensions.Hosting.Internal.Host.StartAsync(CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.RunAsync(IHost, CancellationToken )
   at Microsoft.Extensions.Hosting.HostingAbstractionsHostExtensions.Run(IHost)
   at Microsoft.AspNetCore.Builder.WebApplication.Run(String )
   at Program.<Main>$(String[] args)
