#!/usr/bin/env bash
# 停止 TokenPay 服务
set -e
DIR=$(cd "$(dirname "$0")" && pwd)
cd "$DIR"
if [ -f tokenpay.pid ]; then
  PID=$(cat tokenpay.pid)
  if kill -0 "$PID" 2>/dev/null; then
    kill "$PID" || true
    sleep 1
    if kill -0 "$PID" 2>/dev/null; then
      echo "进程未立即退出，尝试强制结束..."
      kill -9 "$PID" || true
    fi
  fi
  rm -f tokenpay.pid
  echo "TokenPay 已停止"
else
  echo "未发现 tokenpay.pid，尝试根据进程名结束..."
  pkill -f 'TokenPay|tokenpay' || true
fi
