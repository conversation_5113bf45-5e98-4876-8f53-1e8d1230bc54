# TokenPay 部署（epay/tokenpay）

- 本目录已放置 TokenPay v1.1.0 发布包（优先 Linux x64）。
- 启动脚本：`./run_tokenpay.sh`（自动寻找并执行二进制文件）。
- 如需自定义监听地址或端口，可在脚本内追加参数 `--urls http://0.0.0.0:5080`。
- 建议在宝塔中创建站点反向代理到上述端口，并配置 HTTPS。

## 回调配置
- 在 TokenPay 中将回调地址配置为：`https://你的主站域名/epay/tokenpay_notify.php`
- 回调签名密钥需与后台“支付配置”里 `回调签名密钥` 一致。

## 后台配置
- 系统设置 → 支付配置：开启“加密货币支付”，填写收银台地址与回调密钥。

## Etherscan V2 适配
- v1.1.0 版本适配 Etherscan V2，需要使用新版 `EVMChains.json` 并配置 `ChainId` 与新 `ApiKey`（参见发行说明）。

