#!/usr/bin/env bash
# 启动 TokenPay 服务（建议使用宝塔反向代理对外提供 HTTPS）
set -e
DIR=$(cd "$(dirname "$0")" && pwd)
cd "$DIR"
# 可执行文件名自动探测（常见名：TokenPay、tokenpay、TokenPay.Server）
BIN=$(ls -1 | grep -E '^(TokenPay|tokenpay|TokenPay\.Server)$' | head -n1)
if [ -z "$BIN" ]; then
  # 尝试在发布目录中查找
  BIN=$(find . -maxdepth 2 -type f -perm -u+x -printf "%f\n" | grep -E 'TokenPay|tokenpay' | head -n1)
fi
if [ -z "$BIN" ]; then echo "未找到可执行文件，请检查解压内容"; exit 1; fi
# 后台运行，绑定到本机 5080 端口
nohup ./$BIN --urls http://127.0.0.1:5080 > tokenpay.out 2>&1 &
echo $! > tokenpay.pid
echo "TokenPay 已启动，PID=$(cat tokenpay.pid)，监听 http://127.0.0.1:5080"
