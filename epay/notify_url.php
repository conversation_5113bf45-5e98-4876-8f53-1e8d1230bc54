<?php
@header('Content-Type: text/html; charset=UTF-8');
include("../confing/common.php");
require_once("epay/notify.class.php");
$alipayNotify = new AlipayNotify($alipay_config);
$verify_result = $alipayNotify->verifyNotify();
if($verify_result) {
	$out_trade_no=trim(strip_tags(daddslashes($_GET['out_trade_no'])));
	$trade_no=trim(strip_tags(daddslashes($_GET['trade_no'])));
	$trade_status=trim(strip_tags(daddslashes($_GET['trade_status'])));// 交易状态
	$name=trim(strip_tags(daddslashes($_GET['name'])));
	$money=trim(strip_tags(daddslashes($_GET['money'])));
	$pid=trim(strip_tags(daddslashes($_GET['pid'])));
	$type = $_GET['type'];
	
	// 加锁查询本地订单
	$srow=$DB->get_row("SELECT * FROM qingka_wangke_pay WHERE `out_trade_no`='{$out_trade_no}' LIMIT 1 FOR UPDATE");
	
    // 将金额转换为分进行精确比较
    $expectedCents = (int)round(floatval($srow['money']) * 100);
    $paidCents = (int)round(floatval($money) * 100);

    if ($trade_status == 'TRADE_SUCCESS' && $srow['status']==0 && $expectedCents === $paidCents) {
		// 付款完成：更新支付状态
		$DB->query("update `qingka_wangke_pay` set `status` ='1',`endtime` ='$date',`trade_no`='{$trade_no}' where `out_trade_no`='{$out_trade_no}'");
        echo "success";	
        exit();	
    }else{
		// 非成功或金额不一致：记录交易号与时间，返回成功保证平台不重复通知
		$DB->query("update `qingka_wangke_pay` set `endtime` ='$date',`trade_no`='{$trade_no}' where `out_trade_no`='{$out_trade_no}'");
	    echo "success";	
        exit();	
	}
}
else {
// 验签失败
echo "fail";
}
?>