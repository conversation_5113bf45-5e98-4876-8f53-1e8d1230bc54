<?php
include ('./confing/common.php');

// 文件ID到文件路径的映射
$fileMap = [
    1 => "/www/wwwroot/**************/index/downloads/FreeDom模板（5.28）.zip",
    2 => "/www/wwwroot/**************/index/downloads/教程很重要套娃鲸鱼.zip",
    3 => "/www/wwwroot/**************/index/downloads/新版运动世界对接插件.zip",
    4 => "/www/wwwroot/**************/index/downloads/同系统对接雷电_v1.1.zip",
    5 => "/www/wwwroot/**************/index/downloads/APPUI打卡无限套娃.zip",
    6 => "/www/wwwroot/**************/index/downloads/盘古套娃对接.zip",
    7 => "/www/wwwroot/**************/index/downloads/FD独家项目改密，暂停，日志教程.txt",
    8 => "/www/wwwroot/**************/index/downloads/FDjgjk.php",
    // 添加更多文件映射
];

// 获取请求中的文件ID，支持GET和POST
$fileId = isset($_REQUEST['file_id']) ? intval($_REQUEST['file_id']) : 0;

// 检查文件ID是否有效
if (!array_key_exists($fileId, $fileMap)) {
    exit('Invalid file ID.');
}

// 获取文件路径
$filePath = $fileMap[$fileId];

// 检查用户权限
function checkUserPermission($userrow, $fileId) {
    // 示例权限验证逻辑，根据实际情况调整
    if ($fileId == 1) {
        wlog($userrow['uid'],"资料下载","用户资料下载，ID：{$fileId}",0);
        return $userrow['money'] >= 1000 && $userrow['addprice'] == 0.2;
    } elseif (in_array($fileId, [2, 3, 4, 5, 6, 7, 8])) {
        wlog($userrow['uid'],"资料下载","用户资料下载，ID：{$fileId}",0);
        return $userrow['zcz'] >= 500 && $userrow['addprice'] == 0.2 && $userrow['money'] >= 20;
    }
    // 添加更多文件的权限验证
    return false;
}

// 验证用户权限
if (!checkUserPermission($userrow, $fileId)) {
    // 用户无权限时，创建一个文本文件并提供下载
    header('Content-Description: File Transfer');
    header('Content-Type: text/plain');
    header('Content-Disposition: attachment; filename="无权限下载.txt"');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . strlen("您没有权限下载该文件。"));
    ob_clean();
    flush();
    exit("您没有权限下载该文件。") ;
}

// 检查文件是否存在
if (!file_exists($filePath)) {
    exit('文件不存在！');
}

// 设置适当的头信息，以便浏览器识别下载操作
header('Content-Description: File Transfer');
header('Content-Type: application/octet-stream');
$filename = basename($filePath);
$encodedFilename = rawurlencode($filename); // 正确进行URL编码
header("Content-Disposition: attachment; filename*=UTF-8''{$encodedFilename}");
header('Expires: 0');
header('Cache-Control: must-revalidate');
header('Pragma: public');
header('Content-Length: ' . filesize($filePath));

// 清除输出缓冲区并关闭输出缓冲
ob_clean();
flush();

// 使用流式传输读取文件内容，减少内存消耗[^6^]
$file = fopen($filePath, 'rb');
if (!$file) {
    die('File open failed.');
}
while (!feof($file)) {
    $buffer = fread($file, 1024 * 8); // 每次读取8KB，可根据实际情况调整
    echo $buffer;
    ob_flush(); // 刷新输出缓冲区
    flush();    // 刷新PHP缓冲区
}
fclose($file);

// 退出脚本
exit;
?>