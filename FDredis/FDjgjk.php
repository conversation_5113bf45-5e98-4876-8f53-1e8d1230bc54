<?php
include(__DIR__ . '/../confing/common.php');

// 脚本配置
$config = [
    'enable_incremental' => false,   // 启用增量同步
    'cache_expire' => 86400,        // 缓存24小时
    'enable_email_notify' => true,  // 邮件通知
    'notify_threshold' => 5,        // 通知阈值
    'batch_size' => 50,            // 批量处理大小
    'show_progress' => true,       // 禁用进度条适合宝塔计划任务
    'verbose_mode' => true,        // 简化输出模式
];

// 价格规则（hids为hid数组，multiplier为价格加价，仅支持倍率加价）
$price_rules = [
    ['hids' => [120], 'multiplier' => 1.05],// 输入符合情况一的hid。价格加价情况一：类似暗网的平台返回的价格，比如源台定价0.2，但是返回的价格是1，即源台单价*5的情况
    ['hids' => [118,100], 'multiplier' => 1.05 * 5],// 输入符合情况二的hid。价格加价情况二：类似FreeDom平台返回的价格，比如源台定价0.2，返回的价格也是0.2的情况。
    ['hids' => [104], 'multiplier' => 1.1],// 自定义情况
];

// 初始化
$script_start = microtime(true);
$updates = [];
$stats = ['total' => 0, 'success' => 0, 'failed' => 0, 'processed' => 0];

// 检测运行环境
$is_cron = !isset($_SERVER['TERM']) || empty($_SERVER['TERM']);
if ($is_cron) {
    $config['show_progress'] = false;
    $config['verbose_mode'] = false;
}

echo "========================================\n";
echo "货源监控脚本启动\n";
echo "开始时间: " . date('Y-m-d H:i:s') . "\n";
echo "运行环境: " . ($is_cron ? "计划任务" : "交互终端") . "\n";
echo "========================================\n";

// Redis连接
try {
    $redis = new Redis();
    $redis->connect('127.0.0.1', 6379);
    echo "redis连接成功\n";
} catch (Exception $e) {
    echo "Redis连接失败使用全量同步\n";
    $config['enable_incremental'] = false;
}

// 辅助函数
function getProductFingerprint($product) {
    return md5($product['price'] . '|' . $product['status'] . '|' . $product['content']);
}

function getCachedData($redis, $hid) {
    if (!$redis) return null;
    $cached = $redis->get("supplier_data_{$hid}");
    return $cached ? json_decode($cached, true) : null;
}

function setCachedData($redis, $hid, $data) {
    if (!$redis) return false;
    return $redis->setex("supplier_data_{$hid}", 86400, json_encode($data));
}

function applyPriceRule($hid, $price, $rules) {
    foreach ($rules as $rule) {
        if (in_array($hid, $rule['hids'])) {
            return $price * $rule['multiplier'];
        }
    }
    return $price;
}

// 分析已对接商品的前缀和分类模式
function analyzeExistingProducts($local_products, $source_fenlei) {
    $patterns = [];

    foreach ($local_products as $cid => $product) {
        // 提取可能的前缀假设前缀后面跟着原商品名的一部分
        $local_name = $product['name'];
        $local_fenlei = $product['fenlei'];
        $sort = $product['sort'] ?? 0;

        // 存储模式信息
        if (!isset($patterns[$local_fenlei])) {
            $patterns[$local_fenlei] = [
                'count' => 0,
                'max_sort' => 0,
                'prefixes' => [],
                'sample_names' => []
            ];
        }

        $patterns[$local_fenlei]['count']++;
        $patterns[$local_fenlei]['max_sort'] = max($patterns[$local_fenlei]['max_sort'], $sort);
        $patterns[$local_fenlei]['sample_names'][] = $local_name;
    }

    // 选择商品数量最多的分类作为目标分类
    if (empty($patterns)) return null;

    $target_fenlei = array_keys($patterns)[0];
    $max_count = 0;

    foreach ($patterns as $fenlei => $info) {
        if ($info['count'] > $max_count) {
            $max_count = $info['count'];
            $target_fenlei = $fenlei;
        }
    }

    // 分析前缀模式
    $sample_names = $patterns[$target_fenlei]['sample_names'];
    $common_prefix = extractCommonPrefix($sample_names);

    return [
        'target_fenlei' => $target_fenlei,
        'common_prefix' => $common_prefix,
        'max_sort' => $patterns[$target_fenlei]['max_sort'],
        'product_count' => $patterns[$target_fenlei]['count']
    ];
}

// 提取公共前缀
function extractCommonPrefix($names) {
    if (empty($names)) return '';

    // 简单的前缀提取逻辑
    $first_name = $names[0];
    $prefixes = [];

    // 尝试提取中文前缀常见的2-4个字符
    for ($len = 2; $len <= 6; $len++) {
        if (mb_strlen($first_name, 'UTF-8') > $len) {
            $potential_prefix = mb_substr($first_name, 0, $len, 'UTF-8');

            // 检查这个前缀是否在其他名称中也存在
            $match_count = 0;
            foreach ($names as $name) {
                if (mb_strpos($name, $potential_prefix, 0, 'UTF-8') === 0) {
                    $match_count++;
                }
            }

            // 如果超过一半的商品都有这个前缀认为是有效前缀
            if ($match_count >= count($names) * 0.6) {
                $prefixes[$len] = $potential_prefix;
            }
        }
    }

    // 返回最长的有效前缀
    if (!empty($prefixes)) {
        $max_len = max(array_keys($prefixes));
        return $prefixes[$max_len];
    }

    return '';
}

// 移除货源商品的原有前缀
function removeSourcePrefix($product_name) {
    $cleaned_name = $product_name;

    // 方法1: 移除括号内的前缀
    if (preg_match('/^(.+?)(.+)$/u', $cleaned_name, $matches)) {
        $cleaned_name = $matches[2];
    }
    // 方法2: 移除括号内的前缀
    elseif (preg_match('/^(.+?)(.+)$/u', $cleaned_name, $matches)) {
        $cleaned_name = $matches[2];
    }
    // 方法3: 移除()括号内的前缀
    elseif (preg_match('/^\((.+?)\)(.+)$/u', $cleaned_name, $matches)) {
        $cleaned_name = $matches[2];
    }
    // 方法4: 移除字母+连接符的前缀
    elseif (preg_match('/^[a-zA-Z]+[-_](.+)$/u', $cleaned_name, $matches)) {
        $cleaned_name = $matches[1];
    }
    // 方法5: 移除常见中文前缀
    else {
        $chinese_prefixes = ['代刷', '自营', '合作'];
        foreach ($chinese_prefixes as $prefix) {
            if (mb_strpos($cleaned_name, $prefix, 0, 'UTF-8') === 0) {
                $cleaned_name = mb_substr($cleaned_name, mb_strlen($prefix, 'UTF-8'), null, 'UTF-8');
                $cleaned_name = ltrim($cleaned_name, '-_ ');
                break;
            }
        }
    }

    // 移除开头的空格和特殊字符
    $cleaned_name = ltrim($cleaned_name, ' -_');

    // 如果清理后的名称为空或太短返回原名称
    if (empty($cleaned_name) || mb_strlen($cleaned_name, 'UTF-8') < 2) {
        return $product_name;
    }

    return $cleaned_name;
}

// 进度条显示函数
function showProgress($current, $total, $prefix = '', $width = 50) {
    global $config;

    // 如果禁用进度条只在完成时显示简单信息
    if (!$config['show_progress']) {
        if ($current >= $total) {
            echo " {$prefix}: {$total} 项处理完成\n";
        }
        return;
    }

    $percent = $total > 0 ? ($current / $total) * 100 : 0;
    $filled = intval($width * $current / max($total, 1));
    $empty = $width - $filled;

    $bar = str_repeat('', $filled) . str_repeat('', $empty);
    $progress = sprintf("\r%s [%s] %d/%d (%.1f%%)", $prefix, $bar, $current, $total, $percent);

    echo $progress;
    if ($current >= $total) {
        echo "\n";
    }
    flush();
}

// 获取需要监控的货源商
$check_hids = array_merge(...array_column($price_rules, 'hids'));
$hids_str = implode(',', $check_hids);

// 批量加载本地商品
echo "加载本地商品数据...\n";
$localProducts = [];
$result = $DB->query("SELECT cid, noun, name, status, docking, fenlei, sort FROM qingka_wangke_class WHERE docking IN ({$hids_str})");
while ($row = $DB->fetch($result)) {
    $localProducts[$row['docking']][$row['noun']] = $row;
}
echo "已加载 " . count($localProducts) . " 个货源商数据\n";

// 处理每个货源商
$suppliers = $DB->query("SELECT * FROM qingka_wangke_huoyuan WHERE hid IN ({$hids_str}) AND status=1");
$suppliers_list = [];
while ($supplier = $DB->fetch($suppliers)) {
    $suppliers_list[] = $supplier;
}

$total_suppliers = count($suppliers_list);
echo "\n开始处理 {$total_suppliers} 个货源商\n";

foreach ($suppliers_list as $index => $supplier) {
    $hid = $supplier['hid'];
    $stats['total']++;
    $current_supplier = $index + 1;

    // 显示货源商进度
    showProgress($current_supplier, $total_suppliers, " 货源商进度", 30);
    echo "处理: {$supplier['name']} (HID: {$hid})\n";
    
    // API调用
    $api_data = array("uid" => $supplier["user"], "key" => $supplier["pass"]);
    $api_result = get_url("{$supplier['url']}/api.php?act=getclass", $api_data);
    
    if (!$api_result || !($result = json_decode($api_result, true)) || !isset($result["data"])) {
        echo "API调用失败\n";
        $stats['failed']++;
        continue;
    }
    
    $data = $result["data"];
    $local = $localProducts[$hid] ?? [];
    $stats['success']++;
    
    // 增量同步检查
    if ($config['enable_incremental']) {
        $cached = getCachedData($redis, $hid);
        if ($cached) {
            $current_fps = [];
            foreach ($data as $product) {
                $current_fps[$product['cid']] = getProductFingerprint($product);
            }
            
            $cached_fps = $cached['fingerprints'] ?? [];
            $changed = [];
            
            foreach ($data as $product) {
                $cid = $product['cid'];
                if (!isset($cached_fps[$cid]) || $cached_fps[$cid] !== $current_fps[$cid]) {
                    $changed[] = $product;
                }
            }
            
            // 检查删除的商品
            foreach ($cached_fps as $cid => $fp) {
                if (!isset($current_fps[$cid]) && isset($local[$cid]) && $local[$cid]['status'] == 1) {
                    $updates[] = ['docking' => $hid, 'noun' => $cid, 'status' => 0, 'action' => 'offline'];
                }
            }
            
            $data = $changed;
            echo "增量同步: 处理 " . count($data) . " 个变化商品\n";
        }
        
        // 更新缓存
        $cache_data = ['timestamp' => time(), 'fingerprints' => []];
        foreach ($result["data"] as $product) {
            $cache_data['fingerprints'][$product['cid']] = getProductFingerprint($product);
        }
        setCachedData($redis, $hid, $cache_data);
    }
    
    if (empty($data)) {
        echo "无变化\n";
        continue;
    }
    
    // 按分类分组
    $by_fenlei = [];
    foreach ($data as $product) {
        $by_fenlei[$product['fenlei']][$product['cid']] = $product;
    }
    
    $update_count = $online_count = $offline_count = 0;
    
    // 处理每个分类
    foreach ($by_fenlei as $fenlei => $products) {
        // 检查是否已对接此分类
        $has_integrated = false;
        foreach ($products as $cid => $product) {
            if (isset($local[$cid])) {
                $has_integrated = true;
                break;
            }
        }
        
        if (!$has_integrated) continue;
        
        echo "   监控分类: {$fenlei}\n";

        // 分析已对接商品的模式前缀分类排序
        $pattern = analyzeExistingProducts($local, $fenlei);

        // 处理商品
        $total_products = count($products);
        $processed_products = 0;

        // 记录商品数量变化
        $cache_key = "supplier_product_count_{$hid}";
        $last_count = $config['enable_incremental'] && isset($redis) ? $redis->get($cache_key) : 0;
        if ($last_count && $last_count != $total_products) {
            $change = $total_products - $last_count;
            echo " 商品数量变化: {$last_count}  {$total_products} (" . ($change > 0 ? "+{$change}" : $change) . ")\n";
        }
        if ($config['enable_incremental'] && isset($redis)) {
            $redis->setex($cache_key, $config['cache_expire'], $total_products);
        }

        foreach ($products as $cid => $product) {
            $processed_products++;
            if ($config['show_progress'] && $total_products > 10) { // 只有商品数量较多时才显示进度
                showProgress($processed_products, $total_products, "    商品处理", 20);
            }
            $price = applyPriceRule($hid, $product['price'], $price_rules);

            if (!isset($local[$cid])) {
                // 新商品应用继承规则
                if ($pattern) {
                    $original_name = $product['name'] ?? "新商品_{$cid}";
                    // 移除货源商品的原有前缀
                    $cleaned_name = removeSourcePrefix($original_name);
                    // 应用本地前缀
                    $new_name = $pattern['common_prefix'] . $cleaned_name;
                    $target_fenlei = $pattern['target_fenlei'];
                    $new_sort = $pattern['max_sort'] + 1;

                    $updates[] = [
                        'price' => $price, 'content' => $product['content'],
                        'docking' => $hid, 'noun' => $cid, 'status' => 1,
                        'action' => 'create_and_online',
                        'name' => $new_name,
                        'fenlei' => $target_fenlei,
                        'sort' => $new_sort,
                        'kcid' => $product['kcid'] ?? ''
                    ];

                    // 更新模式中的最大sort避免重复
                    $pattern['max_sort'] = $new_sort;

                    if (!$config['verbose_mode'] && $total_products <= 10) { // 商品少时显示详细信息
                        echo "     新增商品: {$original_name}  {$new_name} (分类: {$target_fenlei}, sort: {$new_sort})\n";
                        if ($cleaned_name !== $original_name) {
                            echo "       清理前缀: {$original_name}  {$cleaned_name}\n";
                        }
                    }
                } else {
                    // 如果没有找到模式使用默认值
                    $updates[] = [
                        'price' => $price, 'content' => $product['content'],
                        'docking' => $hid, 'noun' => $cid, 'status' => 1,
                        'action' => 'create_and_online',
                        'name' => $product['name'] ?? "新商品_{$cid}",
                        'fenlei' => $fenlei,
                        'sort' => 0,
                        'kcid' => $product['kcid'] ?? ''
                    ];
                    echo "     新增商品: {$product['name']} (ID: {$cid}) - 使用默认设置\n";
                }
                $online_count++;
            } else {
                $localProduct = $local[$cid];

                if ($localProduct['status'] == 0) {
                    // 已存在商品恢复上架
                    $updates[] = [
                        'price' => $price, 'content' => $product['content'],
                        'docking' => $hid, 'noun' => $cid, 'status' => 1, 'action' => 'online'
                    ];
                    $online_count++;
                    if (!$config['verbose_mode'] && $total_products <= 10) {
                        echo "     恢复商品: {$localProduct['name']} (ID: {$cid})\n";
                    }
                } else {
                    // 已存在商品更新价格和内容
                    $updates[] = [
                        'price' => $price, 'content' => $product['content'],
                        'docking' => $hid, 'noun' => $cid, 'status' => 1, 'action' => 'update'
                    ];
                    $update_count++;
                }
            }
        }
        
        // 检查下架商品
        $api_cids = array_keys($products);
        foreach ($local as $cid => $localProduct) {
            if (!in_array($cid, $api_cids) && $localProduct['status'] == 1) {
                $found_in_api = false;
                foreach ($data as $check) {
                    if ($check['cid'] == $cid) {
                        $found_in_api = true;
                        break;
                    }
                }
                if (!$found_in_api) {
                    $updates[] = ['docking' => $hid, 'noun' => $cid, 'status' => 0, 'action' => 'offline'];
                    $offline_count++;
                }
            }
        }
    }
    
    if ($update_count || $online_count || $offline_count) {
        $create_count = count(array_filter($updates, fn($u) => $u['action'] == 'create_and_online' && $u['docking'] == $hid));
        echo "   更新:{$update_count} 上架:" . ($online_count - $create_count) . " 下架:{$offline_count} 新增:{$create_count}\n";
        $stats['processed']++;
    }
}

// 批量更新数据库
if (!empty($updates)) {
    echo "\n 批量更新数据库...\n";

    $by_action = ['online' => [], 'offline' => [], 'update' => [], 'create_and_online' => []];
    foreach ($updates as $update) {
        $by_action[$update['action']][] = $update;
    }

    // 批量上架
    if (!empty($by_action['online'])) {
        $batches = array_chunk($by_action['online'], $config['batch_size']);
        $total_batches = count($batches);
        echo " 批量上架处理中...\n";

        foreach ($batches as $batch_index => $batch) {
            showProgress($batch_index + 1, $total_batches, "上架进度", 25);
            $cases_price = $cases_content = $nouns = [];
            foreach ($batch as $update) {
                $noun = $DB->escape($update['noun']);
                $price = $DB->escape($update['price']);
                $content = $DB->escape($update['content']);
                $docking = $update['docking'];

                $cases_price[] = "WHEN noun = '{$noun}' AND docking = '{$docking}' THEN '{$price}'";
                $cases_content[] = "WHEN noun = '{$noun}' AND docking = '{$docking}' THEN '{$content}'";
                $nouns[] = "'{$noun}'";
            }

            $sql = "UPDATE qingka_wangke_class SET
                    price = CASE " . implode(' ', $cases_price) . " ELSE price END,
                    content = CASE " . implode(' ', $cases_content) . " ELSE content END,
                    status = 1
                    WHERE noun IN (" . implode(',', array_unique($nouns)) . ")";
            $DB->query($sql);
        }
        echo " 上架: " . count($by_action['online']) . " 个\n";
    }

    // 批量创建新商品
    if (!empty($by_action['create_and_online'])) {
        $batches = array_chunk($by_action['create_and_online'], $config['batch_size']);
        $total_batches = count($batches);
        echo " 批量创建新商品中...\n";

        foreach ($batches as $batch_index => $batch) {
            showProgress($batch_index + 1, $total_batches, "创建进度", 25);
            $values = [];
            foreach ($batch as $update) {
                $noun = $DB->escape($update['noun']);
                $name = $DB->escape($update['name']);
                $price = $DB->escape($update['price']);
                $content = $DB->escape($update['content']);
                $docking = $update['docking'];
                $fenlei = $DB->escape($update['fenlei']);
                $sort = $update['sort'];
                $kcid = $DB->escape($update['kcid']);

                $values[] = "('{$noun}', '{$name}', '{$price}', '{$content}', '{$docking}', '{$fenlei}', {$sort}, '{$kcid}', 1, '" . date('Y-m-d H:i:s') . "')";
            }

            if (!empty($values)) {
                $sql = "INSERT INTO qingka_wangke_class
                        (noun, name, price, content, docking, fenlei, sort, kcid, status, addtime)
                        VALUES " . implode(',', $values) . "
                        ON DUPLICATE KEY UPDATE
                        name = VALUES(name),
                        price = VALUES(price),
                        content = VALUES(content),
                        fenlei = VALUES(fenlei),
                        sort = VALUES(sort),
                        status = VALUES(status)";
                $DB->query($sql);
            }
        }
        echo " 新增: " . count($by_action['create_and_online']) . " 个\n";
    }

    // 批量下架
    if (!empty($by_action['offline'])) {
        $batches = array_chunk($by_action['offline'], 100);
        $total_batches = count($batches);
        echo " 批量下架处理中...\n";

        foreach ($batches as $batch_index => $batch) {
            showProgress($batch_index + 1, $total_batches, "下架进度", 25);
            $conditions = [];
            foreach ($batch as $update) {
                $noun = $DB->escape($update['noun']);
                $docking = $update['docking'];
                $conditions[] = "(noun = '{$noun}' AND docking = '{$docking}')";
            }
            $sql = "UPDATE qingka_wangke_class SET status = 0 WHERE " . implode(' OR ', $conditions);
            $DB->query($sql);
        }
        echo " 下架: " . count($by_action['offline']) . " 个\n";
    }

    // 批量更新
    if (!empty($by_action['update'])) {
        $batches = array_chunk($by_action['update'], $config['batch_size']);
        $total_batches = count($batches);
        echo " 批量更新价格中...\n";

        foreach ($batches as $batch_index => $batch) {
            showProgress($batch_index + 1, $total_batches, "更新进度", 25);
            $cases_price = $cases_content = $nouns = [];
            foreach ($batch as $update) {
                $noun = $DB->escape($update['noun']);
                $price = $DB->escape($update['price']);
                $content = $DB->escape($update['content']);
                $docking = $update['docking'];

                $cases_price[] = "WHEN noun = '{$noun}' AND docking = '{$docking}' THEN '{$price}'";
                $cases_content[] = "WHEN noun = '{$noun}' AND docking = '{$docking}' THEN '{$content}'";
                $nouns[] = "'{$noun}'";
            }

            $sql = "UPDATE qingka_wangke_class SET
                    price = CASE " . implode(' ', $cases_price) . " ELSE price END,
                    content = CASE " . implode(' ', $cases_content) . " ELSE content END
                    WHERE noun IN (" . implode(',', array_unique($nouns)) . ") AND status = 1";
            $DB->query($sql);
        }
        echo " 更新: " . count($by_action['update']) . " 个\n";
    }

    // 记录日志和邮件通知
    $total_changes = count($by_action['online']) + count($by_action['offline']) + count($by_action['create_and_online']);
    $total_operations = $total_changes + count($by_action['update']);

    if ($total_operations > 0) {
        $log_msg = "自动监控: 上架" . count($by_action['online']) . "个, 下架" . count($by_action['offline']) . "个, 新增" . count($by_action['create_and_online']) . "个, 更新" . count($by_action['update']) . "个";
        $DB->query("INSERT INTO qingka_wangke_log (uid, type, text, money, addtime, ip) VALUES (1, '系统监控', '{$log_msg}', 0, '" . date('Y-m-d H:i:s') . "', '127.0.0.1')");

        // 邮件通知 - 只在商品上架/下架/新增时发送更新操作不发送邮件
        if ($config['enable_email_notify'] && ($total_changes >= $config['notify_threshold'])) {
            echo "检测到重要变化 (上架/下架/新增: {$total_changes}个)准备发送邮件通知\n";
            $admin = $DB->get_row("SELECT email, notify FROM qingka_wangke_user WHERE uid='1'");
            if (!empty($admin['email'])) {
                $replacements = json_encode([
                    'online_count' => count($by_action['online']),
                    'offline_count' => count($by_action['offline']),
                    'create_count' => count($by_action['create_and_online']),
                    'update_count' => count($by_action['update']),
                    'monitor_time' => date('Y-m-d H:i:s')
                ], JSON_UNESCAPED_UNICODE);

                $DB->query("INSERT INTO qingka_wangke_email_queue (template_id, to_email, to_name, uid, replacements) VALUES ('monitor_report', '{$admin['email']}', '管理员', 1, '".$DB->escape($replacements)."')");
                echo "邮件通知已发送 (上架/下架/新增操作)\n";
            }
        } else if ($config['enable_email_notify'] && $total_changes > 0 && $total_changes < $config['notify_threshold']) {
            echo "变化数量 ({$total_changes}个) 未达到通知阈值 ({$config['notify_threshold']}个)不发送邮件\n";
        } else if ($config['enable_email_notify'] && $total_changes == 0 && count($by_action['update']) > 0) {
            echo "仅有价格更新操作 (" . count($by_action['update']) . "个)不发送邮件通知\n";
        }
    }
}

// 最终报告
$execution_time = round(microtime(true) - $script_start, 2);
$memory_peak = round(memory_get_peak_usage(true) / 1024 / 1024, 2);

echo "\n 执行完成\n";
echo " 耗时: {$execution_time}秒 |  内存: {$memory_peak}MB\n";
echo " 货源商: {$stats['success']}/{$stats['total']} 成功 | 处理: {$stats['processed']} 个\n";

if (!empty($updates)) {
    $total_updates = count(array_filter($updates, fn($u) => $u['action'] == 'update'));
    $total_online = count(array_filter($updates, fn($u) => $u['action'] == 'online'));
    $total_offline = count(array_filter($updates, fn($u) => $u['action'] == 'offline'));
    $total_create = count(array_filter($updates, fn($u) => $u['action'] == 'create_and_online'));
    echo " 操作: 更新{$total_updates} 上架{$total_online} 下架{$total_offline} 新增{$total_create}\n";
}

// 清理
if (isset($redis)) $redis->close();
unset($updates, $localProducts);
gc_collect_cycles();

echo "========================================\n";
?>
